import {
    checkData,
    execQuery,
    baseResponse,
    insert,
    update,
    dbQuery,
    formataHora,
    plantaoPorSituacao,
    calculaValorFechamento,
    calcularQtRealizadasEaprovadas,
    deleteEntity,
} from 'capfunctions';

import { _listChecks } from './methods/listChecks.mjs';
import { _listJustificativas } from './methods/listJustificativas.mjs';
import { _updateCheckAprovados } from './methods/updateCheckAprovados.mjs';
import { _enviarCheckAprovadosParaFechamento } from './methods/enviarCheckAprovadosParaFechamento.mjs';

async function _listcli(event) {
    console.log(event);
    try {
        if (
            checkData(event, {
                obrigatorios: ['psCPF'],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const query = `SELECT pc.*, c.clCliente, c.clNomeCliente
        FROM capProfSaudeCliente pc  INNER JOIN capCliente c ON pc.clCliente = c.clCliente
                  WHERE pc.psCPF = ?
                `;

        const params = [event.psCPF];
        const response = await execQuery(query, params);
        console.log('response', response);
        if (response.success) {
            return baseResponse.created(
                'Listado com sucesso',
                response.results
            );
        }
        return baseResponse.error('Erro ao listar clientes', response);
    } catch (error) {
        console.log('ERROR _listcli', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _listatendps(event) {
    console.log(event);

    let filter = '';
    if (process.env.IS_LOCAL !== "true") {
        filter += ` AND t.opPeriodoFim >= CURRENT_TIMESTAMP`;
    }

    try {
        if (
            checkData(event, {
                obrigatorios: ['psCPF'],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        let query = '';

        const situacao = `%${event.opSituacao || ''}%`;

        if (event.opSituacao === 'Aberto') {
            query = /*sql*/ `
        SELECT t.*, 
            IF(
                EXISTS (
                    SELECT 1 
                    FROM capInstSaudePlantaoSolicitacao ps 
                    WHERE ps.opNrPlantao = t.opNrPlantao
                        AND ps.psCPF = p.psCPF
                        AND ps.psSituacao = 'Solicitado'
                ), 1, 0
            ) as solicitado
        FROM 
            capInstSaudePlantao t, 
            capProfSaudeCliente p, 
            capCliente c, 
            capInstSaudeContrato i, 
            capInstSaudeContratoEspec e, 
            capProfSaudeEspecialidade a
        WHERE 
            p.psCPF = ?
            AND t.isInstSaude = i.isInstSaude
            AND t.opSituacao like ?
            AND t.esEspecialidade = a.esEspecialidade
            AND t.clCliente = p.clCliente
            AND p.clCliente = c.clCliente
            AND c.clCliente = i.clCliente
            AND i.isInstSaude = e.isInstSaude
            AND i.ocNrContrato = e.ocNrContrato
            AND i.clCliente = e.clCliente
            AND a.psCPF = p.psCPF
            AND e.esEspecialidade = a.esEspecialidade
            AND NOT EXISTS (
                SELECT 1 
                FROM capInstSaudePlantaoSolicitacao psol 
                WHERE psol.isInstSaude = t.isInstSaude
                    AND psol.laNome = t.laNome
                    AND psol.esEspecialidade = t.esEspecialidade
                    AND psol.ocNrContrato = t.ocNrContrato
                    AND psol.clCliente = t.clCliente
                    AND psol.opNrPlantao = t.opNrPlantao
            )
            ${filter}
          GROUP BY  t.clCliente, t.isInstSaude, t.ocNrContrato, t.psCPF, t.opNrPlantao    
          ORDER BY t.opNrPlantao DESC;
      `;
        } else if (event.opSituacao === 'Solicitado') {
            query = /*sql*/ `
        SELECT p.*, s.psDataSolicitacao
            FROM capInstSaudePlantaoSolicitacao s
            INNER JOIN capInstSaudePlantao p 
        ON p.opNrPlantao = s.opNrPlantao 
                  where s.psCPF = ?
                  and   s.psSituacao like ?
                  and p.opSituacao = "Solicitado"
        ORDER BY p.opNrPlantao DESC
      `;
        } else {
            query = /*sql*/ `
        SELECT t.*, 
          s.psDataSolicitacao,
          s.psSequenciaSolicitacao, 
          sa.dtModificacao,
          CASE WHEN NOT exists (
            SELECT * FROM capOperPlantaoCheck pc2 WHERE pc2.isInstSaude = i.isInstSaude 
							AND pc2.esEspecialidade = a.esEspecialidade 
							AND pc2.clCliente = p.clCliente 
							AND pc2.laNome = t.laNome 
							AND pc2.ocNrContrato = t.ocNrContrato 
							AND pc2.psCPF = p.psCPF 
							AND pc2.opNrPlantao = t.opNrPlantao
          ) THEN true ELSE (
            SELECT IF(MAX(pc.ocCheckin) < IF (MAX(pc.ocCheckout) IS NULL, '2023-01-01 00:00:00', MAX(pc.ocCheckout)), true, false) 
							FROM capOperPlantaoCheck pc 
							WHERE pc.isInstSaude = i.isInstSaude 
							AND pc.esEspecialidade = a.esEspecialidade 
							AND pc.clCliente = p.clCliente 
							AND pc.laNome = t.laNome 
							AND pc.ocNrContrato = t.ocNrContrato 
							AND pc.psCPF = p.psCPF 
							AND pc.opNrPlantao = t.opNrPlantao
          ) END AS checkIn,
          (
            SELECT ap.adDataSolicitacao 
                   FROM capAditivoProfSaude ap 
                   WHERE ap.isInstSaude = i.isInstSaude 
                   AND ap.esEspecialidade = a.esEspecialidade 
                   AND ap.clCliente = p.clCliente 
                   AND ap.laNome = t.laNome 
                   AND ap.ocNrContrato = t.ocNrContrato 
                   AND ap.psCPF = p.psCPF 
                   AND ap.opNrPlantao = t.opNrPlantao LIMIT 1
          )  AS adDataSolicitacao
          FROM capInstSaudePlantao t, 
               capProfSaudeCliente p, 
               capCliente c, 
               capInstSaudeContrato i, 
               capInstSaudeContratoEspec e,
               capProfSaudeEspecialidade a, 
               capInstSaudePlantaoSolicitacao s,
               capInstSaudePlSolicitAprovado sa
          WHERE p.psCPF = ?
            AND t.psCPF = p.psCPF
            AND t.isInstSaude = i.isInstSaude
            AND t.esEspecialidade = a.esEspecialidade
            AND t.clCliente = p.clCliente
            AND t.opSituacao like ?
            AND s.isInstSaude = i.isInstSaude
            AND s.laNome = t.laNome
            AND s.esEspecialidade = t.esEspecialidade
            AND s.ocNrContrato = t.ocNrContrato
            AND s.clCliente = t.clCliente
            AND s.ocNrContrato = t.ocNrContrato
            AND s.opNrPlantao = t.opNrPlantao
            AND s.psCPF = p.psCPF
            AND sa.isInstSaude = i.isInstSaude
            AND sa.laNome = t.laNome
            AND sa.esEspecialidade = t.esEspecialidade
            AND sa.ocNrContrato = t.ocNrContrato
            AND sa.clCliente = t.clCliente
            AND sa.ocNrContrato = t.ocNrContrato
            AND sa.opNrPlantao = t.opNrPlantao
            AND sa.psCPF = p.psCPF
            AND t.esEspecialidade = a.esEspecialidade
            AND t.clCliente = p.clCliente
            AND p.clCliente = c.clCliente
            AND c.clCliente = i.clCliente
            AND i.isInstSaude = e.isInstSaude
            AND i.ocNrContrato = e.ocNrContrato
            AND i.clCliente = e.clCliente
            AND a.psCPF = p.psCPF
            AND e.esEspecialidade = a.esEspecialidade
          GROUP BY t.clCliente, t.isInstSaude, t.ocNrContrato, t.psCPF, t.opNrPlantao
          ORDER BY t.opNrPlantao DESC
          ;
      `;
        }

        const params = [event.psCPF, situacao];

        const response = await execQuery(query, params);
        console.log('response', response);
        if (response.success) {
            return baseResponse.created(
                'Listado com sucesso',
                response.results
            );
        }
        return baseResponse.error(
            'Ocorreu um erro ao listar atendimentos',
            response
        );
    } catch (error) {
        console.log('ERROR _listatendps', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _inssolicitatend(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'psCPF',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const entity = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            psCPF: event.psCPF,
            opNrPlantao: event.opNrPlantao,
            psSituacao: 'Solicitado',
        };
        //      dtInclusao: 'current_timestamp'
        console.log('_inssolicitatend entity', entity);
        console.log('_inssolicitatend event', event);

        const response = await insert(
            'capInstSaudePlantaoSolicitacao',
            entity,
            event
        );

        if (response.success) {
            const entityPlantao = {
                opSituacao: 'Solicitado',
            };

            const wherePlantao = {
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                ocNrContrato: event.ocNrContrato,
                opNrPlantao: event.opNrPlantao,
                clCliente: event.clCliente,
            };

            const responsePlantao = await update(
                'capInstSaudePlantao',
                entityPlantao,
                wherePlantao,
                event
            );

            if (!responsePlantao.success) {
                return baseResponse.error('Erro ao atualizar o plantao');
            }

            return baseResponse.created('Solicitação criada com sucesso');
        }

        return baseResponse.notFound(response);
    } catch (error) {
        console.log('ERROR _inssolicitatend', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _confsituacao(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'psCPF',
                    'opSituacao',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const updSit = {
            opSituacao: event.opSituacao,
        };

        const whereSit = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            opNrPlantao: event.opNrPlantao,
        };

        const response = await update(
            'capInstSaudePlantao',
            updSit,
            whereSit,
            event
        );

        if (response.success) {
            return baseResponse.created('Criado com successo');
        }

        return baseResponse.notFound('Nada encontrado');
    } catch (error) {
        console.log('ERROR _confsituacao', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _updtChecks(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'psSequenciaSolicitacao',
                    'ocCheckIn',
                    'ocQtRealizadas',
                    'ocQtAprovadas',
                    'ocQtGlosadas',
                    'checkParaAlterar',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        if (event.ocCheckIn && event.ocCheckOut) {
            if (new Date(event.ocCheckOut) < new Date(event.ocCheckIn)) {
                return baseResponse.badRequest(
                    'Checkin não pode ser menor que o Checkout'
                );
            }
        }

        const { qtRealizadas, qtAprovadas } = event.ocCheckOut
          ? await calcularQtRealizadasEaprovadas(
              formataHora(event.ocCheckIn),
              formataHora(event.ocCheckOut),
              formataHora(event.ocQtGlosadas),
              formataHora(event.ocIntervalo)
            )
          : { qtRealizadas: '00:00:00', qtAprovadas: '00:00:00' };

        const updck = {
            ocQtAprovadas: qtAprovadas,
            ocQtGlosadas: formataHora(event.ocQtGlosadas),
            ocQtRealizadas: qtRealizadas,
            pcJustificativa: null,
            ocCheckIn: formataHora(event.ocCheckIn),
            ocCheckOut: event.ocCheckOut ? formataHora(event.ocCheckOut) : null,
            ocIntervalo: event.ocIntervalo ? formataHora(event.ocIntervalo) : '00:00:00',
        };

        if (!!event?.pcJustificativa) {
            updck.pcJustificativa = event.pcJustificativa;
        }
        
        if (!!event?.ocCheckAprovado) {
            updck.ocCheckAprovado = event.ocCheckAprovado ? 1 : 0;
        }

        const whereck = {
            isInstSaude: event.checkParaAlterar.isInstSaude,
            laNome: event.checkParaAlterar.laNome,
            esEspecialidade: event.checkParaAlterar.esEspecialidade,
            ocNrContrato: event.checkParaAlterar.ocNrContrato,
            clCliente: event.checkParaAlterar.clCliente,
            opNrPlantao: event.checkParaAlterar.opNrPlantao,
            ocCheckIn: formataHora(event.checkParaAlterar.ocCheckIn),
            ceTipoPagamento: event.checkParaAlterar.ceTipoPagamento,
        };

        delete event.checkParaAlterar.hrsTrabalhadas;

        const respupdck = await update(
            'capOperPlantaoCheck',
            updck,
            whereck,
            event
        );

        if (respupdck.success) {
            if (event?.codFechamento) {
                await calculaValorFechamento({
                    psCPF: event.psCPF,
                    codFechamento: event?.codFechamento,
                    isInstSaude: event.checkParaAlterar.isInstSaude,
                    laNome: event.checkParaAlterar.laNome,
                    esEspecialidade: event.checkParaAlterar.esEspecialidade,
                    ocNrContrato: event.checkParaAlterar.ocNrContrato,
                    clCliente: event.checkParaAlterar.clCliente,
                    opNrPlantao: event.checkParaAlterar.opNrPlantao,
                    ceTipoPagamento: event.checkParaAlterar.ceTipoPagamento,
                });
            }

            return baseResponse.ok('Ok');
        } else {
            return baseResponse.badRequest(
                'Erro ao fazer update de horas no Checkin!'
            );
        }
    } catch (error) {
        console.log('ERROR _updtchecks', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _inserCheck(event) {
    /**
     * Feito via operador
     */
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'psCPF',
                    'psSequenciaSolicitacao',
                    'ocCheckIn',
                    // "ocCheckOut",
                    // "ocQtRealizadas",
                    'ocQtAprovadas',
                    'ocQtGlosadas',
                    'ceTipoPagamento',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        if (await plantaoPorSituacao(event.opNrPlantao, 'Concluido')) {
            return baseResponse.badRequest('Plantão já concluído.');
        }

        const [dentroDoLimiteCheckin] = await dbQuery(
            /*sql*/ `
                SELECT a.agData
                FROM capInstSaudePlantaoAgenda a, capInstSaudePlantao p
                WHERE p.psCPF = ?
                AND a.isInstSaude = ? AND a.laNome = ? AND a.esEspecialidade = ? AND a.ocNrContrato = ? AND a.clCliente = ? AND a.opNrPlantao = ? AND DATE(agData) = DATE(?)
                AND a.isInstSaude = p.isInstSaude
                AND a.laNome = p.laNome
                AND a.esEspecialidade = p.esEspecialidade
                AND a.ocNrContrato = p.ocNrContrato
                AND a.clCliente = p.clCliente
                AND a.opNrPlantao = p.opNrPlantao
                AND a.ceTipoPagamento = p.ceTipoPagamento
            `,
            [
                event.psCPF,
                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.opNrPlantao,
                formataHora(event.ocCheckIn),
            ]
        );

        if (!dentroDoLimiteCheckin) {
            return baseResponse.badRequest(
                'Checkin fora do horário de plantão'
            );
        }

        if (event.ocCheckIn && event.ocCheckOut) {
            const [[ultimoCheckout], [noFuturo]] = await dbQuery(
                /*sql*/ `
              SELECT DATE_FORMAT(ocCheckOut, '%d/%m/%Y %H:%i:%s') as dataConflito FROM capOperPlantaoCheck
              WHERE DATE(ocCheckOut) = DATE(?) AND ocCheckOut >= ?
              AND isInstSaude = ? AND laNome = ? AND esEspecialidade = ? AND ocNrContrato = ?
              AND clCliente = ? AND opNrPlantao = ? AND ceTipoPagamento = ? AND agData = ?
              ORDER BY ocCheckOut DESC LIMIT 1;
  
              SELECT 1 WHERE CURRENT_TIMESTAMP < ? OR CURRENT_TIMESTAMP < ?;
          `,
                [
                    formataHora(event.ocCheckIn),
                    formataHora(event.ocCheckIn),
                    event.isInstSaude,
                    event.laNome,
                    event.esEspecialidade,
                    event.ocNrContrato,
                    event.clCliente,
                    event.opNrPlantao,
                    event.ceTipoPagamento,
                    dentroDoLimiteCheckin.agData,
                    formataHora(event.ocCheckIn),
                    formataHora(event.ocCheckOut),
                ]
            );

            if (ultimoCheckout) {
                return baseResponse.badRequest(
                    `Checkin não pode ser feito antes do último checkout ${ultimoCheckout.dataConflito}`,
                    { ...ultimoCheckout }
                );
            }

            if (noFuturo) {
                return baseResponse.badRequest(
                    `Checkin ou Checkout não podem ser feitos no futuro`
                );
            }
        }

        const updck = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            opNrPlantao: event.opNrPlantao,
            ocCheckIn: formataHora(event.ocCheckIn),
            ocQtAprovadas: formataHora(event?.ocQtAprovadas),
            ocQtGlosadas: formataHora(event?.ocQtGlosadas),
            ocQtRealizadas: formataHora(event?.ocQtRealizadas) ?? '00:00:00',
            pcJustificativa: event?.pcJustificativa || null,
            ceTipoPagamento: event.ceTipoPagamento,
            agData: dentroDoLimiteCheckin.agData,
            ocCheckAprovado: event?.ocCheckAprovado ? 1 : 0,
            ocIntervalo: event.ocIntervalo ? formataHora(event.ocIntervalo) : '00:00:00',
        };

        if (event?.ocCheckOut) {
            updck.ocCheckOut = formataHora(event.ocCheckOut);
        }

        console.log('_inserCheck updck', JSON.stringify({ updck, event }));

        const respupdck = await insert('capOperPlantaoCheck', updck, event);
        console.log('respupdck', respupdck);

        if (respupdck.success) {
            const atendimentoResponse = await execQuery(
                /*sql*/`
                    SELECT * from capInstSaudePlantao 
                    WHERE isInstSaude = ?
                    AND laNome = ?
                    AND esEspecialidade = ?
                    AND ocNrContrato = ?
                    AND clCliente = ?
                    AND opNrPlantao = ?
                `,
                [
                    event.isInstSaude,
                    event.laNome,
                    event.esEspecialidade,
                    event.ocNrContrato,
                    event.clCliente,
                    event.opNrPlantao,
                ]
            );

            const atendimento = atendimentoResponse.results[0];

            if (atendimento.opSituacao === 'AguardExecucao') {
                const infosAtendimento = {
                    isInstSaude: event.isInstSaude,
                    laNome: event.laNome,
                    esEspecialidade: event.esEspecialidade,
                    ocNrContrato: event.ocNrContrato,
                    clCliente: event.clCliente,
                    opNrPlantao: event.opNrPlantao,
                };

                await update(
                    'capInstSaudePlantao',
                    {
                        opSituacao: 'EmExecucao',
                    },
                    infosAtendimento,
                    event
                );
            }

            return baseResponse.ok('Checkado com sucesso!', respupdck);
        } else {
            return baseResponse.badRequest('Erro ao fazer check!');
        }
    } catch (error) {
        console.log('ERROR _inserchecks', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _deleteCheck(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: ['check'],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        if (
            checkData(event.check, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'ocCheckIn',
                    'ceTipoPagamento',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const whereck = {
            isInstSaude: event.check.isInstSaude,
            laNome: event.check.laNome,
            esEspecialidade: event.check.esEspecialidade,
            ocNrContrato: event.check.ocNrContrato,
            clCliente: event.check.clCliente,
            opNrPlantao: event.check.opNrPlantao,
            ocCheckIn: formataHora(event.check.ocCheckIn),
            ceTipoPagamento: event.check.ceTipoPagamento,
            codFechamento: event.check?.codFechamento,
        };

        const respupdck = await deleteEntity(
            'capOperPlantaoCheck',
            whereck,
            event
        );

        if (whereck.codFechamento) {
            const [existsAnyCheck] = await dbQuery(
                /*sql*/ `
                    SELECT 1 FROM capOperPlantaoCheck
                    WHERE codFechamento = ?
                `,
                [
                    whereck.codFechamento
                ]
            );

            if (!existsAnyCheck) {
                await deleteEntity(
                    'capAtendimentoFechamento',
                    { codFechamento: whereck.codFechamento },
                    event
                );
            }
        }

        if (respupdck.success) {
            return baseResponse.ok('', respupdck);
        }

        return baseResponse.badRequest('Erro ao fazer check!');
    } catch (error) {
        console.log('ERROR _deleteCheck', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

export {
    _listatendps,
    _listcli,
    _inssolicitatend,
    _confsituacao,
    _updtChecks,
    _inserCheck,
    _updateCheckAprovados,
    _enviarCheckAprovadosParaFechamento,
    _listChecks,
    _listJustificativas,
    _deleteCheck,
};
