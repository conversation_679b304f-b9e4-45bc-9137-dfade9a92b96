import { baseResponse } from 'capfunctions';

// Global instances for connection reuse
let database = null;
let stateMachine = null;
let messageBuilder = null;

// Initialize components - lazy loading to avoid import issues
async function initializeComponents() {
  if (!database) {
    const Database = (await import('./database.mjs')).default;
    database = new Database();
    await database.initialize();
  }

  if (!messageBuilder) {
    const MessageBuilder = (await import('./utils/messageBuilder.mjs')).default;
    messageBuilder = new MessageBuilder();
  }

  if (!stateMachine) {
    const { StateMachine } = (await import('./services/stateMachine.mjs'));
    stateMachine = new StateMachine(database, messageBuilder);
  }

  return { database, stateMachine, messageBuilder };
}

/**
 * Process WhatsApp message through the chatbot state machine
 * Handles the complete message processing flow including validation, state transitions, and response generation
 * 
 * @param {Object} params - Message processing parameters
 * @param {string} params.userId - User identifier (typically phone number without formatting)
 * @param {string} params.phoneNumber - User's phone number
 * @param {string} params.message - The message content from WhatsApp
 * @param {Object} params.originalPayload - Original webhook payload for logging
 * @returns {Object} Response object with message and processing status
 */
export const _processMessage = async (params) => {
  try {
    const { userId, phoneNumber, message, originalPayload } = params;

    // Validate required parameters
    if (!userId || !phoneNumber || !message) {
      throw new Error('Missing required parameters: userId, phoneNumber, or message');
    }

    console.log('Processing message:', {
      userId,
      phoneNumber,
      message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
    });

    // Initialize components
    const { stateMachine: sm } = await initializeComponents();

    // Process message through state machine
    const response = await sm.processMessage(userId, message, phoneNumber);

    console.log('State machine response:', {
      nextState: response.nextState,
      messageLength: response.message.length,
    });

    // WhatsApp API requires simple 200 response for message processing
    // Internal processing data is logged but not returned to WhatsApp
    console.log('Message processed successfully:', {
      message: response.message?.substring(0, 100) + '...',
      nextState: response.nextState,
      timestamp: new Date().toISOString(),
    });
    
    return {
      statusCode: 200,
    };

  } catch (error) {
    console.error('Error in _processMessage:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    // WhatsApp API requires 200 status even for errors to prevent message retries
    return {
      statusCode: 200,
    };
  }
};

/**
 * Retrieve user session data from the database
 * Gets current session state, session data, and user progress through the chatbot flow
 * 
 * @param {Object} params - Session retrieval parameters
 * @param {string} params.userId - User identifier to retrieve session for
 * @returns {Object} Response object with session data or null if not found
 */
export const _getSession = async (params) => {
  try {
    const { userId } = params;

    // Validate required parameters
    if (!userId) {
      throw new Error('Missing required parameter: userId');
    }

    console.log('Retrieving session for user:', userId);

    // Initialize components
    const { database: db } = await initializeComponents();

    // Get user session
    const session = await db.getUserSession(userId);

    if (!session) {
      console.log('No session found for user:', userId);
      return baseResponse.ok('Session not found', { session: null });
    }

    console.log('Session retrieved:', {
      userId,
      currentState: session.current_state,
      hasSessionData: !!session.session_data,
    });

    // Return session data
    return baseResponse.ok('Session retrieved successfully', {
      session: {
        id: session.id,
        phoneNumber: session.phone_number,
        currentState: session.current_state,
        sessionData: session.session_data,
        createdAt: session.created_at,
        updatedAt: session.updated_at,
      },
    });

  } catch (error) {
    console.error('Error in _getSession:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    return baseResponse.error('Failed to retrieve session data');
  }
};

/**
 * Persist user information to the database
 * Saves user data including personal information, clinic association, and terms acceptance
 * 
 * @param {Object} params - User data saving parameters
 * @param {string} params.sessionId - Session identifier
 * @param {number} params.clinicId - Clinic ID the user is associated with
 * @param {Object} params.userData - User data object
 * @param {string} params.userData.fullName - User's full name
 * @param {string} params.userData.cpf - User's CPF (Brazilian tax ID)
 * @param {string} params.userData.email - User's email address
 * @param {boolean} params.userData.termsAccepted - Whether user accepted terms
 * @returns {Object} Response object indicating save status
 */
export const _saveUserData = async (params) => {
  try {
    const { sessionId, clinicId, userData } = params;

    // Validate required parameters
    if (!sessionId || !clinicId || !userData) {
      throw new Error('Missing required parameters: sessionId, clinicId, or userData');
    }

    // Validate userData structure
    const { fullName, cpf, email, termsAccepted } = userData;
    if (!fullName || !cpf || !email) {
      throw new Error('Missing required user data fields: fullName, cpf, or email');
    }

    console.log('Saving user data:', {
      sessionId,
      clinicId,
      fullName,
      cpf: cpf.substring(0, 3) + '***', // Mask CPF for logging
      email,
      termsAccepted,
    });

    // Initialize components
    const { database: db } = await initializeComponents();

    // Save user data to database
    await db.saveUserData(sessionId, clinicId, userData);

    console.log('User data saved successfully for session:', sessionId);

    // Return success response
    return baseResponse.ok('User data saved successfully', {
      sessionId,
      clinicId,
      termsAccepted,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in _saveUserData:', {
      error: error.message,
      stack: error.stack,
      params: {
        ...params,
        userData: params.userData ? {
          ...params.userData,
          cpf: params.userData.cpf ? params.userData.cpf.substring(0, 3) + '***' : undefined,
        } : undefined,
      },
    });

    return baseResponse.error('Failed to save user data');
  }
};

/**
 * System health check function
 * Verifies database connectivity and overall system health
 * 
 * @param {Object} params - Health check parameters (optional)
 * @returns {Object} Response object with health status
 */
export const _healthCheck = async (params = {}) => {
  try {
    console.log('Performing health check');

    // Initialize components
    const { database: db } = await initializeComponents();

    // Perform database health check
    const dbHealth = await db.healthCheck();

    // Check component initialization
    const componentsHealth = {
      database: !!database,
      stateMachine: !!stateMachine,
      messageBuilder: !!messageBuilder,
    };

    const overallHealth = dbHealth.status === 'healthy' && 
                         componentsHealth.database && 
                         componentsHealth.stateMachine && 
                         componentsHealth.messageBuilder;

    const healthData = {
      status: overallHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      database: dbHealth,
      components: componentsHealth,
      version: '1.0.0',
      service: 'wpphook-chatbot',
    };

    console.log('Health check completed:', {
      status: healthData.status,
      dbStatus: dbHealth.status,
    });

    if (overallHealth) {
      return baseResponse.ok('System is healthy', healthData);
    } else {
      return baseResponse.error('System health check failed', healthData);
    }

  } catch (error) {
    console.error('Error in _healthCheck:', {
      error: error.message,
      stack: error.stack,
    });

    const errorHealthData = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      service: 'wpphook-chatbot',
    };

    return baseResponse.error('Health check failed', errorHealthData);
  }
};

// Export cleanup function for Lambda container reuse
export const cleanup = async () => {
  try {
    if (database) {
      // Don't close the connection pool in Lambda - reuse it
      console.log('Database connection pool maintained for reuse');
    }
  } catch (error) {
    console.error('Cleanup error:', error);
  }
};

/**
 * Session management function - cleanup expired sessions
 * Removes sessions that are older than the configured timeout period
 * 
 * @param {Object} params - Session cleanup parameters
 * @param {number} params.hoursOld - Optional hours threshold (default: 24)
 * @returns {Object} Response object with cleanup results
 */
export const _cleanupSessions = async (params = {}) => {
  try {
    const { hoursOld = 24 } = params;
    
    console.log('Starting session cleanup', { hoursOld });

    // Initialize components
    const { stateMachine: sm } = await initializeComponents();

    // Perform cleanup
    const cleanedCount = await sm.cleanupExpiredSessions();

    console.log('Session cleanup completed', { cleanedCount });

    return baseResponse.ok('Session cleanup completed', {
      cleanedSessions: cleanedCount,
      hoursOld,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in _cleanupSessions:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    return baseResponse.error('Session cleanup failed');
  }
};

/**
 * Get session statistics for monitoring and analytics
 * Returns information about active sessions and their states
 * 
 * @param {Object} params - Session stats parameters (optional)
 * @returns {Object} Response object with session statistics
 */
export const _getSessionStats = async (params = {}) => {
  try {
    console.log('Retrieving session statistics');

    // Initialize components
    const { stateMachine: sm } = await initializeComponents();

    // Get session stats
    const stats = await sm.getSessionStats();

    console.log('Session statistics retrieved', { statCount: stats.length });

    return baseResponse.ok('Session statistics retrieved', {
      stats,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in _getSessionStats:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    return baseResponse.error('Failed to retrieve session statistics');
  }
};

/**
 * Get user interaction journey and history
 * Returns complete user session and interaction history for analysis
 * 
 * @param {Object} params - User journey parameters
 * @param {string} params.userId - User identifier
 * @returns {Object} Response object with user journey data
 */
export const _getUserJourney = async (params) => {
  try {
    const { userId } = params;

    if (!userId) {
      throw new Error('Missing required parameter: userId');
    }

    console.log('Retrieving user journey for:', userId);

    // Initialize components
    const { stateMachine: sm } = await initializeComponents();

    // Get user journey
    const journey = await sm.getUserJourney(userId);

    if (!journey) {
      console.log('No journey found for user:', userId);
      return baseResponse.ok('User journey not found', { journey: null });
    }

    console.log('User journey retrieved:', {
      userId,
      totalInteractions: journey.totalInteractions,
      currentState: journey.session?.current_state,
    });

    return baseResponse.ok('User journey retrieved successfully', {
      journey: {
        session: journey.session,
        interactions: journey.interactions,
        totalInteractions: journey.totalInteractions,
      },
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Error in _getUserJourney:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    return baseResponse.error('Failed to retrieve user journey');
  }
};

/**
 * Reset user session - useful for customer support or testing
 * Deletes the user's current session, allowing them to start fresh
 * 
 * @param {Object} params - Session reset parameters
 * @param {string} params.userId - User identifier
 * @param {string} params.reason - Optional reason for reset
 * @returns {Object} Response object indicating reset status
 */
export const _resetUserSession = async (params) => {
  try {
    const { userId, reason = 'manual_reset' } = params;

    if (!userId) {
      throw new Error('Missing required parameter: userId');
    }

    console.log('Resetting session for user:', { userId, reason });

    // Initialize components
    const { stateMachine: sm } = await initializeComponents();

    // Reset session
    const success = await sm.resetSession(userId, reason);

    if (success) {
      console.log('Session reset successful for user:', userId);
      return baseResponse.ok('Session reset successful', {
        userId,
        reason,
        timestamp: new Date().toISOString(),
      });
    } else {
      console.error('Session reset failed for user:', userId);
      return baseResponse.error('Session reset failed');
    }

  } catch (error) {
    console.error('Error in _resetUserSession:', {
      error: error.message,
      stack: error.stack,
      params,
    });

    return baseResponse.error('Session reset failed');
  }
};

// Export initialization function for testing
export const initializeForTesting = async () => {
  return await initializeComponents();
};