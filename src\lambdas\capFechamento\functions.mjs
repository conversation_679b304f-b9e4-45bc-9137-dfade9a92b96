import {
    checkData,
    execQuery,
    baseResponse,
    dbQuery,
    insert,
    update,
    lambdaInvoke,
} from 'capfunctions';

import moment from 'moment';
import { calculaFechamento } from './calculaFechamento.mjs';
import { _listFechamentos } from './methods/listFechamentos.mjs';

async function _antecipaAtendimento(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'ceTipoPagamento',
                    'fechamentos',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const codigos = event.fechamentos.map((f) => f.codFechamento);

        event.psCPF = event.fechamentos[0].psCPF;

        const fechamentos = await buscaFechamentos(
            {...event, opNrPlantao: null},
            'aprovado',
            codigos
        );

        if (!fechamentos.length) {
            return baseResponse.notFound('Não foi possível encontrar os fechamentos.');
        }

        // const plantoesFechamentos = [...new Set(fechamentos.map(f => f.opNrPlantao))];

        const antecipacao = await calculaFechamento({
            event,
            fechamentos,
        });

        const antecipacaoEnviada = await enviaAntecipacaoParaProcessamento(antecipacao);

        return baseResponse.created('Antecipação criada', antecipacaoEnviada);
    } catch (err) {
        console.log('Erro', err);

        return baseResponse.error('Erro ao processar requisição');
    }
}

async function existeAntecipacaoMensal(event) {
    const query = /*sql*/ `
        SELECT COUNT(*) AS count
        FROM
            capAtendimentoFechamento
        WHERE 
            anNrAntecipacao is not null
        AND MONTH(afFim) = MONTH(CURDATE())
        AND YEAR(afFim) = YEAR(CURDATE())
        AND isInstSaude = ?
        AND laNome = ?
        AND esEspecialidade = ?
        AND ocNrContrato = ?
        AND clCliente = ?
        AND opNrPlantao = ?
        AND ceTipoPagamento = ?
    `;

    const [exists] = await dbQuery(query, [
        event.isInstSaude,
        event.laNome,
        event.esEspecialidade,
        event.ocNrContrato,
        event.clCliente,
        event.opNrPlantao,
        event.ceTipoPagamento,
    ]);

    return exists;
}

export async function buscaAtendimento(event) {
    const [result] = await dbQuery(
        /*sql*/ `
            SELECT * from capInstSaudePlantao 
            WHERE isInstSaude = ?
            AND laNome = ?
            AND esEspecialidade = ?
            AND ocNrContrato = ?
            AND clCliente = ?
            AND opNrPlantao = ?
        `,
        [
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
        ]
    );

    return result;
}

async function buscaFechamentos(event, situacao, codFechamento = null) {
    let query = /*sql*/ `
        SELECT * from
            capAtendimentoFechamento 
        WHERE 
            adSituacao = ?
    `;
    
    let params = [situacao];
    
    // Se temos códigos de fechamento específicos, usamos eles diretamente
    if (Array.isArray(codFechamento) && codFechamento.length > 0) {
        query += ` AND codFechamento IN (?)`;
        params.push(codFechamento);
    } else {
        // Caso contrário, usamos os filtros tradicionais
        query += ` AND 
            isInstSaude = ? AND 
            laNome = ? AND 
            esEspecialidade = ? AND 
            ocNrContrato = ? AND 
            clCliente = ? AND 
            ceTipoPagamento = ?`;
        
        params = params.concat([
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.ceTipoPagamento,
        ]);
        
        // Se não estamos buscando pelo código do fechamento, usamos o número do plantão
        if (event.opNrPlantao) {
            query += ` AND opNrPlantao = ?`;
            params.push(event.opNrPlantao);
        }
    }

    const result = await dbQuery(query, params);
    return result;
}

async function _preVisualizacaoAntecipacao(event) {
    try {
        const atendimento = await buscaAtendimento(event);
        const fechamentos = await buscaFechamentos(event, 'aprovado');
        if (fechamentos.length > 0) {
            const infos = await calculaFechamentoV2({
                event,
                atendimento,
                fechamentos,
            });
            return baseResponse.created('Criado com sucesso', infos);
        }

        return baseResponse.notFound(
            'Não existem fechamentos em aberto para esse atendimento'
        );
    } catch (err) {
        console.log(err);
        return baseResponse.error(err);
    }
}

async function _visualizarValores(event) {
    const [
        atendimento,
        fechamentosAguardandoAprovacao,
        fechamentosAprovados,
        fechamentoAguardandoAntecipacao,
        fechamentosAntecipados,
    ] = await Promise.all([
        buscaAtendimento(event),
        buscaFechamentos(event, 'aguardandoAprovacao'),
        buscaFechamentos(event, 'aprovado'),
        buscaFechamentos(event, 'antecipacaoSolicitada'),
        buscaFechamentos(event, 'antecipado'),
    ]);

    const [aguardandoAprovacao, aprovados, aguardandoAntecipacao, antecipado] =
        await Promise.all([
            calculaFechamentoV2({
                event,
                atendimento,
                fechamentos: fechamentosAguardandoAprovacao,
            }),
            calculaFechamentoV2({
                event,
                atendimento,
                fechamentos: fechamentosAprovados,
            }),
            calculaFechamentoV2({
                event,
                atendimento,
                fechamentos: fechamentoAguardandoAntecipacao,
            }),
            calculaFechamentoV2({
                event,
                atendimento,
                fechamentos: fechamentosAntecipados,
            }),
        ]);

    return baseResponse.ok('Listado com sucesso', {
        aguardandoAprovacao: aguardandoAprovacao.valorSemDesagio,
        aprovado: aprovados.valorSemDesagio,
        aguardandoAntecipacao: aguardandoAntecipacao.valorSemDesagio,
        antecipado: antecipado.valorSemDesagio,
    });
}

async function calculaFechamentoV2({ event, atendimento, fechamentos }) {
    // nao esta certo isso

    if (!fechamentos?.length) {
        return {
            valorComDesagio: 0,
            valorSemDesagio: 0,
        };
    }

    var total = 0;
    var valorBrutoTotal = 0;
    let horasTotal = 0;
    let minutosTotal = 0;
    let valorBrutoDoPlantao = 0;

    const dadosQuery = [
        event.isInstSaude,
        event.laNome,
        event.esEspecialidade,
        event.ocNrContrato,
        event.clCliente,
        event.ceTipoPagamento,
    ];

    const checksWhere = fechamentos
        .map((fechamento) => {
            return /*sql*/ `(isInstSaude = "${fechamento.isInstSaude}" AND 
        laNome ="${fechamento.laNome}" AND 
        esEspecialidade ="${fechamento.esEspecialidade}" AND 
        ocNrContrato = "${fechamento.ocNrContrato}" AND 
        clCliente ="${fechamento.clCliente}" AND 
        opNrPlantao = "${fechamento.opNrPlantao}" AND 
        codFechamento = "${fechamento.codFechamento}" AND
        ceTipoPagamento = "${fechamento.ceTipoPagamento}") 
        `;
        })
        .join(' OR ');

    const checkQuery = `SELECT * from capOperPlantaoCheck WHERE (${checksWhere})`;

    const dadosContratoEspec = execQuery(
        /*sql*/ ` SELECT *
        FROM capInstSaudeContratoEspec
       WHERE isInstSaude = ?
         AND laNome = ?
         AND esEspecialidade = ?
         AND ocNrContrato = ?
         AND clCliente = ?
         AND ceTipoPagamento = ?`,
        dadosQuery
    );

    const checkExec = execQuery(checkQuery);

    const [checkResult, contratoEspecResult] = await Promise.all([
        checkExec,
        dadosContratoEspec,
    ]);

    const taxaDeDesagio = contratoEspecResult.results[0]?.ceTaxaDesagio || 0.06;
    const dias = contratoEspecResult.results[0]?.ceQtdDiasMinRecebim || 30;

    console.log({
        contratoEspecResult: contratoEspecResult.results,
    });

    for (const check of checkResult.results) {
        horasTotal += parseFloat(check.ocQtAprovadas.split(':')[0]);
        minutosTotal += parseFloat(check.ocQtAprovadas.split(':')[1]);
    }

    if (atendimento.ceTipoPagamento === 'Hora') {
        const totalAtendimento =
            parseFloat(horasTotal) + parseFloat(minutosTotal) / 60;
        valorBrutoDoPlantao =
            totalAtendimento * parseFloat(atendimento.opValorHora);
    }

    if (atendimento.ceTipoPagamento === 'Unitario') {
        if (!atendimento.opValorUnit) {
            throw 'Valor unitario do atendimento não encontrado.';
        }

        valorBrutoDoPlantao = atendimento.opValorUnit;
    }

    if (atendimento.ceTipoPagamento === 'Fixo') {
        if (!atendimento.opValorFixo) {
            throw 'Valor fixo do atendimento não encontrado.';
        }

        valorBrutoDoPlantao = atendimento.opValorFixo;
    }

    const taxaDeDesagioAplicada = (1 + taxaDeDesagio) ** (dias / 30);
    const valorComDesagio = valorBrutoDoPlantao / taxaDeDesagioAplicada;

    total = valorComDesagio;
    valorBrutoTotal = valorBrutoDoPlantao;
    console.log('valor desagio: ', valorComDesagio);
    console.log('taxa aplicada: ', taxaDeDesagioAplicada);

    const aditivoProfissionalQuery = await execQuery(
        /*sql*/ `
        SELECT * from capAditivoProfSaude 
        WHERE isInstSaude = ? AND
          laNome = ? AND
          esEspecialidade = ? AND
          ocNrContrato = ? AND
          clCliente = ? AND
          opNrPlantao = ? AND
          psCPF = ?`,
        [
            atendimento.isInstSaude,
            atendimento.laNome,
            atendimento.esEspecialidade,
            atendimento.ocNrContrato,
            atendimento.clCliente,
            atendimento.opNrPlantao,
            atendimento.psCPF,
        ]
    );
    const aditivoProfissional = aditivoProfissionalQuery.results[0];

    return {
        valorComDesagio: (Math.round(total * 100) / 100).toFixed(2),
        valorSemDesagio: (Math.round(valorBrutoTotal * 100) / 100).toFixed(2),
        taxaDeDesagio,
        taxaDeDesagioAplicada: taxaDeDesagioAplicada?.toFixed(2) || 0,
        horasAprovadas: `${horasTotal}:${
            minutosTotal >= 10 ? minutosTotal : '0' + minutosTotal
        }`,
        valorHora: contratoEspecResult?.results[0]?.ceValorHoraProf,
        valorFixo: atendimento.opValorFixo,
        clCliente: atendimento.clCliente,
        ceTipoPagamento: atendimento.ceTipoPagamento,
        dataInicial: 'TODO: Pegar data mais antiga dos fechamentos',
        dataFinal: moment().utcOffset(-180).format('YYYY-MM-DD hh:mm:ss'),
        adDataSolicitacao: aditivoProfissional.adDataSolicitacao,
        psCPF: atendimento.psCPF,
        laNome: atendimento.laNome,
        esEspecialidade: atendimento.esEspecialidade,
        ocNrContrato: atendimento.ocNrContrato,
        opNrPlantao: atendimento.opNrPlantao,
        isInstSaude: atendimento.isInstSaude,
        fechamentos: fechamentos.map((fechamento) => ({
            afNome: fechamento.afNome,
            afIni: fechamento.afIni,
            afFim: fechamento.afFim,
            codFechamento: fechamento.codFechamento,
        })),
    };
}

export async function criaFechamento({ event, ultimoFechamento }) {
    const atendimento = await buscaAtendimento(event);

    const dataInicial = atendimento.opPeriodoIni;
    const [{ dataFinal }] = await dbQuery(
        /*sql*/ `SELECT DATE_FORMAT(CURRENT_TIMESTAMP, '%Y-%m-%d %H:%i:%s') as dataFinal`
    );

    const [fechamentoValido] = await dbQuery(
        /*sql*/ `
        SELECT 1 FROM capOperPlantaoCheck
        WHERE ocCheckOut BETWEEN ? AND ?
        AND isInstSaude = ?
        AND laNome = ?
        AND esEspecialidade = ?
        AND ocNrContrato = ?
        AND clCliente = ?
        AND opNrPlantao = ?
        AND ceTipoPagamento = ?
        AND codFechamento is NULL
        LIMIT 1    
    `,
        [
            dataInicial,
            dataFinal,
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
            event.ceTipoPagamento,
        ]
    );

    if (!fechamentoValido) {
        return baseResponse.badRequest(
            'Não foi possível criar o fechamento, pois não existem checkouts para o período até o momento.'
        );
    }

    const capCodFechamentoResponse = await insert(
        'capCodFechamento',
        {},
        event
    );

    const entityFechamento = {
        isInstSaude: event.isInstSaude,
        laNome: event.laNome,
        esEspecialidade: event.esEspecialidade,
        ocNrContrato: event.ocNrContrato,
        clCliente: event.clCliente,
        opNrPlantao: event.opNrPlantao,
        afNome: 'Fechamento ' + dataFinal,
        afIni: dataInicial,
        afFim: dataFinal,
        codFechamento: capCodFechamentoResponse.results.insertId,
        adSituacao: 'aguardandoAprovacao',
        ceTipoPagamento: event.ceTipoPagamento,
    };

    const response = await insert(
        'capAtendimentoFechamento',
        entityFechamento,
        event
    );

    if (response.success) {
        const query = /*sql*/ `
            UPDATE capOperPlantaoCheck 
            SET 
                codFechamento = ${entityFechamento.codFechamento}
            WHERE
                ocCheckOut BETWEEN  "${entityFechamento.afIni}" AND "${entityFechamento.afFim}"
                AND isInstSaude = "${entityFechamento.isInstSaude}"
                AND laNome = "${entityFechamento.laNome}"
                AND esEspecialidade = "${entityFechamento.esEspecialidade}"
                AND ocNrContrato = "${entityFechamento.ocNrContrato}"
                AND clCliente = "${entityFechamento.clCliente}"
                AND opNrPlantao = "${entityFechamento.opNrPlantao}"
                AND ceTipoPagamento = "${entityFechamento.ceTipoPagamento}"
                AND codFechamento is NULL;
        `;
        await execQuery(query);

        return baseResponse.created('Criado com sucesso');
    }
}

async function _fechamentoParcial(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'opPeriodoIni',
                    'ceTipoPagamento',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const atendimento = await buscaAtendimento(event);

        if (atendimento.opSituacao !== 'EmExecucao') {
            return baseResponse.badRequest(
                'Não é possivel realizar o fechamento de atendimentos que não estão em execução.'
            );
        }

        return await criaFechamento({ event });
    } catch (err) {
        console.log(err);
        return baseResponse.error('Error ao processar requisição');
    }
}

//TODO Remover apos confirmar com o denis se precisa fechar somente a partir da ultima data
async function getUltimoFechamentoDoAtendimento(atendimentoInfo) {
    try {
        const query = `
        SELECT * FROM capAtendimentoFechamento
        WHERE
        isInstSaude = ? AND
        laNome = ? AND
        esEspecialidade = ? AND
        ocNrContrato = ? AND
        clCliente = ? AND 
        opNrPlantao = ? AND
        ceTipoPagamento = ?
        ORDER BY afFim DESC
    `;
        const result = await execQuery(query, [
            atendimentoInfo.isInstSaude,
            atendimentoInfo.laNome,
            atendimentoInfo.esEspecialidade,
            atendimentoInfo.ocNrContrato,
            atendimentoInfo.clCliente,
            atendimentoInfo.opNrPlantao,
            atendimentoInfo.ceTipoPagamento,
        ]);

        return result.results.length > 0 ? result.results[0] : null;
    } catch (err) {
        console.log(err);
        return [];
    }
}

async function _aprovaFechamentos(event) {
    if (
        checkData(event, {
            obrigatorios: ['fechamentos'],
        })
    ) {
        return baseResponse.error('Dados insuficentes');
    }

    try {
        const promisesUpdateFechamentos = event.fechamentos.map(
            (fechamento) => {
                update(
                    'capAtendimentoFechamento',
                    { adSituacao: 'aprovado' },
                    {
                        isInstSaude: fechamento.isInstSaude,
                        laNome: fechamento.laNome,
                        esEspecialidade: fechamento.esEspecialidade,
                        ocNrContrato: fechamento.ocNrContrato,
                        clCliente: fechamento.clCliente,
                        opNrPlantao: fechamento.opNrPlantao,
                        afNome: fechamento.afNome,
                        afIni: fechamento.afIni,
                        codFechamento: fechamento.codFechamento,
                        ceTipoPagamento: fechamento.ceTipoPagamento,
                    },
                    event
                );

                update(
                    'capOperPlantaoCheck',
                    { ocSituacao: 'Fechado' },
                    {
                        isInstSaude: fechamento.isInstSaude,
                        laNome: fechamento.laNome,
                        esEspecialidade: fechamento.esEspecialidade,
                        ocNrContrato: fechamento.ocNrContrato,
                        clCliente: fechamento.clCliente,
                        opNrPlantao: fechamento.opNrPlantao,
                        ceTipoPagamento: fechamento.ceTipoPagamento,
                        codFechamento: fechamento.codFechamento,
                    },
                    event
                );
            }
        );

        await Promise.all(promisesUpdateFechamentos);

        return { success: true };
    } catch (err) {
        console.log('Erro', err);
        throw err;
    }
}

async function enviaAntecipacaoParaProcessamento(antecipacao) {
    try {
        const conditions = antecipacao.fechamentos
            .map((fechamento) => {
                return /*sql*/ `(
                    isInstSaude = "${antecipacao.isInstSaude}" AND 
                    laNome = "${antecipacao.laNome}" AND 
                    esEspecialidade = "${antecipacao.esEspecialidade}" AND 
                    ocNrContrato = "${antecipacao.ocNrContrato}" AND 
                    clCliente = "${antecipacao.clCliente}" AND 
                    opNrPlantao = "${fechamento.opNrPlantao}" AND 
                    afNome = "${fechamento.afNome}" AND 
                    afIni = "${fechamento.afIni}" AND 
                    afFim = "${fechamento.afFim}" AND 
                    codFechamento = "${fechamento.codFechamento}"
                )`;
            })
            .join(' OR ');

        const query = /*sql*/ `
            UPDATE capAtendimentoFechamento
            SET adSituacao = 'processandoAntecipacao'
            WHERE ${conditions};
        `;

        await execQuery(query);

        await lambdaInvoke('capProcessaAntecipacao', {
            Records: [
                {
                    body: JSON.stringify([antecipacao]),
                },
            ],
        });

        return;

        // const sqsClient = new SQSClient({ region: 'us-east-2' });
        // const queueUrl =
        //     'https://sqs.us-east-2.amazonaws.com/************/processaAntecipacoes.fifo';

        // const messageBody = [antecipacao];
        // const MessageGroupId = uuidv4();

        // const paramsfila = {
        //     QueueUrl: queueUrl,
        //     MessageBody: JSON.stringify(messageBody),
        //     MessageGroupId,
        // };

        // console.log('params', paramsfila);

        // const command = new SendMessageCommand(paramsfila);
        // const data = await sqsClient.send(command);

        // console.log('Mensagem enviada com sucesso', data.MessageId);
    } catch (err) {
        console.log('Erro', err);
        throw err;
    }
}

export {
    _fechamentoParcial,
    _listFechamentos,
    _aprovaFechamentos,
    _antecipaAtendimento,
    _preVisualizacaoAntecipacao,
    _visualizarValores,
    calculaFechamentoV2,
    buscaFechamentos
};
