import Joi from 'joi';
import { onlyN<PERSON>ber, isEmailValid } from 'capfunctions';
import { validateEmail as sharedValidateEmail, sanitizeHtml, escapeHtml } from 'capfunctions/validationUtils.mjs';

/**
 * Validators class for wpphook module
 * Integrates with shared validation utilities from capfunctions layer
 * while maintaining wpphook-specific validation logic
 */
export class Validators {
  // CPF validation with algorithm - using shared utility for number extraction
  static validateCPF(cpf) {
    if (!cpf) return false;
    
    // Remove non-numeric characters using shared utility
    cpf = onlyNumber(cpf);
    
    // Check if has 11 digits
    if (cpf.length !== 11) return false;
    
    // Check if all digits are the same
    if (/^(\d)\1{10}$/.test(cpf)) return false;
    
    // Validate first check digit
    let sum = 0;
    for (let i = 0; i < 9; i++) {
      sum += parseInt(cpf.charAt(i)) * (10 - i);
    }
    let remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(9))) return false;
    
    // Validate second check digit
    sum = 0;
    for (let i = 0; i < 10; i++) {
      sum += parseInt(cpf.charAt(i)) * (11 - i);
    }
    remainder = (sum * 10) % 11;
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cpf.charAt(10))) return false;
    
    return true;
  }

  // Email validation - using shared validation utilities with fallback to Joi
  static validateEmail(email) {
    // First try the shared validation utilities
    if (sharedValidateEmail(email) || isEmailValid(email)) {
      return true;
    }
    
    // Fallback to more comprehensive Joi validation for edge cases
    const emailSchema = Joi.string()
      .email({ tlds: { allow: false } })
      .max(255)
      .required();
    
    const { error } = emailSchema.validate(email);
    return !error;
  }

  // Name validation - allowing dots and common titles
  static validateName(name) {
    if (!name || typeof name !== 'string') return false;
    
    const nameSchema = Joi.string()
      .min(2)
      .max(255)
      .pattern(/^[a-zA-ZÀ-ÿ\s\.]+$/)
      .required();
    
    const { error } = nameSchema.validate(name.trim());
    return !error;
  }

  // Phone number validation - using shared utility for number extraction
  static validatePhoneNumber(phone) {
    if (!phone) return false;
    
    // Remove non-numeric characters using shared utility
    const cleanPhone = onlyNumber(phone);
    
    // Brazilian phone numbers: 10-11 digits
    return cleanPhone.length >= 10 && cleanPhone.length <= 11;
  }

  // Input sanitization - using shared utilities with wpphook-specific enhancements
  static sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // First apply shared HTML sanitization
    let sanitized = sanitizeHtml(input);
    
    // Apply wpphook-specific sanitization
    sanitized = sanitized
      .trim()
      .replace(/['"]/g, '') // Remove quotes to prevent injection
      .substring(0, 1000); // Limit length for WhatsApp messages
    
    return sanitized;
  }

  // Enhanced HTML escaping using shared utility
  static escapeInput(input) {
    if (typeof input !== 'string') return input;
    return escapeHtml(input);
  }

  // Sanitize CPF (keep only numbers) - using shared utility
  static sanitizeCPF(cpf) {
    if (!cpf) return '';
    return onlyNumber(cpf);
  }

  // Sanitize phone number - using shared utility
  static sanitizePhoneNumber(phone) {
    if (!phone) return '';
    return onlyNumber(phone);
  }

  // Validate clinic name
  static validateClinicName(name) {
    if (!name || typeof name !== 'string') return false;
    
    const trimmed = name.trim();
    return trimmed.length >= 1 && trimmed.length <= 255;
  }

  // Validate message content
  static validateMessage(message) {
    if (!message || typeof message !== 'string') return false;
    
    const trimmed = message.trim();
    return trimmed.length >= 1 && trimmed.length <= 4096;
  }

  // Check if message is exit command
  static isExitCommand(message) {
    if (!message || typeof message !== 'string') return false;
    
    const exitCommands = ['sair', 'exit', 'quit', 'parar', 'cancelar'];
    const normalized = message.toLowerCase().trim();
    
    return exitCommands.includes(normalized);
  }

  // Check if message is GS2 trigger
  static isGS2Trigger(message) {
    if (!message || typeof message !== 'string') return false;
    
    const normalized = message.toLowerCase().trim();
    
    return normalized === 'gs2' || 
           normalized.includes('aderir aos benefícios') ||
           normalized.includes('capitale holding');
  }

  // Validate user response options - case-insensitive exact match
  static validateUserResponse(message, validOptions) {
    if (!message || !Array.isArray(validOptions)) return false;
    
    // Normalize text for case-insensitive comparison
    const normalizeText = (text) => {
      return text
        .toLowerCase()
        .trim()
        .replace(/\s+/g, ' '); // Normalize multiple spaces to single space
    };
    
    const normalizedMessage = normalizeText(message);
    
    // Debug logging for troubleshooting
    console.debug('Validation Debug:', {
      original: message,
      normalized: normalizedMessage,
      options: validOptions.map(opt => ({
        original: opt,
        normalized: normalizeText(opt)
      }))
    });
    
    return validOptions.some(option => {
      const normalizedOption = normalizeText(option);
      const match = normalizedMessage === normalizedOption;
      if (match) {
        console.debug('Match found:', { message: normalizedMessage, option: normalizedOption });
      }
      
      return match;
    });
  }

  // Format CPF for display - using shared utility for number extraction
  static formatCPF(cpf) {
    if (!cpf) return '';
    const clean = onlyNumber(cpf);
    if (clean.length !== 11) return cpf;
    
    return clean.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  }

  // Format CNPJ for display - using shared utility for number extraction
  static formatCNPJ(cnpj) {
    if (!cnpj) return '';
    const clean = onlyNumber(cnpj);
    if (clean.length !== 14) return cnpj;
    
    return clean.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
  }

  // Comprehensive input validation for user data
  static validateUserData(userData) {
    const errors = [];

    if (!this.validateName(userData.fullName)) {
      errors.push('Nome completo inválido');
    }

    if (!this.validateCPF(userData.cpf)) {
      errors.push('CPF inválido');
    }

    if (!this.validateEmail(userData.email)) {
      errors.push('Email inválido');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}