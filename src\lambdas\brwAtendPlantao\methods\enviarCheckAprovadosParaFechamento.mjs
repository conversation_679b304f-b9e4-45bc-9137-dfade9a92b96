import {
    baseResponse,
    checkData,
    dbQuery,
    insert,
    update,
    formataHora,
    buscaTotalHorasTotalValor,
} from 'capfunctions';

export async function buscaAtendimento(event) {
    const [result] = await dbQuery(
        /*sql*/ `
            SELECT * from capInstSaudePlantao 
            WHERE isInstSaude = ?
            AND laNome = ?
            AND esEspecialidade = ?
            AND ocNrContrato = ?
            AND clCliente = ?
            AND opNrPlantao = ?
        `,
        [
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
        ]
    );

    return result;
}

const pegaCheckoutMaisRecente = (checks) => {
    return checks.reduce((acc, current) => {
        return new Date(current.ocCheckOut).getTime() > new Date(acc.ocCheckOut).getTime()
            ? current
            : acc;
    });
};

const pegaPrimeiroCheckin = (checks) => {
    if (checks.length === 1) {
        return checks[0];
    }

    return checks.reduce((acc, current) => {
        return new Date(current.ocCheckIn).getTime() < new Date(acc.ocCheckIn).getTime()
            ? current
            : acc;
    });
};

export async function _enviarCheckAprovadosParaFechamento(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'opPeriodoIni',
                    'ceTipoPagamento',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const atendimento = await buscaAtendimento(event);

        if (atendimento.opSituacao !== 'EmExecucao') {
            return baseResponse.badRequest(
                'Não é possivel realizar o fechamento de atendimentos que não estão em execução.'
            );
        }

        const checks = event.checks.filter((check) => !check.codFechamento);

        if (!checks.length) {
            return baseResponse.badRequest('Nenhum checkin/checkout encontrado para o fechamento.');
        }

        const primeiroCheckin = pegaPrimeiroCheckin(checks);
        const checkoutMaisRecente = pegaCheckoutMaisRecente(checks);

        const dataInicial = formataHora(primeiroCheckin.ocCheckIn);
        const dataFinal = formataHora(checkoutMaisRecente.ocCheckOut);

        const {
            results: { insertId: capCodFechamentoId },
        } = await insert('capCodFechamento', {}, event);

        for (let i = 0; i < checks.length; i++) {
            const checksAtualizado = await update(
                'capOperPlantaoCheck',
                {
                    codFechamento: capCodFechamentoId,
                    ocUsuarioAprovacao: event.body.user,
                    ocSituacao: 'EnviadoAprovacao',
                },
                {
                    ocCheckOut: formataHora(checks[i].ocCheckOut),
                    isInstSaude: checks[i].isInstSaude,
                    laNome: checks[i].laNome,
                    esEspecialidade: checks[i].esEspecialidade,
                    ocNrContrato: checks[i].ocNrContrato,
                    clCliente: checks[i].clCliente,
                    opNrPlantao: checks[i].opNrPlantao,
                    ceTipoPagamento: checks[i].ceTipoPagamento,
                },
                event
            );

            console.log(
                `Checkin atualizado com codFechamento: ${capCodFechamentoId}`,
                checksAtualizado
            );
        }

        const [aditivo] = await dbQuery(
            /*sql*/ `
            select * from capAditivoProfSaude
            where isInstSaude = ?
            and laNome = ?
            and esEspecialidade = ?
            and ocNrContrato = ?
            and clCliente = ?
            and opNrPlantao = ?
        `,
            [
                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.opNrPlantao,
            ]
        );

        const { totalHoras, valorTotal } = await buscaTotalHorasTotalValor({
            ...event,
            codFechamento: capCodFechamentoId,
        });

        const entityFechamento = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            opNrPlantao: event.opNrPlantao,
            afNome: `Fechamento ${capCodFechamentoId} - Plantão: ${event.opNrPlantao}`,
            afIni: dataInicial,
            afFim: dataFinal,
            codFechamento: capCodFechamentoId,
            adSituacao: 'aguardandoAprovacao',
            ceTipoPagamento: event.ceTipoPagamento,
            psCPF: event.psCPF,
            adDataSolicitacao: aditivo.adDataSolicitacao,
            afValor: valorTotal,
        };

        console.log(
            `Atualizando fechamento ${capCodFechamentoId}, com totalHoras ${totalHoras} e valorTotal ${valorTotal}`
        );
        const capAtendimentoFechamentoResult = await insert(
            'capAtendimentoFechamento',
            entityFechamento,
            event
        );
        console.log('capAtendimentoFechamentoResult', capAtendimentoFechamentoResult);

        return baseResponse.created('Criado com sucesso');
    } catch (err) {
        console.error(err);

        return baseResponse.error('Error ao processar requisição');
    }
}
