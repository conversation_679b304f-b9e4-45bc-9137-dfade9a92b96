import {
  checkData,
  execQuery,
  baseResponse,
  insert,
  update,
  onlyNumber,
} from 'capfunctions';

async function _list(event) {
  console.log(event);
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'esEspecialidade',
          'ocNrContrato',
          'clCliente',
          'ceTipoPagamento',
          'opNrPlantao'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const query = `SELECT
                    b.psCPF,
                    a.usNome,
                    a.usTelefone, 
                    a.usEmail, 
                    s.psDataSolicitacao,
                    s.psSequenciaSolicitacao,
                    s.psSituacao
                  FROM capInstSaudePlantaoSolicitacao s, capUsuario a, capProfissionalSaude b
                  where s.isInstSaude = ?
                  and   s.laNome = ?
                  and   s.esEspecialidade = ?
                  and   s.ocNrContrato = ?
                  and   s.clCliente = ?
                  and   s.ceTipoPagamento = ?
                  and   s.opNrPlantao = ?
                  and   s.psCPF=b.psCPF
                  and   a.usCPFUsuario=b.psCPF
                  ORDER BY s.psSequenciaSolicitacao ASC
                `;

    const params = [event.isInstSaude,event.laNome,event.esEspecialidade,event.ocNrContrato,event.clCliente, event.ceTipoPagamento, event.opNrPlantao];
    const response = await execQuery(query, params);
    
    console.log('response', response);
    
    if (response.success) {
      return baseResponse.created('Listado com sucesso', response.results)
    }
    
    return baseResponse.error("Ocorreu um erro ao listar plantões");
  } catch (error) {
    console.log("ERROR _list",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _listpsplantao(event) {
  console.log('evento',event);
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'esEspecialidade',
          'ocNrContrato',
          'clCliente',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const query = `SELECT b.psCPF,a.usNome,a.usTelefone, a.usEmail
                FROM capUsuario a, capProfissionalSaude b, capProfSaudeCliente c, capProfSaudeEspecialidade e, capInstSaudeContratoEspec t
                where t.isInstSaude = ?
                and   t.laNome = ?
                and   t.esEspecialidade = ?
                and   t.ocNrContrato = ?
                and   t.clCliente = ?
                and   c.clCliente = t.clCliente
                and   c.psCPF=b.psCPF
                and   a.usCPFUsuario=b.psCPF
                and   e.psCPF=b.psCPF
                and   e.esEspecialidade = t.esEspecialidade
                ORDER BY a.usNome;
                `;

    const params = [event.isInstSaude,event.laNome,event.esEspecialidade,event.ocNrContrato,onlyNumber(event.clCliente)];
    console.log('parametros query _listpsplantao', params);
    console.log('query', query);
    const response = await execQuery(query, params);
    
    console.log('response', response);
    
    if (response.success) {
      return baseResponse.created('Listado com sucesso', response.results)
    }
    return baseResponse.error("Ocorreu um erro ao listar os profissionais para plantão");
  } catch (error) {
    console.log("ERROR _listpsplantao",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _atribuir(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'esEspecialidade',
          'ocNrContrato',
          'clCliente',
          'ceTipoPagamento',
          'opNrPlantao',
          'psCPF',
          'psSequenciaSolicitacao',
          'psDataSolicitacao',
          'usCPFUsuarioAprovador'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const entity = {
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      esEspecialidade: event.esEspecialidade,
      ocNrContrato: event.ocNrContrato,
      clCliente: event.clCliente,
      ceTipoPagamento: event.ceTipoPagamento,
      opNrPlantao: event.opNrPlantao,
      psCPF: event.psCPF,
      adDataSolicitacao: event.psDataSolicitacao,
      usCPFusuarioAprovador: event.usCPFUsuarioAprovador,
      adValorFixo: event.opValorHora,
      adValor: event.opValorHora
    };

    const response = await insert('capAditivoProfSaude', entity, event);

    if (response.success) {

        const entPS = {
          psSituacao: 'Aprovado'
        };
    
        const updPS = {
          isInstSaude: event.isInstSaude,
          laNome: event.laNome,
          esEspecialidade: event.esEspecialidade,
          ocNrContrato: event.ocNrContrato,
          clCliente: event.clCliente,
          ceTipoPagamento: event.ceTipoPagamento,
          opNrPlantao: event.opNrPlantao,
          psCPF: event.psCPF,
          psSequenciaSolicitacao: event.psSequenciaSolicitacao
        };

        const respupdPS = await update('capInstSaudePlantaoSolicitacao', entPS, updPS, event);

        const entSP = {
          psCPF: event.psCPF,
          opSituacao: 'AguardExecucao',
          usCPFUsuarioAprovador: event.usCPFUsuarioAprovador
        };
    
        const updSP = {
          isInstSaude: event.isInstSaude,
          laNome: event.laNome,
          esEspecialidade: event.esEspecialidade,
          ocNrContrato: event.ocNrContrato,
          clCliente: event.clCliente,
          ceTipoPagamento: event.ceTipoPagamento,
          opNrPlantao: event.opNrPlantao,
        };
        
        await update('capInstSaudePlantao', entSP, updSP, event);
        
        const entityPSA = {
          isInstSaude: event.isInstSaude,
          laNome: event.laNome,
          esEspecialidade: event.esEspecialidade,
          ocNrContrato: event.ocNrContrato,
          clCliente: event.clCliente,
          ceTipoPagamento: event.ceTipoPagamento,
          opNrPlantao: event.opNrPlantao,
          psCPF: event.psCPF,
          psSequenciaSolicitacao: event.psSequenciaSolicitacao,
          opSituacao: 'EmExecucao',
          saUsuarioEvento: event.usCPFusuarioAprovador,
          saDataEvento: 'current_timestamp'
        };
    
        await insert('capInstSaudePlSolicitAprovado', entityPSA, event);
    
        return baseResponse.created('Criado com successo');
    }

    return baseResponse.notFound('Nada encontrado');
  } catch (error) {
    console.log("ERROR _atribuir",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

export { _atribuir, _list, _listpsplantao };