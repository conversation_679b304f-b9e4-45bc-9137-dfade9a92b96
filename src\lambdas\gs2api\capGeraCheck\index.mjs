import { AtendimentoRepository } from './repositories/atendimento.mjs';
import { AgendaRepository } from './repositories/agenda.mjs';
import { CheckRepository } from './repositories/checkin.mjs';
import { CheckProcessor } from './processors/checkin.mjs';
import { AtendimentoProcessor } from './processors/atendimento.mjs';
import { FechamentoProcessor } from './processors/fechamento.mjs';
import { PlantaoProcessor } from './processors/plantao.mjs';

export const handler = async () => {
  console.log('Iniciando processamento...');

  try {
    const CLIENTES = [
      26324097000181, // clienteDemo
    ];

    const atendimentoRepository = new AtendimentoRepository(CLIENTES);
    const agendaRepository = new AgendaRepository();
    const checkRepository = new CheckRepository();
    const checkProcessor = new CheckProcessor(checkRepository);
    const atendimentoProcessor = new AtendimentoProcessor(
      agendaRepository,
      checkProcessor,
      checkRepository,
      atendimentoRepository
    );
    const fechamentoProcessor = new FechamentoProcessor(checkRepository);
    const plantaoProcessor = new PlantaoProcessor(
      atendimentoRepository,
      atendimentoProcessor,
      fechamentoProcessor
    );

    await plantaoProcessor.process();

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Processamento concluído' }),
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Erro no processamento' }),
    };
  }
};
