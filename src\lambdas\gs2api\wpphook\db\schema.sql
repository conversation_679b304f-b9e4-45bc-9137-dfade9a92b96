-- Complete schema with UUID-only sessions
-- No legacy support, clean implementation

CREATE TABLE `clinics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `razao_social` varchar(255) NOT NULL,
  `cnpj` varchar(18) NOT NULL,
  `active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_cnpj` (`cnpj`),
  KEY `idx_active` (`active`)
);

CREATE TABLE `user_sessions` (
  `id` varchar(36) NOT NULL,  -- UUID primary key
  `phone_number` varchar(20) NOT NULL,
  `current_state` enum('INITIAL','GREETING','CLINIC_REQUEST','CLINIC_VALIDATION','CLINIC_CONFIRMATION','NAME_REQUEST','CPF_REQUEST','EMAIL_REQUEST','DATA_CONFIRMATION','DATA_CORRECTION','TERMS_PRESENTATION','FAQ_MENU','FAQ_RESPONSE','TERMS_ACCEPTANCE','COMPLETION','END') NOT NULL DEFAULT 'INITIAL',
  `session_data` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone_number`),
  KEY `idx_state` (`current_state`),
  KEY `idx_updated` (`updated_at`),
  KEY `idx_phone_updated` (`phone_number`, `updated_at`)  -- Composite index for efficient latest session lookup
);

CREATE TABLE `user_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_uuid` varchar(36) NOT NULL,  -- References user_sessions.id
  `clinic_id` int NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `cpf` varchar(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `terms_accepted` tinyint(1) DEFAULT '0',
  `terms_accepted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `clinic_id` (`clinic_id`),
  KEY `idx_cpf` (`cpf`),
  KEY `idx_email` (`email`),
  KEY `idx_session_uuid` (`session_uuid`),
  CONSTRAINT `user_data_ibfk_1` FOREIGN KEY (`session_uuid`) REFERENCES `user_sessions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_data_ibfk_2` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`)
);

CREATE TABLE `interaction_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_uuid` varchar(36) NOT NULL,  -- References user_sessions.id
  `message_type` enum('INCOMING','OUTGOING') NOT NULL,
  `message_content` text NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_uuid` (`session_uuid`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_type` (`message_type`),
  CONSTRAINT `interaction_logs_ibfk_1` FOREIGN KEY (`session_uuid`) REFERENCES `user_sessions` (`id`) ON DELETE CASCADE
);

CREATE TABLE `session_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_uuid` varchar(36) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `action_type` enum('CREATED','UPDATED','COMPLETED','EXPIRED') NOT NULL,
  `old_state` varchar(50) DEFAULT NULL,
  `new_state` varchar(50) DEFAULT NULL,
  `session_data` json DEFAULT NULL,
  `action_timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_phone_history` (`phone_number`),
  KEY `idx_session_history` (`session_uuid`),
  KEY `idx_timestamp_history` (`action_timestamp`)
);

CREATE TABLE `human_contact_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_uuid` varchar(36) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `reason` varchar(50) NOT NULL DEFAULT 'clinic_not_found',
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_uuid` (`session_uuid`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `human_contact_requests_ibfk_1` FOREIGN KEY (`session_uuid`) REFERENCES `user_sessions` (`id`) ON DELETE CASCADE
);

-- Stored procedures for session management

DELIMITER $$

CREATE PROCEDURE `GetLatestSessionByPhone`(IN phone_num VARCHAR(20))
BEGIN
    SELECT 
        id,
        phone_number,
        current_state,
        session_data,
        created_at,
        updated_at
    FROM user_sessions
    WHERE phone_number = phone_num
    ORDER BY updated_at DESC
    LIMIT 1;
END$$

CREATE PROCEDURE `CreateNewSession`(
    IN phone_num VARCHAR(20),
    IN initial_state VARCHAR(50),
    IN session_json JSON
)
BEGIN
    DECLARE new_uuid VARCHAR(36);
    SET new_uuid = UUID();
    
    INSERT INTO user_sessions (id, phone_number, current_state, session_data)
    VALUES (new_uuid, phone_num, initial_state, session_json);
    
    SELECT new_uuid as session_id;
END$$

CREATE PROCEDURE `GetSessionHistory`(IN phone_num VARCHAR(20), IN limit_count INT)
BEGIN
    SELECT 
        sh.session_uuid,
        sh.action_type,
        sh.old_state,
        sh.new_state,
        sh.action_timestamp,
        us.current_state,
        us.session_data
    FROM session_history sh
    LEFT JOIN user_sessions us ON sh.session_uuid = us.id
    WHERE sh.phone_number = phone_num
    ORDER BY sh.action_timestamp DESC
    LIMIT limit_count;
END$$

-- Triggers to track session changes

CREATE TRIGGER `track_session_changes`
AFTER UPDATE ON `user_sessions`
FOR EACH ROW
BEGIN
    INSERT INTO session_history (
        session_uuid,
        phone_number,
        action_type,
        old_state,
        new_state,
        session_data,
        action_timestamp
    ) VALUES (
        NEW.id,
        NEW.phone_number,
        'UPDATED',
        OLD.current_state,
        NEW.current_state,
        NEW.session_data,
        NOW()
    );
END$$

CREATE TRIGGER `track_session_creation`
AFTER INSERT ON `user_sessions`
FOR EACH ROW
BEGIN
    INSERT INTO session_history (
        session_uuid,
        phone_number,
        action_type,
        old_state,
        new_state,
        session_data,
        action_timestamp
    ) VALUES (
        NEW.id,
        NEW.phone_number,
        'CREATED',
        NULL,
        NEW.current_state,
        NEW.session_data,
        NOW()
    );
END$$

DELIMITER ;