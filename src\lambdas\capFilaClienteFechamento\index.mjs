import { AgendaRepository } from './repositories/agenda.mjs';
import { CheckRepository } from './repositories/checkin.mjs';
import { CheckProcessor } from './processors/checkin.mjs';
import { AtendimentoProcessor } from './processors/atendimento.mjs';
import { FechamentoProcessor } from './processors/fechamento.mjs';
import { PlantaoProcessor } from './processors/plantao.mjs';

export const handler = async (event) => {
  try {
    console.log(event);

    const agendaRepository = new AgendaRepository();
    const checkRepository = new CheckRepository();
    const checkProcessor = new CheckProcessor(checkRepository);

    const atendimentoProcessor = new AtendimentoProcessor(
      agendaRepository,
      checkProcessor,
      checkRepository
    );

    const fechamentoProcessor = new FechamentoProcessor(checkRepository);
    const plantaoProcessor = new PlantaoProcessor(atendimentoProcessor, fechamentoProcessor);

    const atendimentos = [];

    event.Records.forEach((record) => {
      const body = JSON.parse(record.body);

      body.forEach((i) => {
        atendimentos.push(i);
      });
    });

    if (atendimentos.length === 0) {
      console.log('No data to process.');
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'No data to process' }),
      };
    }

    await plantaoProcessor.process(atendimentos);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'Processamento concluído' }),
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 500,
      body: JSON.stringify({ error: 'Erro no processamento' }),
    };
  }
};
