const AWS = require('aws-sdk');
const SES = new AWS.SES({ region: process.env.REGION || 'us-east-1' });

var mimemessage = require('mimemessage');

var mailContent = mimemessage.factory({ contentType: 'multipart/mixed', body: [] });

exports.handler = async function (event) {
  mailContent.header('From', '<EMAIL>');
  mailContent.header(
    'To',
    '<EMAIL>',
  );
  mailContent.header(
    'Subject',
    'Arquivo diário de Titulos de Antecipação de Recebíveis ' + event.csvfile
  );

  var alternateEntity = mimemessage.factory({
    contentType: 'multipart/alternate',
    body: [],
  });

  var plainEntity = mimemessage.factory({
    body: 'Voce esta recebendo este email para para lançamentos dos títulos junto a BANKME',
  });

  alternateEntity.body.push(plainEntity);

  mailContent.body.push(alternateEntity);

  var attachmentEntity = mimemessage.factory({
    contentType: 'text/html;charset=utf-8',
    body: event.csvdata,
  });

  attachmentEntity.header('Content-Disposition', 'attachment ;filename=' + event.csvfile);

  mailContent.body.push(attachmentEntity);

  const res = await SES.sendRawEmail({ RawMessage: { Data: mailContent.toString() } }).promise();

  console.log('envio email:', res);

  return res;
};
