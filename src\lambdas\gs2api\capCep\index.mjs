import {execQuery} from './functions.mjs';


export const handler = async (event) => {

try {

    console.log('event', event);
    var query;
    var params;
    query = `SELECT cep, uf, cidade, bairro, endereco, complemento, nome
        FROM capCEP
        where cep = ?`;

    params = [event.cep];
    
    const respsacado = await execQuery(query, params);
    
    console.log('respsacado ', respsacado);
    if (respsacado) {
      var psSacado=JSON.parse(JSON.stringify(respsacado[0]));
    } else {
      return ({cep: event.cep, f: false });
    }
    return (psSacado);
  } catch (error) {
      console.log('Erro no CEP', error);
      return (false);
  }
}