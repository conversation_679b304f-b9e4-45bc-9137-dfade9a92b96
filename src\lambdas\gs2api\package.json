{"name": "lambdas", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@aws-sdk/client-lambda": "^3.693.0", "@aws-sdk/client-sqs": "^3.693.0", "@prisma/client": "^6.3.1", "axios": "^0.27.2", "bcryptjs": "^2.4.3", "capcorpconf": "file:../../layers/capcorpconf", "capfunctions": "file:../../layers/capfunctions", "dayjs": "^1.11.4", "dotenv": "^16.0.1", "joi": "^18.0.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.3", "mysql": "^2.18.1", "uuid": "^8.3.2", "yup": "^0.32.11"}}