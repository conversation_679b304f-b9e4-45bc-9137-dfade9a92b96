import { checkData, baseResponse, dbQuery } from 'capfunctions';

export async function _list(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: ['clCliente'],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        // Pagination parameters
        const page = Number(event.page) || 1;
        const pageSize = Number(event.pageSize) || 25;
        const offset = (page - 1) * pageSize;

        // Build base parameters for filters
        const baseParams = [event.clCliente];
        let filter = '';

        if (event.opSituacao) {
            filter += /*sql*/ `
                AND t.opSituacao LIKE ?
            `;

            baseParams.push(`%${event.opSituacao}%`);
        }

        if (event.search) {
            filter += /*sql*/ `
                AND (
                    t.ocNrContrato LIKE ?
                    OR t.laNome LIKE ?
                    OR t.esEspecialidade LIKE ?
                    OR t.ceTipoPagamento LIKE ?
                    OR t.opNrPlantao LIKE ?
                    OR t.opSituacao LIKE ?
                    OR u.usNome LIKE ?
                )
            `;
            baseParams.push(
                `%${event.search}%`,
                `%${event.search}%`,
                `%${event.search}%`,
                `%${event.search}%`,
                `%${event.search}%`,
                `%${event.search}%`,
                `%${event.search}%`
            );
        }

        // Prepare multi query combining count and data queries
        const multiQuery = /*sql*/ `
            SELECT COUNT(*) as total
            FROM capInstSaudePlantao t
            LEFT JOIN capProfissionalSaude ps ON (
                t.psCPF = ps.psCPF
            )
            LEFT JOIN capUsuario u ON (
                ps.psCPF = u.usCPFUsuario
            )
            WHERE t.clCliente = ?
            ${filter};

            SELECT
                t.ocNrContrato,
                t.laNome,
                t.esEspecialidade,
                t.ceTipoPagamento,
                t.opPeriodoIni,
                t.opPeriodoFim,
                t.opNrPlantao,
                t.opSituacao,
                t.clCliente,
                t.isInstSaude,
                t.opTipoFechamento,
                t.opDiaFechamento,
                u.usNome
            FROM capInstSaudePlantao t
            LEFT JOIN capProfissionalSaude ps ON (
                t.psCPF = ps.psCPF
            )
            LEFT JOIN capUsuario u ON (
                ps.psCPF = u.usCPFUsuario
            )
            WHERE t.clCliente = ?
            ${filter}
            ORDER BY t.opNrPlantao DESC
            LIMIT ?
            OFFSET ?;
        `;

        // Duplicate base parameters for both queries and add pagination for the second
        const multiParams = [...baseParams, ...baseParams, pageSize, offset];

        // Execute multi query; result[0] is count, result[1] is data
        const result = await dbQuery(multiQuery, multiParams);
        const total = result[0][0].total;
        const totalPages = Math.ceil(total / pageSize);
        const nextPage = page < totalPages ? page + 1 : null;
        const data = result[1];

        return baseResponse.paginated('Listado com sucesso', {
            totalPages,
            currentPage: page,
            nextPage,
            data,
        });
    } catch (error) {
        console.log('ERROR _list', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}