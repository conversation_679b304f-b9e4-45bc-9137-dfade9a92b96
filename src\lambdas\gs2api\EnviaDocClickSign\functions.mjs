import {S3Client, GetObjectCommand} from "@aws-sdk/client-s3";
import {getSignedUrl} from '@aws-sdk/s3-request-presigner';

const clientS3 = new S3Client({region: "us-east-2"});

const S3_BUCKET = "ch-capitaleaditivo";

import axios from "axios";

const requestConfig = {
  method: "post",
  maxBodyLength: Infinity,
  url: "https://app.clicksign.com/api/v3/envelopes/",
  headers: {
    Host: "app.clicksign.com",
    Authorization: process.env.CLICKSIGN_API_KEY_PRODUCTION,
    "Content-Type": "application/vnd.api+json",
    Accept: "application/vnd.api+json",
  },
};


export async function criaEnvelope(pdf64, keyPath, type) {
  const data = JSON.stringify({
    data: {
      type: "envelopes",
      attributes: {
        name: "Aditivo do Contrato de Antecipação de Recebíveis",
        locale: "pt-BR",
        auto_close: true,
        remind_interval: 3,
        block_after_refusal: false,
      },
    },
  });
  
  const config = {
    ...requestConfig,
    data,
  };
  try {
    const resp = await axios.request(config);
    return resp?.data;
  } catch (e) {
    console.log(e);
    console.log("ERROR CRIA ENVELOPE");
  }
  //console.log(JSON.stringify(resp.data));
}

export async function addDocToEnvelope(envelope_id, aditivo) {
  
  const adt = await getS3Object(`aditivos/aditivo-${aditivo}.pdf`);
  
  
  const data = JSON.stringify({
    data: {
      type: "documents",
      attributes: {
        filename: `aditivo-${aditivo}.pdf`,
        content_base64:
          `data:application/pdf;base64,${adt}`
      },
    },
  });
  
  let config = {
    ...requestConfig,
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}/documents`,
    data
  };
  try {
    const resp = await axios.request(config);
    console.log('DOCUMENT TO ENVELOPE', resp.data);
    return resp.data;
  } catch (e) {
    console.log("ERROR ADICIONAR DOCUMENTO AO ENVELOPE");
    // console.log(e)
  }
}

async function getS3Object(keyPath) {
  const command = new GetObjectCommand({
    Bucket: S3_BUCKET,
    Key: keyPath,
  });
  console.log('commnad do S3', command);
  try {
    const signedUrl = await getSignedUrl(clientS3, command, {expiresIn: 3600});
    const response = await axios.get(signedUrl, {responseType: 'arraybuffer'});
    const buffer = Buffer.from(response.data, 'binary');
    const base64 = buffer.toString('base64');
    //console.log(base64);
    return base64;
  } catch (error) {
    console.error("Error retrieving S3 object:", error);
    throw error;
  }
}

export async function addSignatariosToDocument(envelope_id, signerAttributes) {
  const signerData = {
    type: 'signers',
    attributes: {
      ...signerAttributes,
      refusable: true,
      group: "1",
      communicate_events: {
        document_signed: "email",
        signature_request: "email",
        signature_reminder: "email"
      }
    }
  };
  
  console.log('signerData', signerData);
  const config = {
    ...requestConfig,
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}/signers`,
    data: {data: signerData},
  };
  try {
    const resp = await axios.request(config);
    console.log(resp.data);
    return resp?.data;
  } catch (e) {
     console.log(e);
    console.log("ERROR ADICIONAR SIGNATARIO");
  }
}

export async function addSignatarioRequirement(envelope_id, documentId, signerId, signerRole) {
  const signerData = {
    type: 'requirements',
    attributes: {
      action: 'agree',
      role: signerRole,
    },
    relationships: {
      document: {
        data: {type: 'documents', id: documentId}
      },
      signer: {
        data: {type: 'signers', id: signerId}
      }
    }
  };
  
  const config = {
    ...requestConfig,
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}/requirements`,
    data: {data: signerData},
  };
  try {
    const resp = await axios.request(config);
    return resp?.data;
  } catch (e) {
    // console.log(e);
    console.log("ERROR ADICIONAR REQUISITOS DE SIGNATARIO");
  }
}

export async function addSignatarioAuthRequirement(envelope_id, documentId, signerId, signerRole) {
  const signerData = {
    type: 'requirements',
    attributes: {
      action: 'provide_evidence',
      auth: 'email',
    },
    relationships: {
      document: {
        data: {type: 'documents', id: documentId}
      },
      signer: {
        data: {type: 'signers', id: signerId}
      }
    }
  };
  
  const config = {
    ...requestConfig,
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}/requirements`,
    data: {data: signerData},
  };
  try {
    const resp = await axios.request(config);
    return resp?.data;
  } catch (e) {
    console.log(e);
    console.log("ERROR ADICIONAR REQUISITOS AUTENTICAÇÃO");
  }
}

export async function activateEnvelope(envelope_id) {
  const config = {
    ...requestConfig,
    method: 'patch',
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}`,
    data: {
      data: {
        id: envelope_id,
        type: "envelopes",
        attributes: {
          status: "running"
        }
      }
    }
  };
  try {
    const resp = await axios.request(config);
    return resp?.data;
  } catch (e) {
    console.log(e);
    console.log("ERROR ATIVAR ENVELOPE");
  }
}

export async function sendNotification(envelope_id, signerId) {
  const reqData = {
    type: 'notifications',
    attributes: {
      message: 'email',
    },
  };
  
  const config = {
    ...requestConfig,
    url: `https://app.clicksign.com/api/v3/envelopes/${envelope_id}/signers/${signerId}/notifications`,
    data: {data: reqData},
  };
  try {
    const resp = await axios.request(config);
    return resp?.data;
  } catch (e) {
    // console.log(e);
    console.log("ERROR ENVIAR NOTIFICAÇÃO");
  }
}

