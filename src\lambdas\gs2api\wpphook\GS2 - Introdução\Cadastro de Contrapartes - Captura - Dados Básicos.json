{"id": null, "tenantId": "473680", "name": "Cadastro de Contrapartes - Captura - Dados Básicos", "created": null, "flowNodes": [{"flowReplies": [{"flowReplyType": "Text", "data": "<p>👤 Qual é o seu nome completo, por favor?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "NomeCompleto", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^[A-Za-zÀ-ÿ' ]{6,}$", "fallback": "Por favor, escreva o seu nome completo.", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-bLnZQ", "flowNodeType": "Question", "flowNodePosition": {"posX": "2227", "posY": "307"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>📄 <PERSON><PERSON><PERSON>, por favor, informe seu CPF (apenas números).</p>", "caption": "", "mimeType": ""}], "userInputVariable": "CPF", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^\\d{11}$", "fallback": "Por favor, digite apenas os 11 números do seu CPF.", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-SvKDK", "flowNodeType": "Question", "flowNodePosition": {"posX": "3140", "posY": "629"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>📧 Qual seu e-mail para contato?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "Email", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$", "fallback": "Por favor verifique o formato de e-mail (ex: <EMAIL>)", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-ueXOf", "flowNodeType": "Question", "flowNodePosition": {"posX": "4062", "posY": "650"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>✅ Confirme os dados abaixo:<br>\n<br>\n<strong>- Nome: </strong>@NomeCompleto<br>\n<strong>- CPF: </strong>@CPF<br>\n<strong>- Email: </strong>@Email</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "GuWzOoO", "buttonText": "👍 Está certo!", "nodeResultId": "main_message-uLpEQ"}, {"id": "lvCuqDK", "buttonText": "✏️ Preciso corrigir", "nodeResultId": "main_buttons-WcEDx"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-icOxg", "id": "main_buttons-rOiqW", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "5320", "posY": "589"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "bdiXDZO", "flowConditionType": "NotEqual", "variable": "@NomeCompleto", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_question-SvKDK", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-<PERSON><PERSON><PERSON>", "flowNodeType": "Condition", "flowNodePosition": {"posX": "2656", "posY": "306"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "pVbjWxY", "flowConditionType": "NotEqual", "variable": "@CPF", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_question-ueXOf", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-AioVD", "flowNodeType": "Condition", "flowNodePosition": {"posX": "3617", "posY": "291"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>Qual das informações precisa corrigir?</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "YodKkNQ", "buttonText": "👤 Nome", "nodeResultId": "main_question-drrnF"}, {"id": "oZQlPuk", "buttonText": "📄 CPF", "nodeResultId": "main_question-PYnmI"}, {"id": "HXFBBGG", "buttonText": "📧 Email", "nodeResultId": "main_question-<PERSON><PERSON><PERSON>"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "", "id": "main_buttons-WcEDx", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "6391", "posY": "687"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>👤 Qual é o seu nome completo?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "NomeCompleto", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^[A-Za-zÀ-ÿ' ]{6,}$", "fallback": "Por favor, escreva o seu nome completo.", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-drrnF", "flowNodeType": "Question", "flowNodePosition": {"posX": "6902", "posY": "590"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>📧 Qual seu e-mail para contato?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "Email", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$", "fallback": "Por favor verifique o formato de e-mail (ex: <EMAIL>)", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-<PERSON><PERSON><PERSON>", "flowNodeType": "Question", "flowNodePosition": {"posX": "6915", "posY": "970"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>📄 Qual o seu CPF (apenas números)?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "CPF", "answerValidation": {"type": "Regex", "minValue": "", "maxValue": "", "regex": "^\\d{11}$", "fallback": "Por favor, digite apenas os 11 números do seu CPF.", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-PYnmI", "flowNodeType": "Question", "flowNodePosition": {"posX": "6920", "posY": "755"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "zWsFhfy", "flowConditionType": "NotEqual", "variable": "@NomeCompleto", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-rOiqW", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-OfCgA", "flowNodeType": "Condition", "flowNodePosition": {"posX": "7609", "posY": "558"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "ZTyIjzn", "flowConditionType": "NotEqual", "variable": "@CPF", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-rOiqW", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-EwaDv", "flowNodeType": "Condition", "flowNodePosition": {"posX": "7606", "posY": "747"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "gWAQxVw", "flowConditionType": "NotEqual", "variable": "@Email", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-rOiqW", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-crHdQ", "flowNodeType": "Condition", "flowNodePosition": {"posX": "4485", "posY": "287"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "fjSNECr", "flowConditionType": "NotEqual", "variable": "@Email", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-rOiqW", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-hvsVJ", "flowNodeType": "Condition", "flowNodePosition": {"posX": "7620", "posY": "978"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>Vamos precisar que nos informe alguns dados pessoais.</p>", "caption": "", "mimeType": ""}], "id": "main_message-lDqpt", "flowNodeType": "Message", "flowNodePosition": {"posX": "1766", "posY": "240"}, "isStartNode": true}, {"newFlowId": "", "id": "main_invokeFlow-aymuS", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "3101", "posY": "-82"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "TguqtlI", "flowConditionType": "Equal", "variable": "@RespostaLivre", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-rOiqW", "nResultNodeId": "main_invokeFlow-aymuS"}, "conditionOperator": "None", "id": "main_condition-icOxg", "flowNodeType": "Condition", "flowNodePosition": {"posX": "5305", "posY": "121"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-dNJLf", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "6348", "posY": "1198"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p><PERSON><PERSON><PERSON> pela <PERSON>.</p>", "caption": "", "mimeType": ""}], "id": "main_message-uLpEQ", "flowNodeType": "Message", "flowNodePosition": {"posX": "5829.96440450637", "posY": "968.4466067595533"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_question-bLnZQ-main_condition-<PERSON><PERSON><PERSON>", "sourceNodeId": "main_question-bLnZQ", "targetNodeId": "main_condition-<PERSON><PERSON><PERSON>"}, {"id": "reactflow__edge-main_question-SvKDK-main_condition-AioVD", "sourceNodeId": "main_question-SvKDK", "targetNodeId": "main_condition-AioVD"}, {"id": "reactflow__edge-main_buttons-rOiqWlvCuqDK-main_buttons-WcEDx", "sourceNodeId": "main_buttons-rOiqW__lvCuqDK", "targetNodeId": "main_buttons-WcEDx"}, {"id": "reactflow__edge-main_buttons-WcEDxYodKkNQ-main_question-drrnF", "sourceNodeId": "main_buttons-WcEDx__YodKkNQ", "targetNodeId": "main_question-drrnF"}, {"id": "reactflow__edge-main_buttons-WcEDxoZQlPuk-main_question-PYnmI", "sourceNodeId": "main_buttons-WcEDx__oZQlPuk", "targetNodeId": "main_question-PYnmI"}, {"id": "reactflow__edge-main_buttons-WcEDxHXFBBGG-main_question-<PERSON><PERSON><PERSON>", "sourceNodeId": "main_buttons-WcEDx__HXFBBGG", "targetNodeId": "main_question-<PERSON><PERSON><PERSON>"}, {"id": "reactflow__edge-main_question-drrnF-main_condition-OfCgA", "sourceNodeId": "main_question-drrnF", "targetNodeId": "main_condition-OfCgA"}, {"id": "reactflow__edge-main_question-PYnmI-main_condition-EwaDv", "sourceNodeId": "main_question-PYnmI", "targetNodeId": "main_condition-EwaDv"}, {"id": "reactflow__edge-main_question-ueXOf-main_condition-crHdQ", "sourceNodeId": "main_question-ueXOf", "targetNodeId": "main_condition-crHdQ"}, {"id": "reactflow__edge-main_question-DukyJ-main_condition-hvsVJ", "sourceNodeId": "main_question-<PERSON><PERSON><PERSON>", "targetNodeId": "main_condition-hvsVJ"}, {"id": "reactflow__edge-main_message-lDqpt-main_question-bLnZQ", "sourceNodeId": "main_message-lDqpt", "targetNodeId": "main_question-bLnZQ"}, {"id": "reactflow__edge-main_condition-Oshiutrue-main_question-SvKDK", "sourceNodeId": "main_condition-Oshiu__true", "targetNodeId": "main_question-SvKDK"}, {"id": "reactflow__edge-main_condition-Oshiufalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-<PERSON><PERSON><PERSON>__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-AioVDtrue-main_question-ueXOf", "sourceNodeId": "main_condition-AioVD__true", "targetNodeId": "main_question-ueXOf"}, {"id": "reactflow__edge-main_condition-AioVDfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-<PERSON><PERSON>VD__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-crHdQtrue-main_buttons-rOiqW", "sourceNodeId": "main_condition-crHdQ__true", "targetNodeId": "main_buttons-rOiqW"}, {"id": "reactflow__edge-main_condition-crHdQfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-crHdQ__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-OfCgAtrue-main_buttons-rOiqW", "sourceNodeId": "main_condition-OfCgA__true", "targetNodeId": "main_buttons-rOiqW"}, {"id": "reactflow__edge-main_condition-OfCgAfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-OfCgA__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-EwaDvfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-EwaDv__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-hvsVJfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-hvsVJ__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_buttons-rOiqWmain_buttons-rOiqW-default-main_condition-icOxg", "sourceNodeId": "main_buttons-rOiqW__main_buttons-rOiqW-default", "targetNodeId": "main_condition-icOxg"}, {"id": "reactflow__edge-main_condition-icOxgfalse-main_invokeFlow-aymuS", "sourceNodeId": "main_condition-icOxg__false", "targetNodeId": "main_invokeFlow-aymuS"}, {"id": "reactflow__edge-main_condition-icOxgtrue-main_buttons-rOiqW", "sourceNodeId": "main_condition-icOxg__true", "targetNodeId": "main_buttons-rOiqW"}, {"id": "reactflow__edge-main_condition-EwaDvtrue-main_buttons-rOiqW", "sourceNodeId": "main_condition-EwaDv__true", "targetNodeId": "main_buttons-rOiqW"}, {"id": "reactflow__edge-main_condition-hvsVJtrue-main_buttons-rOiqW", "sourceNodeId": "main_condition-hvsVJ__true", "targetNodeId": "main_buttons-rOiqW"}, {"id": "reactflow__edge-main_buttons-rOiqWGuWzOoO-main_message-uLpEQ", "sourceNodeId": "main_buttons-rOiqW__GuWzOoO", "targetNodeId": "main_message-uLpEQ"}, {"id": "reactflow__edge-main_message-uLpEQ-main_invokeFlow-dNJLf", "sourceNodeId": "main_message-uLpEQ", "targetNodeId": "main_invokeFlow-dNJLf"}], "lastUpdated": "2025-07-28T03:03:34.821Z", "isDeleted": false, "transform": {"posX": "-2252.9322378396255", "posY": "-7.008822073864337", "zoom": "0.5743491774985177"}, "isPro": true, "channelTypes": ["WA"]}