import { dbQuery } from 'capfunctions';

export const handler = async (event) => {
  try {
    /*
      Confirmação do Atendimento	2 DIAS antes do atendimento.	
      Check-in no Plantão/Serviço	2 horas antes do início do atendimento.	
      Check-out no Plantão/Serviço	15 minutos após o horário previsto de término do plantão/serviço.
  
      
      
      codigo 3 = Confirmação do Atendimento
      codigo 4 = Check-in no Plantão/Serviço
      codigo 5 = Check-out no Plantão/Serviço
    */
    const cliMensagem = await dbQuery(/*sql*/ `
      SELECT 
        cm.clCliente,
        cm.meCodigo,
        cm.cmIncidencia
        m.meMensagem,
        m.meModeloMeta,
      FROM capCliMensagem cm, capMensageria m
      WHERE cm.meCodigo = m.meCodigo;
    `);

    const confirmacaoAtendimento = () => {}
    const avisoCheckin = () => {}
    const avisoCheckout = () => {}

    for (const m of cliMensagem) {
      const { clCliente, meCodigo, cmIncidencia, msMensagem, meModeloMeta } = m;

      if (meCodigo === 3) {
        // Confirmação do Atendimento 2 DIAS antes do atendimento.
        // Verificar se a data de incidência é 2 dias antes da data do atendimento
      } else if (meCodigo === 4) {
        // Check-in no Plantão/Serviço 2 horas antes do início do atendimento.
        // Verificar se a data de incidência é 2 horas antes do início do atendimento
      } else if (meCodigo === 5) {
        // Check-out no Plantão/Serviço 15 minutos após o horário previsto de término do plantão/serviço.
        // Verificar se a data de incidência é 15 minutos após o horário previsto de término do plantão/serviço
        avisoCheckout(m);
      }
    }
  } catch (error) {}
};
