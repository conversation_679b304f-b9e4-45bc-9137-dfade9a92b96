import {
  checkData,
  deleteEntity,
  execQuery,
  insert,
  update,
  find,
  baseResponse,
} from "capfunctions";

const mainTable = "brcMenuRotas";

async function _list(event) {
  try {
    const query = `SELECT 
                    a.mrNome as nome, 
                    a.mrNomeTela as nomeTela,
                    a.mrRota as rota
                  FROM ${mainTable} a 
                  WHERE mrNome like ?
                `;

    const params = ["%"+(event['nome'] || "")+"%"];
    const response = await execQuery(query, params);

    if (response.success) 
      return baseResponse.created('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  } catch (error) {
    console.log("ERROR _list",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _find(event) {
  try{
    if (checkData(event, ["nome"]) )
      return baseResponse.error("Dados insuficentes");
    
    const query = `SELECT 
                    a.mrNome as nome, 
                    a.mrNomeTela as nomeTela,
                    a.mrRota as rota
                  FROM ${mainTable} a 
                  WHERE mrNome = ?
                `;
  
    const params = [(event['nome'] || "")];
    const response = await execQuery(query, params);
  
    if (response && response.results && response.results.length > 0) 
      return baseResponse.created('Listado com sucesso', response.results && response.results[0])
      
    return baseResponse.notFound("Erro ao buscar menu");
  } catch (error) {
    console.log("ERROR _find",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _insert(event) {
  try {
    if (checkData(event, [ "nome", "nomeTela", "rota"]) )
      return baseResponse.error("Dados insuficentes");
    
    const entity = {
      mrNome: event.nome, 
      mrNomeTela: event.nomeTela,
      mrRota: event.rota,
    };

    const response = await insert(mainTable, entity, event);
    if (response.success) 
      return baseResponse.created('Menu criado com sucesso', response.results)
    
    return baseResponse.error("Erro ao inserir menu");
  } catch (error) {
    console.log("ERROR _insert",error);
    return baseResponse.error("Erro ao processar requisição");  }
}

async function _update(event) {
  try {
    if (checkData(event, ["nome"]) )
      return baseResponse.error("Dados insuficentes");
    
    const body = { "nomeTela": "mrNomeTela",  "rota": "mrRota" };

    const entity = {} ;
    Object.keys(body).forEach(v=> entity[body[v]] = event[v]) ;   
    const response = await update(mainTable, entity, { "mrNome": event.nome }, event);

    if (response.success) {
      return baseResponse.ok('Atualizado com sucesso', response.results)
    }
    console.info(response.success);

    return baseResponse.error("Erro ao atualizar menu");
  } catch (error) {
    console.log("ERROR _update",error);
    return baseResponse.error("Erro ao processar requisição");  }
}

async function _delete(event) {
  try {
    if (checkData(event, ["nome"]) )
      return baseResponse.error("Dados insuficentes");
    
    const response = await deleteEntity(mainTable, { "mrNome": event.nome }, event);

    if (response.success) 
      return baseResponse.ok('Removido com sucesso', response.results)
    
    return baseResponse.error("Erro ao remover");
  } catch (error) {
    console.log("ERROR _delete",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

export { _insert, _update, _list, _find, _delete };
