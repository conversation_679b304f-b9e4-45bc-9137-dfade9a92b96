export const ENUM = {
    SOLICITACAODPO: 50,
    FALECONOSCO: 10,
    OUVIDORIA: 30,
};

export const enviaOcorrencia = async ({
    nome,
    email,
    telefone,
    mensagem,
    formType,
    assunto,
    cpf
}) => {
    const tipoOcorrencia = {
        [ENUM.SOLICITACAODPO]: '1014',
        [ENUM.FALECONOSCO]: '1016',
        [ENUM.OUVIDORIA]: '984',
    };

    const tipoAssunto = {
        [ENUM.SOLICITACAODPO]: '889',
        [ENUM.FALECONOSCO]: '890',
        [ENUM.OUVIDORIA]: '868',
    };

    const chaveUnica = cpf || email

    let anotacao = `Nome: ${nome}\n`;
    anotacao += `Email: ${email}\n`;
    anotacao += `Telefone: ${telefone}\n\n`;
    anotacao += `Assunto: ${assunto}\n\n`;
    anotacao += `Mensagem: ${mensagem}\n`;

    const data = {
        idTipoOcorrencia: tipoOcorrencia[formType],
        idAssunto: tipoAssunto[formType],
        rascunho: 0,
        solicitante: {
            chaveUnica,
            nome,
            email,
            telefone,
        },
        beneficiario: {
            chaveUnica,
            nome,
            email,
            telefone,
        },
        anotacao,
    };

    try {

        console.log(
            'Enviando para api.mosiaomnichannel',
            data
        );

        const response = await fetch(
            'https://api.mosiaomnichannel.com.br/clientes/ocorrencias',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    // Authorization: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlzIjoib2NvcnJlbmNpYXMiLCJrZXkiOiJiYjU5MGJlNS1iMGYyLTRkNzktOGJiMi0xZTE3YWRkNWUzOTgiLCJpYXQiOjE3NDc3NjI5NzQsImV4cCI6MzMyNTY0Mjk3NCwiYXVkIjoiYWxsIn0.0eQYLjea8bv6bDzmspt8U4e890E6lMBU8d3VELN3bLw', //Sandbox
                    Authorization: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhcGlzIjoib2NvcnJlbmNpYXMiLCJrZXkiOiJiODE0MTZiMi1mM2YwLTQxZGItOTFmOC03MDU4OTNmN2Q2MTEiLCJpYXQiOjE3MzM0OTY1MTYsImV4cCI6MzMxMTM3NjUxNiwiYXVkIjoiYWxsIn0.4k_u50bYq8CIFOnS5T749fjzbS9Q9TDIaBX57Wx0sWA', //producao
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            }
        );

        console.log(
            'Status da ocorrência enviada para api.mosiaomnichannel',
            response
        );

        const json = await response.json();

        console.log('Resultado de api.mosiaomnichannel', json);

        return json;
    } catch (error) {
        console.error('Erro ao enviar ocorrencia para api.mosiaomnichannel', error);
    }
};
