import { dbQuery, enviarCheckAprovadosParaFechamento } from 'capfunctions';
import { CheckRepository } from '../repositories/checkin.mjs';

export class FechamentoProcessor {

  /**
   * @param {CheckRepository} checkRepository
  */
  constructor(checkRepository) {
    this.checkRepository = checkRepository;
  }

  async process() {
    // Pega novamente atendimentos que contem checkin/checkouts sem fechamentos
    const atendimentosAfechar = await dbQuery(/*sql*/ `
            SELECT
                atendimentos.isInstSaude,
                atendimentos.laNome,
                atendimentos.esEspecialidade,
                atendimentos.ocNrContrato,
                atendimentos.clCliente,
                atendimentos.ceTipoPagamento,
                atendimentos.opNrPlantao,
                atendimentos.usCPFUsuarioAprovador,
                atendimentos.opDataDivulgacao,
                atendimentos.opAtivo,
                atendimentos.opTipoFechamento,
                atendimentos.opDataFechamento,
                atendimentos.opPeriodoIni,
                atendimentos.opPeriodoFim,
                atendimentos.psCPF,
                atendimentos.opDatarecebimento,
                atendimentos.opDataAntecipacao,
                atendimentos.opQtdHorasRequisitada,
                atendimentos.opQtdHorasRealizadas,
                atendimentos.opValorFixo,
                atendimentos.opValorHora,
                atendimentos.opValorUnit,
                atendimentos.opChaveAcesso,
                atendimentos.opSituacao,
                atendimentos.dtInclusao,
                atendimentos.dtModificacao,
                atendimentos.opDiaFechamento
                FROM
                capInstSaudePlantao as atendimentos
                JOIN capInstSaudePlantaoAgenda as agendas ON atendimentos.laNome = agendas.laNome
                AND atendimentos.opNrPlantao = agendas.opNrPlantao
                AND atendimentos.ceTipoPagamento = agendas.ceTipoPagamento
                AND atendimentos.clCliente = agendas.clCliente
                AND atendimentos.ocNrContrato = agendas.ocNrContrato
                AND atendimentos.esEspecialidade = agendas.esEspecialidade
                AND atendimentos.isInstSaude = agendas.isInstSaude
                JOIN capOperPlantaoCheck as checks ON atendimentos.laNome = checks.laNome
                AND atendimentos.opNrPlantao = checks.opNrPlantao
                AND atendimentos.ceTipoPagamento = checks.ceTipoPagamento
                AND atendimentos.clCliente = checks.clCliente
                AND atendimentos.ocNrContrato = checks.ocNrContrato
                AND atendimentos.esEspecialidade = checks.esEspecialidade
                AND atendimentos.isInstSaude = checks.isInstSaude
                WHERE
                atendimentos.opSituacao = 'EmExecucao'
                AND atendimentos.opAtivo = 1
                AND atendimentos.opDataFechamento IS NULL
                AND checks.ocCheckin IS NOT NULL
                AND checks.ocCheckOut IS NOT NULL
                AND checks.codFechamento IS NULL
                AND checks.ocCheckAprovado = 1
                GROUP BY
                atendimentos.opNrPlantao
        `);

    if (atendimentosAfechar.length === 0) {
      return;
    }

    for (const atendimentoParaFechar of atendimentosAfechar) {
      const approvedChecks = await this.checkRepository.getApprovedChecks(atendimentoParaFechar);

      if (!approvedChecks.length) {
        continue;
      }

      // Agrupar os checks por período de fechamento para enviar separadamente
      const checksPorPeriodo = {};
      approvedChecks.forEach((check) => {
        if (!checksPorPeriodo[check.periodoFechamento]) {
          checksPorPeriodo[check.periodoFechamento] = [];
        }
        checksPorPeriodo[check.periodoFechamento].push(check);
      });

      console.log(
        `Processando fechamentos para plantão ${atendimentoParaFechar.opNrPlantao} (${atendimentoParaFechar.opTipoFechamento}):`
      );
      for (const periodo in checksPorPeriodo) {
        const checksDestePeriodo = checksPorPeriodo[periodo];

        if (checksDestePeriodo.length > 0) {
          const primeiroDia = new Date(checksDestePeriodo[0].agData).toISOString().split('T')[0];
          const ultimoDia = new Date(checksDestePeriodo[checksDestePeriodo.length - 1].agData)
            .toISOString()
            .split('T')[0];

          console.log(
            `  - Período terminando em ${periodo}: ${checksDestePeriodo.length} registros (${primeiroDia} até ${ultimoDia})`
          );
        } else {
          console.log(
            `  - Período terminando em ${periodo}: ${checksDestePeriodo.length} registros`
          );
        }

        await enviarCheckAprovadosParaFechamento({
          isInstSaude: atendimentoParaFechar.isInstSaude,
          laNome: atendimentoParaFechar.laNome,
          esEspecialidade: atendimentoParaFechar.esEspecialidade,
          ocNrContrato: atendimentoParaFechar.ocNrContrato,
          clCliente: atendimentoParaFechar.clCliente,
          ceTipoPagamento: atendimentoParaFechar.ceTipoPagamento,
          opNrPlantao: atendimentoParaFechar.opNrPlantao,
          checks: checksDestePeriodo,
          psCPF: atendimentoParaFechar.psCPF,
          body: { user: 'system' },
        });
      }
    }
  }
}
