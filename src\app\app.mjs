import fs from 'fs';
import dotenv from 'dotenv';
dotenv.config();
import './GlobalLogger.mjs';
import express from 'express';
import { fileURLToPath } from 'url';
import path from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

import cors from 'cors';
import { errors } from 'celebrate';

app.use(
  cors({
    origin: '*',
    methods: 'GET,POST,PUT,DELETE',
    allowedHeaders: 'Content-Type,Authorization',
    preflightContinue: false,
  })
);
app.use(errors());

app.options('*', cors());

import { baseResponse } from 'capfunctions';

import bancoRouter from './banco.mjs';

app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

const directories = (source) =>
  fs
    .readdirSync(source, {
      withFileTypes: true,
    })
    .reduce((a, c) => {
      c.isDirectory() && a.push(c.name);
      return a;
    }, []);

app.all('*', function (req, res, next) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', '*');
  res.setHeader('Access-Control-Allow-Headers', '*');
  res.setHeader('Access-Control-Allow-Credentials', true);
  next();
});

const handler = async (req, res) => {
  try {
    const endpoint = String(req.params[0].replace('/', '')).toLowerCase();
    const lambdas = directories('src/lambdas');

    const lambda = lambdas.find((l) => String(l).toLowerCase() === endpoint);

    if (req.headers['authorization']) {
      req.headers['Authorization'] = req.headers['authorization'];
    }

    if (lambda) {
      const { handler } = await import(`../lambdas/${lambda}/index.mjs`);
      let response = await handler({
        ...req.body,
        headers: req.headers,
      });
      if (
        response?.statusCode === 404 ||
        String(response?.message).toLowerCase().includes('método não encontrado')
      ) {
        response = await handler({
          headers: req.headers,
          body: req.body,
        });
      }
      return res.send(response);
    }
    return res.send(baseResponse.notFound('Método não encontrado'));
  } catch (error) {
    console.info(error);
    console.info(error.message);
    return res.send(baseResponse.error(error.message));
  }
};

app.use('/banco', bancoRouter);

app.get('*', express.json({ type: '*/*' }), handler);
app.post('*', express.json({ type: '*/*' }), handler);

export { app };
