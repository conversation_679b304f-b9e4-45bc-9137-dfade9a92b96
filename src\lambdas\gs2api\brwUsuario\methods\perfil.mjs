import { baseResponse } from 'capfunctions';

export function _listPerfis(event) {
  console.log('_listPerfis', event);

  const response = [
    { value: 'gestorcliente', label: 'Gestor' },
    { value: 'escalista', label: 'Escalista' },
  ];

  if (Array.isArray(event?.body?.groups)) {
    // Usuario logado tem perfil de gestor capitale
    if (event.body.groups.includes('gestorcapitale')) {
      response.push({ value: 'gestorcapitale', label: 'Gestor Capitale' });
    }
  }

  return baseResponse.ok('', response);
}
