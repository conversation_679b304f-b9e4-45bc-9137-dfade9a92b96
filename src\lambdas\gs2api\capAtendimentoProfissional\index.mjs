import { baseResponse, getUserFromToken, UserGroupTypes } from 'capfunctions';
import {
  _list,
  _solicitarAtendimento,
  _find,
  _listChecks,
  _realizarCheck,
  _listSolicitacoes,
  _agenda,
} from './functions.mjs';
import { _listFechamentos } from './methods/listFechamentos.mjs';

const actions = {
  _list,
  _solicitarAtendimento,
  _find,
  _listChecks,
  _realizarCheck,
  _listSolicitacoes,
  _agenda,
  _listFechamentos,
};

export const handler = async (event) => {
  console.log(event);

  try {
    const userFromToken = await getUserFromToken(event.headers?.Authorization);

    const groupsAllowed = [
      UserGroupTypes.GESTOR_CAPITALE,
      UserGroupTypes.PROFISSIONAL,
      UserGroupTypes.PROF_SAUDE,
    ];
    const isAllowed = groupsAllowed.some((group) => userFromToken?.body.groups.includes(group));

    if (!isAllowed) {
      return baseResponse.unauthorized('Usu<PERSON>rio sem as permissões necessárias.');
    }

    if (!!userFromToken) {
      if (!(event.body?.method in actions || event.method in actions)) {
        return baseResponse.notFound('Método não encontrado');
      }

      return {
        newToken: userFromToken?.body?.newToken,
        ...(await actions[event.body?.method || event.method]({
          ...(event.body || event),
          ...userFromToken,
        })),
      };
    }

    return baseResponse.unauthorized('Token de sessão expirado, por favor, faça login novamente');
  } catch (error) {
    console.log('Erro ao processar a requisição', error);

    return baseResponse.error(
      'Erro ao processar a requisição. CATCH HANDLER capAtendimentoProfissional'
    );
  }
};
