import {
  createResponse,
  execQuery,
  baseResponse,
  getUserFromToken
} from 'capfunctions';


async function _listconselho(event) {
  console.log(event);
  try {
    const query = `SELECT ccConselhoClasse, ccDenominacao
                   FROM capConselhoClasse`;
    const response = await execQuery(query);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}


async function _listbanco(event) {
  console.log(event);
  try {
    const query = `SELECT bcBancoNR, bcBancoNome
                   FROM capBanco`;
    const response = await execQuery(query);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}


async function _listtipopix(event) {
  console.log(event);
  try {
    const query = `SELECT pxPIXTipo, pxPIXDescricao
                   FROM capTipoPIX`;
    const response = await execQuery(query);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}


async function _listtipoassinaturaeletronica(event) {
  console.log(event);
  try {
    const query = `SELECT aeAssEletronicaTipo, aeAssEletronicaDescricao
                   FROM capTipoAssinaturaEletronica`;
    const response = await execQuery(query);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}


async function _listespecialidade(event) {
  console.log(event);
  try {
    const query = `SELECT esEspecialidade
                   FROM capEspecialidade`;
    const response = await execQuery(query);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}

async function _buscaCEP(event) {
  console.log(event);
  try {
    const query = `SELECT * FROM capCEP WHERE cep = ? LIMIT 1`;
    const response = await execQuery(query, [event.cep]);
    if (response.success) return createResponse(200,'Listado com successo',true,response.results[0]);
    return createResponse(500, '', false, null);
  } catch (error) {
    console.log(error);
    return createResponse(500, '', false, null);
  }
}


const actions = { _listconselho, _listbanco, _listtipopix, _listtipoassinaturaeletronica, _listespecialidade, _buscaCEP };

export const handler = async (event) => {
  console.log(JSON.stringify({ event }, null, 3));

  try {
    const userFromToken = await getUserFromToken(event.headers?.Authorization);
    
    if(!!userFromToken){
      if (!(event.body?.method in actions || event.method in actions)) {
        return baseResponse.notFound("Método não encontrado");
      }
  
      return {
        newToken: userFromToken?.body?.newToken,
        ...(await actions[event.body?.method || event.method]({ ...( event.body || event ), ... userFromToken } )),
      };
    }
    
    return baseResponse.unauthorized('Token de sessão expirado, por favor, faça login novamente');
  } catch (error) {
    console.log('Catch no handler', JSON.stringify({ error }, null, 3));

    return baseResponse.error('Erro no servidor');
  }
};
