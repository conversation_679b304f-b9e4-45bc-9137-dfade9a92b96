import { Database } from '../database.mjs';
import { MessageBuilder } from '../utils/messageBuilder.mjs';
import { Validators } from '../utils/validators.mjs';

// State definitions
const STATES = {
  INITIAL: 'INITIAL',
  GREETING: 'GREETING',
  CLINIC_REQUEST: 'CLINIC_REQUEST',
  CLINIC_VALIDATION: 'CLINIC_VALIDATION',
  CLINIC_CONFIRMATION: 'CLINIC_CONFIRMATION',
  HUMAN_CONTACT_NAME: 'HUMAN_CONTACT_NAME',
  HUMAN_CONTACT_EMAIL: 'HUMAN_CONTACT_EMAIL',
  NAME_REQUEST: 'NAME_REQUEST',
  CPF_REQUEST: 'CPF_REQUEST',
  EMAIL_REQUEST: 'EMAIL_REQUEST',
  DATA_CONFIRMATION: 'DATA_CONFIRMATION',
  DATA_CORRECTION: 'DATA_CORRECTION',
  TERMS_PRESENTATION: 'TERMS_PRESENTATION',
  FAQ_MENU: 'FAQ_MENU',
  FAQ_RESPONSE: 'FAQ_RESPONSE',
  TERMS_ACCEPTANCE: 'TERMS_ACCEPTANCE',
  COMPLETION: 'COMPLETION',
  END: 'END',
};

class StateMachine {
  /**
   * @param {Database} database
   * @param {MessageBuilder} messageBuilder
   */
  constructor(database, messageBuilder) {
    this.db = database;
    this.messageBuilder = messageBuilder;
    this.states = STATES;
    this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  async processMessage(sessionId, message, phoneNumber) {
    let session = null;
    try {
      // Sanitize input
      const sanitizedMessage = Validators.sanitizeInput(message);

      // Validate existing session using phone number
      const sessionValidation = await this.validateSessionByPhone(phoneNumber);

      if (sessionValidation.valid) {
        session = sessionValidation.session;
        console.info('Reusing existing session', {
          sessionId: session.id,
          phoneNumber,
          currentState: session.current_state,
        });
      } else {
        // Create new session if none exists, expired, or completed
        const forceNew =
          sessionValidation.reason === 'completed' || sessionValidation.reason === 'expired';
        session = await this.db.createUserSession(phoneNumber, STATES.INITIAL, forceNew);
        console.info('Created new session', {
          sessionId: session.id,
          phoneNumber,
          reason: sessionValidation.reason,
        });
      }

      // Log incoming message AFTER ensuring session exists
      await this.db.logInteraction(session.id, 'INCOMING', sanitizedMessage);

      // Check for exit command
      if (Validators.isExitCommand(sanitizedMessage)) {
        return await this.handleExit(session.id, session);
      }

      // Process based on current state
      const response = await this.processStateTransition(session.id, sanitizedMessage, session);

      // Log outgoing message
      await this.db.logInteraction(session.id, 'OUTGOING', response.message);

      return response;
    } catch (error) {
      console.error('Error processing message:', error);
      return {
        message: 'Desculpe, ocorreu um erro interno. Tente novamente em alguns instantes.',
        nextState: session?.current_state || STATES.INITIAL,
      };
    }
  }

  async processStateTransition(sessionId, message, session) {
    const currentState = session.current_state;
    const sessionData = session.session_data || {};

    switch (currentState) {
      case STATES.INITIAL:
        return await this.handleInitialState(sessionId, message, sessionData);

      case STATES.GREETING:
        return await this.handleGreetingState(sessionId, message, sessionData);

      case STATES.CLINIC_REQUEST:
        return await this.handleClinicRequestState(sessionId, message, sessionData);

      case STATES.CLINIC_VALIDATION:
        return await this.handleClinicValidationState(sessionId, message, sessionData);

      case STATES.CLINIC_CONFIRMATION:
        return await this.handleClinicConfirmationState(sessionId, message, sessionData);

      case STATES.NAME_REQUEST:
        return await this.handleNameRequestState(sessionId, message, sessionData);

      case STATES.CPF_REQUEST:
        return await this.handleCPFRequestState(sessionId, message, sessionData);

      case STATES.EMAIL_REQUEST:
        return await this.handleEmailRequestState(sessionId, message, sessionData);

      case STATES.DATA_CONFIRMATION:
        return await this.handleDataConfirmationState(sessionId, message, sessionData);

      case STATES.DATA_CORRECTION:
        return await this.handleDataCorrectionState(sessionId, message, sessionData);

      case STATES.TERMS_PRESENTATION:
        return await this.handleTermsPresentationState(sessionId, message, sessionData);

      case STATES.FAQ_MENU:
        return await this.handleFAQMenuState(sessionId, message, sessionData);

      case STATES.FAQ_RESPONSE:
        return await this.handleFAQResponseState(sessionId, message, sessionData);

      case STATES.TERMS_ACCEPTANCE:
        return await this.handleTermsAcceptanceState(sessionId, message, sessionData);

      case STATES.HUMAN_CONTACT_NAME:
        return await this.handleHumanContactNameState(sessionId, message, sessionData);

      case STATES.HUMAN_CONTACT_EMAIL:
        return await this.handleHumanContactEmailState(sessionId, message, sessionData);

      case STATES.COMPLETION:
        return await this.handleCompletionState(sessionId, message, sessionData);

      default:
        return await this.handleInitialState(sessionId, message, sessionData);
    }
  }

  async handleExit(sessionId, session) {
    await this.db.updateUserSession(sessionId, STATES.END, session.session_data);
    return {
      message: this.messageBuilder.buildExitMessage(),
      nextState: STATES.END,
    };
  }

  // Validate state transitions
  isValidTransition(fromState, toState) {
    const validTransitions = {
      [STATES.INITIAL]: [STATES.GREETING, STATES.END],
      [STATES.GREETING]: [STATES.CLINIC_REQUEST, STATES.END],
      [STATES.CLINIC_REQUEST]: [STATES.CLINIC_VALIDATION, STATES.HUMAN_CONTACT_NAME, STATES.END],
      [STATES.CLINIC_VALIDATION]: [STATES.CLINIC_CONFIRMATION, STATES.CLINIC_REQUEST],
      [STATES.CLINIC_CONFIRMATION]: [STATES.NAME_REQUEST],
      [STATES.HUMAN_CONTACT_NAME]: [STATES.HUMAN_CONTACT_EMAIL, STATES.END],
      [STATES.HUMAN_CONTACT_EMAIL]: [STATES.END],
      [STATES.NAME_REQUEST]: [STATES.CPF_REQUEST, STATES.END],
      [STATES.CPF_REQUEST]: [STATES.EMAIL_REQUEST, STATES.END],
      [STATES.EMAIL_REQUEST]: [STATES.DATA_CONFIRMATION, STATES.END],
      [STATES.DATA_CONFIRMATION]: [STATES.DATA_CORRECTION, STATES.TERMS_PRESENTATION],
      [STATES.DATA_CORRECTION]: [STATES.NAME_REQUEST, STATES.CPF_REQUEST, STATES.EMAIL_REQUEST],
      [STATES.TERMS_PRESENTATION]: [STATES.FAQ_MENU, STATES.TERMS_ACCEPTANCE],
      [STATES.FAQ_MENU]: [STATES.FAQ_RESPONSE, STATES.TERMS_ACCEPTANCE],
      [STATES.FAQ_RESPONSE]: [STATES.FAQ_MENU, STATES.TERMS_ACCEPTANCE],
      [STATES.TERMS_ACCEPTANCE]: [STATES.COMPLETION],
      [STATES.COMPLETION]: [STATES.END],
      [STATES.END]: [STATES.INITIAL],
    };

    return validTransitions[fromState]?.includes(toState) || false;
  }

  async updateSessionState(sessionId, newState, sessionData) {
    await this.db.updateUserSession(sessionId, newState, sessionData);
  }

  // Session management methods integrated from sessionManager
  async isSessionExpired(session) {
    if (!session || !session.updated_at) {
      return true;
    }

    const now = new Date();
    const sessionTime = new Date(session.updated_at);
    const timeDiff = now - sessionTime;

    return timeDiff > this.sessionTimeout;
  }

  async validateSessionByPhone(phoneNumber) {
    try {
      const session = await this.db.getLatestSessionByPhone(phoneNumber);

      if (!session) {
        console.debug('No session found for phone', { phoneNumber });
        return { valid: false, reason: 'no_session' };
      }

      // Check if session is completed
      if (session.current_state === STATES.COMPLETION || session.current_state === STATES.END) {
        console.info('Session completed for phone', {
          phoneNumber,
          sessionId: session.id,
          state: session.current_state,
        });
        return { valid: false, reason: 'completed' };
      }

      if (await this.isSessionExpired(session)) {
        console.info('Session expired for phone', {
          phoneNumber,
          sessionId: session.id,
          lastActivity: session.updated_at,
        });

        // Clean up expired session
        await this.db.deleteUserSession(session.id);
        return { valid: false, reason: 'expired' };
      }

      return { valid: true, session };
    } catch (error) {
      console.error('Error validating session:', error);
      return { valid: false, reason: 'error' };
    }
  }

  // Legacy method for backward compatibility
  async validateSession(sessionId) {
    try {
      const session = await this.db.getUserSession(sessionId);

      if (!session) {
        console.debug('No session found', { sessionId });
        return { valid: false, reason: 'no_session' };
      }

      if (await this.isSessionExpired(session)) {
        console.info('Session expired', {
          sessionId,
          lastActivity: session.updated_at,
        });

        // Clean up expired session
        await this.db.deleteUserSession(sessionId);
        return { valid: false, reason: 'expired' };
      }

      return { valid: true, session };
    } catch (error) {
      console.error('Session validation failed', {
        sessionId,
        error: error.message,
      });
      return { valid: false, reason: 'error' };
    }
  }

  async cleanupExpiredSessions() {
    try {
      const hoursOld = 24;
      const cleanedCount = await this.db.cleanupOldSessions(hoursOld);

      console.info('Session cleanup completed', {
        cleanedSessions: cleanedCount,
        hoursOld,
      });

      return cleanedCount;
    } catch (error) {
      console.error('Session cleanup failed', {
        error: error.message,
      });
      throw error;
    }
  }

  async resetSession(sessionId, reason = 'user_request') {
    try {
      await this.db.deleteUserSession(sessionId);

      console.info('Session reset', {
        sessionId,
        reason,
      });

      return true;
    } catch (error) {
      console.error('Session reset failed', {
        sessionId,
        reason,
        error: error.message,
      });
      return false;
    }
  }

  async getSessionStats() {
    try {
      const stats = await this.db.executeQuery(`
        SELECT 
          current_state,
          COUNT(*) as count,
          AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)) as avg_duration_minutes
        FROM user_sessions 
        WHERE updated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        GROUP BY current_state
      `);

      return stats;
    } catch (error) {
      console.error('Failed to get session stats', {
        error: error.message,
      });
      return [];
    }
  }

  async getUserJourney(sessionId) {
    try {
      const interactions = await this.db.getInteractionHistory(sessionId, 100);
      const session = await this.db.getUserSession(sessionId);

      return {
        session,
        interactions: interactions.reverse(), // Chronological order
        totalInteractions: interactions.length,
      };
    } catch (error) {
      console.error('Failed to get user journey', {
        sessionId,
        error: error.message,
      });
      return null;
    }
  }

  // Send webhook to external system (Make.com integration)
  async sendUserDataWebhook(userData) {
    try {
      const webhookUrl = 'https://hook.us2.make.com/od4cnvjqcq4n4g923qeq0fqke8vll4u2';

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        console.error('Webhook request failed:', {
          status: response.status,
          statusText: response.statusText,
        });
        return false;
      }

      console.log('User data sent to webhook successfully');
      return true;
    } catch (error) {
      console.error('Error sending webhook:', error);
      return false;
    }
  }

  // State handlers
  async handleInitialState(sessionId, message, sessionData) {
    // Check if message is GS2 trigger
    if (Validators.isGS2Trigger(message)) {
      const newSessionData = {
        ...sessionData,
        startTime: new Date().toISOString(),
      };

      await this.updateSessionState(sessionId, STATES.GREETING, newSessionData);

      return {
        message: this.messageBuilder.buildGreeting(),
        nextState: STATES.GREETING,
      };
    }

    // If not a trigger, stay in initial state
    return {
      message: this.messageBuilder.buildInitialHelp(),
      nextState: STATES.INITIAL,
    };
  }

  async handleGreetingState(sessionId, message, sessionData) {
    // Check user response based on JSON flow
    if (Validators.validateUserResponse(message, ['✅ Claro, vamos lá', 'Claro, vamos lá'])) {
      // First send thank you message
      const thankYouMessage = this.messageBuilder.buildThankYouAndNext();

      await this.updateSessionState(sessionId, STATES.CLINIC_REQUEST, sessionData);

      return {
        message: [thankYouMessage, this.messageBuilder.buildClinicRequest()],
        nextState: STATES.CLINIC_REQUEST,
      };
    }

    if (Validators.validateUserResponse(message, ['⏳ Não, obrigado', 'Não, obrigado'])) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);

      return {
        message: this.messageBuilder.buildThankYouMessage(),
        nextState: STATES.END,
      };
    }

    // Check for exit command
    if (Validators.isExitCommand(message)) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);
      return {
        message: this.messageBuilder.buildExitMessage(),
        nextState: STATES.END,
      };
    }

    // Invalid response, ask again with options
    return {
      message: this.messageBuilder.buildGreetingRepeat(),
      nextState: STATES.GREETING,
    };
  }

  async handleClinicRequestState(sessionId, message, sessionData) {
    // Handle button responses from buildClinicNotFoundWithOptions
    if (Validators.validateUserResponse(message, ['Tentar novamente', 'clinic_try_again'])) {
      // Stay in CLINIC_REQUEST state to allow user to try again
      return {
        message: this.messageBuilder.buildClinicRequest(),
        nextState: STATES.CLINIC_REQUEST,
      };
    }

    if (Validators.validateUserResponse(message, ['Contato humano', 'human_contact_request'])) {
      // Transition to HUMAN_CONTACT_NAME state
      const newSessionData = {
        ...sessionData,
        humanContactData: {
          reason: 'clinic_not_found',
        },
      };

      await this.updateSessionState(sessionId, STATES.HUMAN_CONTACT_NAME, newSessionData);

      return {
        message: this.messageBuilder.buildHumanContactNameRequest(),
        nextState: STATES.HUMAN_CONTACT_NAME,
      };
    }

    if (Validators.validateUserResponse(message, ['Sair', 'exit_conversation'])) {
      // Transition to END state
      await this.updateSessionState(sessionId, STATES.END, sessionData);
      return {
        message: this.messageBuilder.buildExitMessage(),
        nextState: STATES.END,
      };
    }

    if (!Validators.validateClinicName(message)) {
      return {
        message: this.messageBuilder.buildInvalidClinicName(),
        nextState: STATES.CLINIC_REQUEST,
      };
    }

    // Search for clinic
    const clinics = await this.db.findClinic(message);

    if (clinics.length === 0) {
      return {
        message: this.messageBuilder.buildClinicNotFoundWithOptions(),
        nextState: STATES.CLINIC_REQUEST,
      };
    }

    // If multiple clinics found, take the first exact match or closest match
    const clinic = clinics[0];

    const newSessionData = {
      ...sessionData,
      clinicData: {
        id: clinic.id,
        name: clinic.name,
        razaoSocial: clinic.razao_social,
        cnpj: clinic.cnpj,
      },
    };

    await this.updateSessionState(sessionId, STATES.CLINIC_CONFIRMATION, newSessionData);

    return {
      message: this.messageBuilder.buildClinicConfirmation(clinic),
      nextState: STATES.CLINIC_CONFIRMATION,
    };
  }

  async handleClinicValidationState(sessionId, message, sessionData) {
    // This state is handled by handleClinicRequestState
    // Keeping for consistency with design
    return await this.handleClinicRequestState(sessionId, message, sessionData);
  }

  async handleClinicConfirmationState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['Sim, isso mesmo'])) {
      await this.updateSessionState(sessionId, STATES.NAME_REQUEST, sessionData);

      return {
        message: ['Obrigado!', this.messageBuilder.buildNameRequest()],
        nextState: STATES.NAME_REQUEST,
      };
    }

    if (Validators.validateUserResponse(message, ['Não, deve ser engano'])) {
      // Go back to clinic request with specific message
      const newSessionData = { ...sessionData };
      delete newSessionData.clinicData;

      await this.updateSessionState(sessionId, STATES.CLINIC_REQUEST, newSessionData);

      return {
        message:
          'Por favor, escreva novamente o nome da clínica ou hospital que nos indicou.\n\nSe preferir, pode encerrar a conversa escrevendo: **Sair**',
        nextState: STATES.CLINIC_REQUEST,
      };
    }

    // Invalid response
    return {
      message: this.messageBuilder.buildClinicConfirmationRepeat(sessionData.clinicData),
      nextState: STATES.CLINIC_CONFIRMATION,
    };
  }

  async handleNameRequestState(sessionId, message, sessionData) {
    if (!Validators.validateName(message)) {
      return {
        message: this.messageBuilder.buildInvalidName(),
        nextState: STATES.NAME_REQUEST,
      };
    }

    const newSessionData = {
      ...sessionData,
      userData: {
        ...sessionData.userData,
        fullName: message.trim(),
      },
    };

    // If this is a correction (has all required fields), go back to data confirmation
    if (
      sessionData.userData &&
      sessionData.userData.fullName &&
      sessionData.userData.cpf &&
      sessionData.userData.email
    ) {
      await this.updateSessionState(sessionId, STATES.DATA_CONFIRMATION, newSessionData);
      return {
        message: this.messageBuilder.buildDataConfirmation(newSessionData.userData),
        nextState: STATES.DATA_CONFIRMATION,
      };
    }

    await this.updateSessionState(sessionId, STATES.CPF_REQUEST, newSessionData);

    return {
      message: this.messageBuilder.buildCPFRequest(),
      nextState: STATES.CPF_REQUEST,
    };
  }

  async handleCPFRequestState(sessionId, message, sessionData) {
    const sanitizedCPF = Validators.sanitizeCPF(message);

    if (!Validators.validateCPF(sanitizedCPF)) {
      return {
        message: this.messageBuilder.buildInvalidCPF(),
        nextState: STATES.CPF_REQUEST,
      };
    }

    const newSessionData = {
      ...sessionData,
      userData: {
        ...sessionData.userData,
        cpf: sanitizedCPF,
      },
    };

    // If this is a correction (has all required fields), go back to data confirmation
    if (
      sessionData.userData &&
      sessionData.userData.fullName &&
      sessionData.userData.email &&
      sessionData.userData.cpf
    ) {
      await this.updateSessionState(sessionId, STATES.DATA_CONFIRMATION, newSessionData);
      return {
        message: this.messageBuilder.buildDataConfirmation(newSessionData.userData),
        nextState: STATES.DATA_CONFIRMATION,
      };
    }

    await this.updateSessionState(sessionId, STATES.EMAIL_REQUEST, newSessionData);

    return {
      message: this.messageBuilder.buildEmailRequest(),
      nextState: STATES.EMAIL_REQUEST,
    };
  }

  async handleEmailRequestState(sessionId, message, sessionData) {
    if (!Validators.validateEmail(message)) {
      return {
        message: this.messageBuilder.buildInvalidEmail(),
        nextState: STATES.EMAIL_REQUEST,
      };
    }

    const newSessionData = {
      ...sessionData,
      userData: {
        ...sessionData.userData,
        email: message.trim().toLowerCase(),
      },
    };

    await this.updateSessionState(sessionId, STATES.DATA_CONFIRMATION, newSessionData);

    return {
      message: this.messageBuilder.buildDataConfirmation(newSessionData.userData),
      nextState: STATES.DATA_CONFIRMATION,
    };
  }

  async handleDataConfirmationState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['Está certo!'])) {
      await this.updateSessionState(sessionId, STATES.TERMS_PRESENTATION, sessionData);

      return {
        message: this.messageBuilder.buildTermsPresentation(),
        nextState: STATES.TERMS_PRESENTATION,
      };
    }

    if (Validators.validateUserResponse(message, ['Preciso corrigir'])) {
      await this.updateSessionState(sessionId, STATES.DATA_CORRECTION, sessionData);

      return {
        message: this.messageBuilder.buildDataCorrectionMenu(),
        nextState: STATES.DATA_CORRECTION,
      };
    }

    // Invalid response
    return {
      message: this.messageBuilder.buildDataConfirmationRepeat(sessionData.userData),
      nextState: STATES.DATA_CONFIRMATION,
    };
  }

  async handleDataCorrectionState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['Nome'])) {
      await this.updateSessionState(sessionId, STATES.NAME_REQUEST, sessionData);

      return {
        message: '👤 Qual é o seu nome completo?',
        nextState: STATES.NAME_REQUEST,
      };
    }

    if (Validators.validateUserResponse(message, ['CPF'])) {
      await this.updateSessionState(sessionId, STATES.CPF_REQUEST, sessionData);

      return {
        message: '📄 Qual o seu CPF (apenas números)?',
        nextState: STATES.CPF_REQUEST,
      };
    }

    if (Validators.validateUserResponse(message, ['Email'])) {
      await this.updateSessionState(sessionId, STATES.EMAIL_REQUEST, sessionData);

      return {
        message: '📧 Qual seu e-mail para contato?',
        nextState: STATES.EMAIL_REQUEST,
      };
    }

    // Invalid option
    return {
      message: this.messageBuilder.buildDataCorrectionMenuRepeat(),
      nextState: STATES.DATA_CORRECTION,
    };
  }

  async handleTermsPresentationState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['ℹ️ Quero saber mais', 'Quero saber mais'])) {
      await this.updateSessionState(sessionId, STATES.FAQ_MENU, sessionData);

      return {
        message: this.messageBuilder.buildFAQMenu(sessionData.clinicData?.name),
        nextState: STATES.FAQ_MENU,
      };
    }

    if (Validators.validateUserResponse(message, ['✅ Sim, de acordo', 'Sim, de acordo'])) {
      // Save user data to database with terms accepted
      await this.db.saveUserData(sessionId, sessionData.clinicData.id, {
        ...sessionData.userData,
        termsAccepted: true,
      });

      await this.updateSessionState(sessionId, STATES.COMPLETION, sessionData);

      return {
        message: this.messageBuilder.buildCompletion(),
        nextState: STATES.COMPLETION,
      };
    }

    if (Validators.validateUserResponse(message, ['❌ Não, obrigado', 'Não, obrigado'])) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);

      return {
        message: this.messageBuilder.buildThankYouMessage(),
        nextState: STATES.END,
      };
    }

    // Invalid response, show terms presentation again
    return {
      message: this.messageBuilder.buildTermsPresentation(),
      nextState: STATES.TERMS_PRESENTATION,
    };
  }

  async handleFAQMenuState(sessionId, message, sessionData) {
    let faqTopic = null;
    let response = null;

    if (Validators.validateUserResponse(message, ['❓Como funciona?', 'Como funciona?'])) {
      faqTopic = 'como_funciona';
    } else if (
      Validators.validateUserResponse(message, ['💸 Existem custos?', 'Existem custos?'])
    ) {
      faqTopic = 'custos';
    } else if (
      Validators.validateUserResponse(message, ['🔒E segurança de dados?', 'E segurança de dados?'])
    ) {
      faqTopic = 'seguranca';
    } else if (Validators.validateUserResponse(message, ['💰 Como contrato?', 'Como contrato?'])) {
      faqTopic = 'contrato';
    } else if (Validators.validateUserResponse(message, ['📆 Quando recebo?', 'Quando recebo?'])) {
      faqTopic = 'prazo';
    } else if (
      Validators.validateUserResponse(message, [
        '↩️ Voltar para assinar',
        'Voltar para assinar',
        '↩️ Assinar os termos',
        'Assinar os termos',
      ])
    ) {
      await this.updateSessionState(sessionId, STATES.TERMS_PRESENTATION, sessionData);
      return {
        message: this.messageBuilder.buildTermsPresentation(),
        nextState: STATES.TERMS_PRESENTATION,
      };
    }

    if (faqTopic) {
      const newSessionData = {
        ...sessionData,
        currentFAQ: faqTopic,
      };

      await this.updateSessionState(sessionId, STATES.FAQ_RESPONSE, newSessionData);

      const faqResponse = this.messageBuilder.buildFAQResponse(
        faqTopic,
        sessionData.clinicData?.name
      );
      const followUpOptions = this.messageBuilder.buildFAQFollowUp();

      return {
        message: [faqResponse, followUpOptions],
        nextState: STATES.FAQ_RESPONSE,
      };
    }

    // Invalid option
    return {
      message: this.messageBuilder.buildFAQMenu(),
      nextState: STATES.FAQ_MENU,
    };
  }

  async handleFAQResponseState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['ℹ️ Saber mais', 'Saber mais'])) {
      await this.updateSessionState(sessionId, STATES.FAQ_MENU, sessionData);

      return {
        message: this.messageBuilder.buildFAQMenu(),
        nextState: STATES.FAQ_MENU,
      };
    }

    if (Validators.validateUserResponse(message, ['↩️ Assinar os termos', 'Assinar os termos'])) {
      await this.updateSessionState(sessionId, STATES.TERMS_PRESENTATION, sessionData);

      return {
        message: this.messageBuilder.buildTermsPresentation(),
        nextState: STATES.TERMS_PRESENTATION,
      };
    }

    // Default to follow-up options
    return {
      message: this.messageBuilder.buildFAQFollowUp(),
      nextState: STATES.FAQ_RESPONSE,
    };
  }

  async handleTermsAcceptanceState(sessionId, message, sessionData) {
    if (Validators.validateUserResponse(message, ['✅ Sim, de acordo', 'Sim, de acordo'])) {
      // Save user data to database with terms accepted
      await this.db.saveUserData(sessionId, sessionData.clinicData.id, {
        ...sessionData.userData,
        termsAccepted: true,
      });

      await this.updateSessionState(sessionId, STATES.COMPLETION, sessionData);

      return {
        message: this.messageBuilder.buildCompletion(),
        nextState: STATES.COMPLETION,
      };
    }

    if (Validators.validateUserResponse(message, ['❌ Não, obrigado', 'Não, obrigado'])) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);

      return {
        message: this.messageBuilder.buildThankYouMessage(),
        nextState: STATES.END,
      };
    }

    // Invalid response, ask again
    return {
      message: this.messageBuilder.buildTermsAcceptance(sessionData.userData?.fullName),
      nextState: STATES.TERMS_ACCEPTANCE,
    };
  }

  async handleHumanContactNameState(sessionId, message, sessionData) {
    // Check for exit command
    if (Validators.isExitCommand(message)) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);
      return {
        message: this.messageBuilder.buildExitMessage(),
        nextState: STATES.END,
      };
    }

    // Validate name input using existing Validators.validateName
    if (!Validators.validateName(message)) {
      return {
        message: this.messageBuilder.buildInvalidName(),
        nextState: STATES.HUMAN_CONTACT_NAME,
      };
    }

    // Initialize humanContactData if it doesn't exist
    const humanContactData = sessionData.humanContactData || { reason: 'clinic_not_found' };

    // Store name in session data and transition to HUMAN_CONTACT_EMAIL
    const newSessionData = {
      ...sessionData,
      humanContactData: {
        ...humanContactData,
        fullName: message.trim(),
      },
    };

    await this.updateSessionState(sessionId, STATES.HUMAN_CONTACT_EMAIL, newSessionData);

    return {
      message: this.messageBuilder.buildHumanContactEmailRequest(),
      nextState: STATES.HUMAN_CONTACT_EMAIL,
    };
  }

  async handleHumanContactEmailState(sessionId, message, sessionData) {
    // Check for exit command
    if (Validators.isExitCommand(message)) {
      await this.updateSessionState(sessionId, STATES.END, sessionData);
      return {
        message: this.messageBuilder.buildExitMessage(),
        nextState: STATES.END,
      };
    }

    // Validate email input using existing Validators.validateEmail
    if (!Validators.validateEmail(message)) {
      return {
        message: this.messageBuilder.buildInvalidEmail(),
        nextState: STATES.HUMAN_CONTACT_EMAIL,
      };
    }

    // Get session info for phone number
    const session = await this.db.getUserSession(sessionId);
    if (!session) {
      console.error('Session not found for human contact request save');
      return {
        message: this.messageBuilder.buildErrorMessage(),
        nextState: STATES.END,
      };
    }

    // Prepare complete contact request data
    const contactData = {
      fullName: sessionData.humanContactData.fullName,
      email: message.trim().toLowerCase(),
      reason: sessionData.humanContactData.reason || 'clinic_not_found'
    };

    try {
      // Save complete contact request to database using new database methods
      await this.db.saveHumanContactRequest(sessionId, session.phone_number, contactData);

      // Send confirmation message and transition to END state
      await this.updateSessionState(sessionId, STATES.END, sessionData);

      return {
        message: this.messageBuilder.buildHumanContactConfirmation(),
        nextState: STATES.END,
      };
    } catch (error) {
      console.error('Failed to save human contact request:', error);
      
      // Handle database errors by staying in same state and showing error
      return {
        message: 'Desculpe, ocorreu um erro ao salvar sua solicitação. Por favor, tente novamente ou digite "sair" para encerrar.',
        nextState: STATES.HUMAN_CONTACT_EMAIL,
      };
    }
  }

  async handleCompletionState(sessionId, message, sessionData) {
    // Process is complete, any message restarts
    if (Validators.isGS2Trigger(message)) {
      return await this.handleInitialState(sessionId, message, {});
    }

    await this.updateSessionState(sessionId, STATES.END, sessionData);

    return {
      message: this.messageBuilder.buildExitMessage(),
      nextState: STATES.END,
    };
  }
}

export { StateMachine, STATES };
