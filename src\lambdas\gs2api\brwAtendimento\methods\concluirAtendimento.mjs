import {
    baseResponse,
    calculaValorFechamento,
    checkData,
    find,
    insert,
    list,
    plantaoPorSituacao,
    update,
} from 'capfunctions';

export async function _concluirAtendimento(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                ],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        if (await plantaoPorSituacao(event.opNrPlantao, 'Concluido')) {
            return baseResponse.badRequest('Plantão já concluído.');
        }

        const {
            results: { insertId: capCodFechamentoId },
        } = await insert('capCodFechamento', {}, event);

        const aditivo = await find('capAditivoProfSaude', {
            where: {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                opNrPlantao: event.opNrPlantao,
            },
        });

        const plantao = await find('capInstSaudePlantao', {
            columns: ['opPeriodoIni', 'opPeriodoFim'],
            where: {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                opNrPlantao: event.opNrPlantao,
            },
        });

        await insert(
            'capAtendimentoFechamento',
            {
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                ocNrContrato: event.ocNrContrato,
                clCliente: event.clCliente,
                opNrPlantao: event.opNrPlantao,
                afNome: `Fechamento ${capCodFechamentoId} - Plantão: ${event.opNrPlantao}`,
                afIni: plantao.results.opPeriodoIni,
                afFim: plantao.results.opPeriodoFim,
                codFechamento: capCodFechamentoId,
                adSituacao: 'aguardandoAprovacao',
                ceTipoPagamento: event.ceTipoPagamento,
                psCPF: aditivo.results.psCPF,
                adDataSolicitacao: aditivo.results.adDataSolicitacao,
            },
            event
        );

        const checks = await list('capOperPlantaoCheck', {
            where: {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                opNrPlantao: event.opNrPlantao,
                codFechamento: false,
                ceTipoPagamento: event.ceTipoPagamento,
            },
        });

        await Promise.all(
            checks.map((check) => {
                update(
                    'capOperPlantaoCheck',
                    {
                        codFechamento: capCodFechamentoId,
                        ocUsuarioAprovacao: event.body.user,
                        ocSituacao: 'EnviadoAprovacao',
                        ocCheckAprovado: 1,
                    },
                    {
                        isInstSaude: check.isInstSaude,
                        laNome: check.laNome,
                        esEspecialidade: check.esEspecialidade,
                        ocNrContrato: check.ocNrContrato,
                        clCliente: check.clCliente,
                        opNrPlantao: check.opNrPlantao,
                        ceTipoPagamento: check.ceTipoPagamento,
                    },
                    event
                );
            })
        );

        await calculaValorFechamento({
            psCPF: aditivo.results.psCPF,
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            opNrPlantao: event.opNrPlantao,
            codFechamento: capCodFechamentoId,
            ceTipoPagamento: event.ceTipoPagamento,
        });

        const entity = {
            opSituacao: 'Concluido',
        };

        const response = await update(
            'capInstSaudePlantao',
            entity,
            {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                opNrPlantao: event.opNrPlantao,
            },
            event
        );

        if (response.success) {
            return baseResponse.created('Atendimento concluido com sucesso');
        }

        return baseResponse.error('Erro ao concluir atendimento');
    } catch (error) {
        console.log('ERROR _concluir', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}
