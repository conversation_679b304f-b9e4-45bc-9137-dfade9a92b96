import { checkData, baseResponse, dbQuery, formataHora } from 'capfunctions';

const REQUIRED_FIELDS = [
    'isInstSaude',
    'laNome',
    'esEspecialidade',
    'ocNrContrato',
    'clCliente',
    'opNrPlantao',
];

export async function _updateCheckAprovados(event) {
    try {
        if (
            !event.checks ||
            checkData(event, {
                obrigatorios: REQUIRED_FIELDS,
            }) ||
            checkData(event.checks[0], {
                obrigatorios: [
                    ...REQUIRED_FIELDS,
                    'ocCheckIn',
                    'ocQtRealizadas',
                    'ocQtAprovadas',
                    'ocQtGlosadas',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const todosOsChecksSaoDoMesmoPlantao = event.checks.every((check) => {
            return (
                check.isInstSaude === event.isInstSaude &&
                check.laNome === event.laNome &&
                check.esEspecialidade === event.esEspecialidade &&
                check.ocNrContrato === event.ocNrContrato &&
                check.clCliente === event.clCliente &&
                check.opNrPlantao === event.opNrPlantao
            );
        });

        if (!todosOsChecksSaoDoMesmoPlantao) {
            return baseResponse.badRequest(
                'Os checks devem ser do mesmo plantão.'
            );
        }

        await Promise.all(
            event.checks.map(async (check) => {
                await dbQuery(
                    /*sql*/ `
                        UPDATE capOperPlantaoCheck
                        SET ocCheckAprovado = ?
                        WHERE isInstSaude = ?
                        AND laNome = ?
                        AND esEspecialidade = ?
                        AND ocNrContrato = ?
                        AND clCliente = ?
                        AND opNrPlantao = ?
                        AND ocCheckIn = ?
                        AND ocCheckOut = ?
                        AND ceTipoPagamento = ?
                        AND codFechamento IS NULL
                        AND ocUsuarioAprovacao IS NULL; 
                    `,
                    [
                        check.ocCheckAprovado ? 1 : 0,
                        check.isInstSaude,
                        check.laNome,
                        check.esEspecialidade,
                        check.ocNrContrato,
                        check.clCliente,
                        check.opNrPlantao,
                        formataHora(check.ocCheckIn),
                        formataHora(check.ocCheckOut),
                        check.ceTipoPagamento,
                    ]
                );
            })
        );

        return baseResponse.ok('Ok');
    } catch (error) {
        console.log('ERROR _updateCheckAprovados', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}
