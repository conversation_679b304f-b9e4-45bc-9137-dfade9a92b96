import mysql from 'mysql';
import { randomUUID } from 'crypto';

export class Database {
  constructor() {
    this.connection = null;
    this.config = {
      port: process.env.MYSQL_PORT,
      host: process.env.MYSQL_HOST,
      user: process.env.MYSQL_USER,
      password: process.env.MYSQL_PASSWORD,
      database: 'wpphook', // Specific database for wpphook
      multipleStatements: true,
      dateStrings: true,
    };
  }

  async initialize() {
    if (!this.connection) {
      try {
        this.connection = mysql.createConnection(this.config);
        console.log("Database connection created successfully");

        // Test connection
        await new Promise((resolve, reject) => {
          this.connection.connect((error) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
        console.log("Database connection test successful");
      } catch (error) {
        console.error("Failed to initialize database connection:", error);
        throw error;
      }
    }
    return this.connection;
  }

  async executeQuery(query, params = []) {
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        await this.initialize();
        const results = await new Promise((resolve, reject) => {
          this.connection.query(query, params, (error, response) => {
            if (error) {
              reject(error);
            } else {
              resolve(response);
            }
          });
        });
        return results;
      } catch (error) {
        lastError = error;
        console.error(
          `Database query attempt ${attempt} failed:`,
          error.message
        );

        if (attempt < maxRetries) {
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      `Database query failed after ${maxRetries} attempts: ${lastError.message}`
    );
  }

  async close() {
    if (this.connection) {
      await new Promise((resolve) => {
        this.connection.end(() => {
          resolve();
        });
      });
      this.connection = null;
      console.log("Database connection closed");
    }
  }

  // User session operations
  async getUserSession(sessionId) {
    const query = "SELECT * FROM user_sessions WHERE id = ?";
    const results = await this.executeQuery(query, [sessionId]);

    if (results.length === 0) {
      return null;
    }

    const session = results[0];
    // Parse JSON session data
    if (session.session_data) {
      try {
        session.session_data = JSON.parse(session.session_data);
      } catch (error) {
        console.error("Failed to parse session data:", error);
        session.session_data = {};
      }
    }

    return session;
  }

  // Get the latest session for a phone number
  async getLatestSessionByPhone(phoneNumber) {
    const query = `
      SELECT * FROM user_sessions 
      WHERE phone_number = ? 
      ORDER BY updated_at DESC 
      LIMIT 1
    `;
    
    const results = await this.executeQuery(query, [phoneNumber]);

    if (results.length === 0) {
      return null;
    }

    const session = results[0];
    // Parse JSON session data
    if (session.session_data) {
      try {
        session.session_data = JSON.parse(session.session_data);
      } catch (error) {
        console.error("Failed to parse session data:", error);
        session.session_data = {};
      }
    }

    return session;
  }

  async createUserSession(phoneNumber, initialState = "INITIAL", forceNew = false) {
    // Generate a new UUID for this session
    const sessionId = randomUUID();
    
    // Check if we should reuse an existing session
    if (!forceNew) {
      const existingSession = await this.getLatestSessionByPhone(phoneNumber);
      if (existingSession && existingSession.current_state !== 'END' && existingSession.current_state !== 'COMPLETION') {
        // Session is still active, return it
        console.log(`Reusing existing session ${existingSession.id} for phone ${phoneNumber}`);
        return existingSession;
      }
    }

    const sessionData = JSON.stringify({
      sessionId,
      phoneNumber,
      startTime: new Date().toISOString(),
    });

    // Create a new session with UUID as primary key
    const query = `
      INSERT INTO user_sessions (id, phone_number, current_state, session_data) 
      VALUES (?, ?, ?, ?)
    `;

    await this.executeQuery(query, [
      sessionId,
      phoneNumber,
      initialState,
      sessionData,
    ]);

    // Log session creation in history
    await this.logSessionHistory(sessionId, phoneNumber, 'CREATED', null, initialState, sessionData);

    return this.getUserSession(sessionId);
  }

  async updateUserSession(sessionId, state, data = {}) {
    // Get current session for history tracking
    const currentSession = await this.getUserSession(sessionId);
    const oldState = currentSession ? currentSession.current_state : null;

    const query = `
      UPDATE user_sessions 
      SET current_state = ?, session_data = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;

    const sessionData = JSON.stringify(data);
    await this.executeQuery(query, [state, sessionData, sessionId]);

    // Log state change in history
    if (currentSession) {
      await this.logSessionHistory(
        sessionId, 
        currentSession.phone_number, 
        'UPDATED', 
        oldState, 
        state, 
        sessionData
      );
    }

    return this.getUserSession(sessionId);
  }

  async deleteUserSession(sessionId) {
    // Get session info before deletion for history
    const session = await this.getUserSession(sessionId);
    
    const query = "DELETE FROM user_sessions WHERE id = ?";
    await this.executeQuery(query, [sessionId]);

    // Log session expiration in history
    if (session) {
      await this.logSessionHistory(
        sessionId,
        session.phone_number,
        'EXPIRED',
        session.current_state,
        null,
        JSON.stringify(session.session_data)
      );
    }
  }

  // Clinic operations
  async findClinic(name) {
    const query = `
      SELECT * FROM clinics 
      WHERE LOWER(name) LIKE LOWER(?) AND active = TRUE
      ORDER BY name
    `;

    const searchTerm = `%${name.trim()}%`;
    const results = await this.executeQuery(query, [searchTerm]);
    return results;
  }

  async getClinicById(clinicId) {
    const query = "SELECT * FROM clinics WHERE id = ? AND active = TRUE";
    const results = await this.executeQuery(query, [clinicId]);
    return results.length > 0 ? results[0] : null;
  }

  async getAllClinics() {
    const query = "SELECT * FROM clinics WHERE active = TRUE ORDER BY name";
    return await this.executeQuery(query);
  }

  // User data operations
  async saveUserData(sessionId, clinicId, userData) {
    const query = `
      INSERT INTO user_data (session_uuid, clinic_id, full_name, cpf, email, terms_accepted, terms_accepted_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        full_name = VALUES(full_name),
        cpf = VALUES(cpf),
        email = VALUES(email),
        terms_accepted = VALUES(terms_accepted),
        terms_accepted_at = VALUES(terms_accepted_at)
    `;

    const termsAcceptedAt = userData.termsAccepted ? new Date() : null;

    await this.executeQuery(query, [
      sessionId,
      clinicId,
      userData.fullName,
      userData.cpf,
      userData.email,
      userData.termsAccepted || false,
      termsAcceptedAt,
    ]);
  }

  async getUserData(sessionId) {
    const query = `
      SELECT ud.*, c.name as clinic_name, c.razao_social, c.cnpj
      FROM user_data ud
      JOIN clinics c ON ud.clinic_id = c.id
      WHERE ud.session_uuid = ?
    `;

    const results = await this.executeQuery(query, [sessionId]);
    return results.length > 0 ? results[0] : null;
  }

  async updateUserDataField(sessionId, field, value) {
    const allowedFields = ["full_name", "cpf", "email"];
    if (!allowedFields.includes(field)) {
      throw new Error(`Invalid field: ${field}`);
    }

    const query = `UPDATE user_data SET ${field} = ? WHERE session_uuid = ?`;
    await this.executeQuery(query, [value, sessionId]);
  }

  async markTermsAccepted(sessionId) {
    const query = `
      UPDATE user_data 
      SET terms_accepted = TRUE, terms_accepted_at = CURRENT_TIMESTAMP 
      WHERE session_uuid = ?
    `;

    await this.executeQuery(query, [sessionId]);
  }

  // Interaction logging
  async logInteraction(sessionId, messageType, messageContent) {
    const query = `
      INSERT INTO interaction_logs (session_uuid, message_type, message_content, timestamp)
      VALUES (?, ?, ?, CURRENT_TIMESTAMP)
    `;

    if (messageContent && typeof messageContent !== 'string') {
      messageContent = JSON.stringify(messageContent);
    }

    await this.executeQuery(query, [sessionId, messageType, messageContent]);
  }

  async getInteractionHistory(sessionId, limit = 50) {
    const query = `
      SELECT * FROM interaction_logs 
      WHERE session_uuid = ? 
      ORDER BY timestamp DESC 
      LIMIT ?
    `;

    return await this.executeQuery(query, [sessionId, limit]);
  }

  // Cleanup operations
  async cleanupOldSessions(hoursOld = 24) {
    const query = `
      DELETE FROM user_sessions 
      WHERE updated_at < DATE_SUB(NOW(), INTERVAL ? HOUR)
    `;

    const result = await this.executeQuery(query, [hoursOld]);
    console.log(`Cleaned up ${result.affectedRows} old sessions`);
    return result.affectedRows;
  }

  // Session history logging
  async logSessionHistory(sessionId, phoneNumber, actionType, oldState, newState, sessionData) {
    try {
      const query = `
        INSERT INTO session_history 
        (session_uuid, phone_number, action_type, old_state, new_state, session_data)
        VALUES (?, ?, ?, ?, ?, ?)
      `;

      await this.executeQuery(query, [
        sessionId,
        phoneNumber,
        actionType,
        oldState,
        newState,
        sessionData
      ]);
    } catch (error) {
      // Don't fail if history logging fails
      console.error('Failed to log session history:', error);
    }
  }

  // Get all sessions for a phone number (including historical)
  async getAllSessionsForPhone(phoneNumber, limit = 10) {
    const query = `
      SELECT 
        id as session_id,
        phone_number,
        current_state,
        session_data,
        created_at,
        updated_at
      FROM user_sessions
      WHERE phone_number = ?
      ORDER BY created_at DESC
      LIMIT ?
    `;

    const results = await this.executeQuery(query, [phoneNumber, limit]);
    
    // Parse session data for each result
    return results.map(session => {
      if (session.session_data) {
        try {
          session.session_data = JSON.parse(session.session_data);
        } catch (error) {
          session.session_data = {};
        }
      }
      return session;
    });
  }

  // Human contact request operations
  async saveHumanContactRequest(sessionId, phoneNumber, contactData) {
    const maxRetries = 3;
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const query = `
          INSERT INTO human_contact_requests 
          (session_uuid, phone_number, full_name, email, reason, status)
          VALUES (?, ?, ?, ?, ?, ?)
        `;

        const result = await this.executeQuery(query, [
          sessionId,
          phoneNumber,
          contactData.fullName,
          contactData.email,
          contactData.reason || 'clinic_not_found',
          'pending'
        ]);

        return {
          id: result.insertId,
          sessionUuid: sessionId,
          phoneNumber: phoneNumber,
          fullName: contactData.fullName,
          email: contactData.email,
          reason: contactData.reason || 'clinic_not_found',
          status: 'pending',
          createdAt: new Date()
        };
      } catch (error) {
        lastError = error;
        console.error(
          `Human contact request save attempt ${attempt} failed:`,
          error.message
        );

        if (attempt < maxRetries) {
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      `Failed to save human contact request after ${maxRetries} attempts: ${lastError.message}`
    );
  }

  async getHumanContactRequest(sessionId) {
    try {
      const query = `
        SELECT * FROM human_contact_requests 
        WHERE session_uuid = ? 
        ORDER BY created_at DESC 
        LIMIT 1
      `;

      const results = await this.executeQuery(query, [sessionId]);
      return results.length > 0 ? results[0] : null;
    } catch (error) {
      console.error('Failed to retrieve human contact request:', error);
      throw new Error(`Failed to retrieve human contact request: ${error.message}`);
    }
  }

  async updateHumanContactRequestStatus(id, status) {
    const maxRetries = 3;
    let lastError;

    const validStatuses = ['pending', 'contacted', 'resolved'];
    if (!validStatuses.includes(status)) {
      throw new Error(`Invalid status: ${status}. Must be one of: ${validStatuses.join(', ')}`);
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const query = `
          UPDATE human_contact_requests 
          SET status = ?, updated_at = CURRENT_TIMESTAMP 
          WHERE id = ?
        `;

        const result = await this.executeQuery(query, [status, id]);
        
        if (result.affectedRows === 0) {
          throw new Error(`No human contact request found with id: ${id}`);
        }

        return { id, status, updatedAt: new Date() };
      } catch (error) {
        lastError = error;
        console.error(
          `Human contact request status update attempt ${attempt} failed:`,
          error.message
        );

        if (attempt < maxRetries) {
          // Wait before retry with exponential backoff
          const delay = Math.pow(2, attempt) * 1000;
          await new Promise((resolve) => setTimeout(resolve, delay));
        }
      }
    }

    throw new Error(
      `Failed to update human contact request status after ${maxRetries} attempts: ${lastError.message}`
    );
  }

  // Health check
  async healthCheck() {
    try {
      await this.executeQuery("SELECT 1 as health");
      return { status: "healthy", timestamp: new Date().toISOString() };
    } catch (error) {
      return {
        status: "unhealthy",
        error: error.message,
        timestamp: new Date().toISOString(),
      };
    }
  }
}