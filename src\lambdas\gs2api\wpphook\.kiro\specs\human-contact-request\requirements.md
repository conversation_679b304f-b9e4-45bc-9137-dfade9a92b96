# Requirements Document

## Introduction

Esta funcionalidade adiciona uma opção para o usuário solicitar contato humano quando a mensagem de erro "Por favor, tente de novo ou escreva sair para encerrar a conversa." é enviada. Quando o usuário escolher esta opção, será solicitado nome e email, e o atendimento será finalizado após a coleta desses dados.

## Requirements

### Requirement 1

**User Story:** Como usuário que não conseguiu encontrar sua clínica no sistema, eu quero ter a opção de solicitar contato humano, para que alguém possa me ajudar pessoalmente.

#### Acceptance Criteria

1. WHEN a mensagem "Por favor, tente de novo ou escreva sair para encerrar a conversa." é enviada THEN o sistema SHALL incluir uma terceira opção "Solicitar contato humano"
2. WHEN o usuário seleciona "Solicitar contato humano" THEN o sistema SHALL transicionar para um novo estado de coleta de dados de contato
3. WHEN o usuário está no estado de coleta de contato THEN o sistema SHALL solicitar o nome completo primeiro
4. WHEN o usuário fornece um nome válido THEN o sistema SHALL solicitar o email de contato
5. WHEN o usuário fornece um email válido THEN o sistema SHALL salvar os dados de contato e finalizar o atendimento

### Requirement 2

**User Story:** Como administrador do sistema, eu quero que os dados de solicitação de contato sejam salvos no banco de dados, para que possamos fazer o follow-up adequado.

#### Acceptance Criteria

1. WHEN os dados de contato são coletados THEN o sistema SHALL salvar nome, email, telefone e timestamp no banco de dados
2. WHEN os dados são salvos THEN o sistema SHALL incluir o motivo da solicitação como "clinic_not_found"
3. WHEN os dados são salvos THEN o sistema SHALL incluir o sessionId para rastreabilidade
4. WHEN o salvamento é bem-sucedido THEN o sistema SHALL enviar uma mensagem de confirmação ao usuário

### Requirement 3

**User Story:** Como usuário que solicitou contato humano, eu quero receber uma confirmação de que minha solicitação foi registrada, para que eu saiba que alguém entrará em contato comigo.

#### Acceptance Criteria

1. WHEN a solicitação de contato é processada com sucesso THEN o sistema SHALL enviar uma mensagem de confirmação
2. WHEN a mensagem de confirmação é enviada THEN o sistema SHALL informar o prazo estimado para contato
3. WHEN a confirmação é enviada THEN o sistema SHALL transicionar para o estado END
4. WHEN o estado END é atingido THEN o sistema SHALL finalizar a sessão

### Requirement 4

**User Story:** Como usuário, eu quero que a validação dos dados de contato seja consistente com o resto do sistema, para que a experiência seja uniforme.

#### Acceptance Criteria

1. WHEN o usuário fornece um nome THEN o sistema SHALL validar usando as mesmas regras do fluxo principal
2. WHEN o usuário fornece um email THEN o sistema SHALL validar usando as mesmas regras do fluxo principal
3. WHEN dados inválidos são fornecidos THEN o sistema SHALL mostrar mensagens de erro apropriadas
4. WHEN o usuário fornece dados inválidos THEN o sistema SHALL permitir nova tentativa sem sair do fluxo

### Requirement 5

**User Story:** Como desenvolvedor, eu quero que os novos estados sejam integrados ao state machine existente, para que o fluxo seja consistente com a arquitetura atual.

#### Acceptance Criteria

1. WHEN novos estados são adicionados THEN eles SHALL seguir o padrão de nomenclatura existente
2. WHEN transições de estado são definidas THEN elas SHALL ser validadas pelo método isValidTransition
3. WHEN novos handlers são criados THEN eles SHALL seguir o padrão dos handlers existentes
4. WHEN mensagens são construídas THEN elas SHALL usar o MessageBuilder existente