import { baseResponse, getUserFromToken } from 'capfunctions';
import { _processaFechamento } from './functions.mjs';

const actions = { _processaFechamento };

export const handler = async (event) => {
    console.log(event);
    try {
        const processamento = [];

        for (const record of event?.Records) {
            console.log(typeof record.body);

            const recordData = JSON.parse(record.body);

            for (const body of recordData) {
                console.log(body);
                processamento.push(_processaFechamento(body));
            }
        }

        await Promise.all(processamento);

        return { success: true };
    } catch (err) {
        console.log(err);
        return { success: false };
    }
};
