import { baseResponse } from 'capfunctions';
import crypto from 'crypto';
import { DocumentHandler } from './utils/documentHandler.mjs';
import { StateMachine } from './services/stateMachine.mjs';
import { Database } from './database.mjs';
import { MessageBuilder } from './utils/messageBuilder.mjs';

// Global instances for connection reuse
let database = null;
let stateMachine = null;
let messageBuilder = null;

/**
 * @returns {{ database: Database, stateMachine: StateMachine, messageBuilder: MessageBuilder }}
 */
async function initializeComponents() {
  if (!database) {
    database = new Database();
    await database.initialize();
  }

  if (!messageBuilder) {
    messageBuilder = new MessageBuilder();
  }

  if (!stateMachine) {
    stateMachine = new StateMachine(database, messageBuilder);
  }

  return { database, stateMachine, messageBuilder };
}

// Map button/list IDs to expected responses
function mapButtonIdToResponse(buttonId, fallbackTitle) {
  const buttonMap = {
    // Greeting responses
    greeting_yes: '<PERSON><PERSON><PERSON>, vamos lá',
    greeting_no: '<PERSON><PERSON>, obri<PERSON>',

    // Clinic confirmation
    clinic_confirm_yes: 'Sim, isso mesmo',
    clinic_confirm_no: 'Não, deve ser engano',

    // Data confirmation
    data_confirm_yes: 'Está certo!',
    data_confirm_no: 'Preciso corrigir',

    // Data correction
    correct_name: 'Nome',
    correct_cpf: 'CPF',
    correct_email: 'Email',

    // Terms and FAQ
    terms_accept: 'Sim, de acordo',
    terms_reject: 'Não, obrigado',
    terms_faq: 'ℹ️ Quero saber mais',
    faq_more: 'Saber mais',
    faq_back: 'Voltar para assinar',
    faq_back_to_terms: '↩️ Assinar os termos',

    // FAQ topics
    faq_como_funciona: 'Como funciona?',
    faq_custos: 'Existem custos?',
    faq_seguranca: 'E segurança de dados?',
    faq_contrato: 'Como contrato?',
    faq_prazo: 'Quando recebo?',
  };

  return buttonMap[buttonId] || fallbackTitle || buttonId;
}

// Verify WhatsApp webhook signature
function verifyWhatsAppSignature(payload, signature, appSecret) {
  if (!signature || !appSecret) {
    return false;
  }

  try {
    // WhatsApp sends signature as sha256=<hash>
    const expectedSignature = signature.replace('sha256=', '');
    const payloadBody = typeof payload === 'string' ? payload : JSON.stringify(payload);

    const hmac = crypto.createHmac('sha256', appSecret);
    hmac.update(payloadBody, 'utf8');
    const calculatedSignature = hmac.digest('hex');

    return crypto.timingSafeEqual(
      Buffer.from(expectedSignature, 'hex'),
      Buffer.from(calculatedSignature, 'hex')
    );
  } catch (error) {
    console.error('Error verifying WhatsApp signature:', error);
    return false;
  }
}

// Parse WhatsApp webhook payload
function parseWebhookPayload(event) {
  try {
    let body;

    // Handle different event formats
    if (event.body) {
      body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      body = event;
    }

    // Check if this is a WhatsApp Business API webhook
    if (body.object === 'whatsapp_business_account' && body.entry) {
      const entry = body.entry[0];
      const changes = entry.changes[0];

      // Check if this is a status update (not a message)
      if (changes && changes.value && changes.value.statuses) {
        console.log('Status update received, ignoring:', changes.value.statuses[0]);
        return null; // Return null for status updates
      }

      if (changes && changes.value && changes.value.messages && changes.value.messages.length > 0) {
        const messageData = changes.value.messages[0];
        const contacts = changes.value.contacts[0];

        // Extract message content
        let message = '';
        if (messageData.type === 'text' && messageData.text) {
          message = messageData.text.body;
        } else if (messageData.type === 'interactive' && messageData.interactive) {
          // Handle interactive messages (buttons, lists, etc.)
          if (messageData.interactive.type === 'button_reply') {
            const buttonId = messageData.interactive.button_reply.id;
            message = mapButtonIdToResponse(buttonId, messageData.interactive.button_reply.title);
          } else if (messageData.interactive.type === 'list_reply') {
            const listId = messageData.interactive.list_reply.id;
            message = mapButtonIdToResponse(listId, messageData.interactive.list_reply.title);
          }
        }

        const phoneNumber = messageData.from;
        const userId = phoneNumber.replace(/\D/g, ''); // Use phone as user ID

        if (!message || !phoneNumber) {
          throw new Error('Missing required fields: message or phone number');
        }

        return {
          userId,
          phoneNumber,
          message: message.trim(),
          originalPayload: body,
          messageId: messageData.id,
          timestamp: messageData.timestamp,
          profileName: contacts?.profile?.name || '',
        };
      }
    }

    // Fallback for other formats (legacy support)
    const message = body.message || body.text || body.Body || '';
    const phoneNumber = body.from || body.From || body.phone || '';
    const userId = phoneNumber.replace(/\D/g, ''); // Use phone as user ID

    if (!message || !phoneNumber) {
      throw new Error('Missing required fields: message or phone number');
    }

    return {
      userId,
      phoneNumber,
      message: message.trim(),
      originalPayload: body,
    };
  } catch (error) {
    console.error('Error parsing webhook payload:', error);
    console.error('Payload body:', JSON.stringify(event.body || event, null, 2));
    throw new Error('Invalid webhook payload format');
  }
}

// Handle errors gracefully
function handleError(error, context = {}) {
  console.error('Lambda execution error:', {
    error: error.message,
    stack: error.stack,
    context,
  });

  // WhatsApp API requires 200 status even for errors to prevent message retries
  return {
    statusCode: 200,
  };
}

// Send WhatsApp message via Graph API
async function sendWhatsAppMessage(phoneNumber, messageData) {
  const whatsappToken = process.env.WHATSAPP_TOKEN;
  const phoneNumberId = process.env.WHATSAPP_PHONE_NUMBER_ID;

  if (!whatsappToken || !phoneNumberId) {
    console.error('Missing WhatsApp configuration: WHATSAPP_TOKEN or WHATSAPP_PHONE_NUMBER_ID');
    return false;
  }

  try {
    const url = `https://graph.facebook.com/v17.0/${phoneNumberId}/messages`;

    let requestBody = {
      messaging_product: 'whatsapp',
      to: phoneNumber,
    };

    // Handle different message formats
    if (typeof messageData === 'string') {
      // Simple text message
      requestBody.type = 'text';
      requestBody.text = {
        body: messageData,
      };
    } else if (messageData && typeof messageData === 'object') {
      // Interactive message (buttons/lists)
      if (messageData.type === 'interactive') {
        requestBody.type = 'interactive';
        requestBody.interactive = messageData.interactive;
      } else if (messageData.type === 'document') {
        // Document message - handle with DocumentHandler
        try {
          const documentHandler = new DocumentHandler(whatsappToken, phoneNumberId);
          const success = await documentHandler.uploadAndSendDocument(
            phoneNumber,
            messageData.document.link,
            messageData.document.caption || '',
            messageData.document.filename || 'document.pdf'
          );

          if (success) {
            console.log('Document sent successfully via WhatsApp Media API');
            return true;
          } else {
            // Fallback to text message if document upload fails
            console.log('Document upload failed, sending as text message');
            requestBody.type = 'text';
            requestBody.text = {
              body: `📄 Documento: ${
                messageData.document.caption || messageData.document.filename
              }\n\n*Não foi possível enviar o documento PDF. Por favor, solicite o documento por email ou entre em contato conosco.*`,
            };
          }
        } catch (error) {
          console.error('Error handling document message:', error);
          // Fallback to text message
          requestBody.type = 'text';
          requestBody.text = {
            body: `📄 Documento: ${
              messageData.document.caption || messageData.document.filename
            }\n\n*Não foi possível enviar o documento PDF. Por favor, solicite o documento por email ou entre em contato conosco.*`,
          };
        }
      } else {
        // Fallback to text if format is not recognized
        requestBody.type = 'text';
        requestBody.text = {
          body: JSON.stringify(messageData),
        };
      }
    } else {
      console.error('Invalid message format:', messageData);
      return false;
    }

    console.log('Sending WhatsApp message:', {
      to: phoneNumber,
      messageType: requestBody.type,
      interactive: requestBody.type === 'interactive' ? requestBody.interactive.type : 'N/A',
    });

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${whatsappToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    const responseData = await response.json();

    if (!response.ok) {
      console.error('WhatsApp API error:', {
        status: response.status,
        statusText: response.statusText,
        error: responseData,
      });
      return false;
    }

    console.log('WhatsApp message sent successfully:', responseData.messages?.[0]?.id);
    return true;
  } catch (error) {
    console.error('Error sending WhatsApp message:', error);
    return false;
  }
}

// Health check endpoint
function handleHealthCheck() {
  return baseResponse.ok('Chatbot is healthy');
}

// Main Lambda handler
export const handler = async (event) => {
  try {
    console.log('Event received:', event);

    // Handle health check
    if (event.requestContext.http.method === 'GET') {
      if (event.requestContext.http.path === '/health') {
        return handleHealthCheck();
      }

      if (
        event.queryStringParameters?.['hub.mode'] === 'subscribe' &&
        event.queryStringParameters?.['hub.verify_token'] === process.env.WHATSAPP_VERIFY_TOKEN
      ) {
        // WhatsApp webhook verification - return only the challenge value
        return {
          statusCode: 200,
          headers: {
            'Content-Type': 'text/plain',
          },
          body: event.queryStringParameters['hub.challenge'],
        };
      }
    }

    // For POST requests (actual messages), verify signature
    // TODO: Enable signature verification when WHATSAPP_APP_SECRET is configured
    if (event.requestContext.http.method === 'POST') {
      const signature = event.headers['x-hub-signature-256'];
      const appSecret = process.env.WHATSAPP_APP_SECRET;

      if (appSecret && signature) {
        if (!verifyWhatsAppSignature(event.body, signature, appSecret)) {
          console.error('Invalid WhatsApp signature');
          return {
            statusCode: 200, // Return 200 to prevent retries, but ignore invalid messages
          };
        }
      } else {
        console.log('Signature verification skipped - WHATSAPP_APP_SECRET not configured');
      }
    }

    // Parse incoming webhook
    const parsedPayload = parseWebhookPayload(event);

    // If parseWebhookPayload returns null, it's a status update - just acknowledge it
    if (!parsedPayload) {
      console.log('Non-message webhook received (status update), acknowledging');
      return {
        statusCode: 200,
      };
    }

    const { userId, phoneNumber, message } = parsedPayload;

    console.log('Processing message:', {
      userId, // Will be same as phoneNumber for now, kept for backward compatibility
      phoneNumber,
      message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
    });

    // Initialize components
    const { stateMachine } = await initializeComponents();

    // Process message through state machine
    // Use phone number for session management instead of userId for consistency
    const response = await stateMachine.processMessage(phoneNumber, message, phoneNumber);

    console.log('State machine response:', {
      nextState: response.nextState,
      messageLength: response.message.length,
    });

    // Send response via WhatsApp API - handle both single messages and arrays
    let messageSent = false;

    if (Array.isArray(response.message)) {
      // Send multiple messages in sequence
      for (const message of response.message) {
        const sent = await sendWhatsAppMessage(phoneNumber, message);
        if (!sent) {
          console.error('Failed to send one of the WhatsApp messages in array');
        } else {
          messageSent = true;
        }
        // Add small delay between messages
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    } else {
      // Send single message
      messageSent = await sendWhatsAppMessage(phoneNumber, response.message);
      if (!messageSent) {
        console.error('Failed to send WhatsApp message');
      }
    }

    // WhatsApp webhook requires 200 OK response
    return {
      statusCode: 200,
    };
  } catch (error) {
    return handleError(error, {
      event,
    });
  }
};

export { parseWebhookPayload, initializeComponents };
