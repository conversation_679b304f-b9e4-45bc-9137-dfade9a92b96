# Fluxo de Contato Humano - Chatbot GS2

## Visão Geral

O fluxo de contato humano é ativado quando o chatbot não consegue encontrar a clínica informada pelo usuário no banco de dados. Este fluxo permite que o usuário solicite contato direto com a equipe humana para resolver a situação.

## Estados do Fluxo

### 1. CLINIC_REQUEST
- **Trigger**: Usuário informa nome de clínica que não existe no banco de dados
- **Resposta**: Mensagem com 3 opções interativas:
  - "Tentar novamente" - Volta para solicitar nome da clínica
  - "Contato humano" - Inicia fluxo de contato humano
  - "Sair" - Encerra a conversa

### 2. HUMAN_CONTACT_NAME
- **Trigger**: Usuário seleciona "Contato humano"
- **Função**: Solicita nome completo do usuário
- **Validações**:
  - Mínimo 2 caracteres
  - <PERSON><PERSON><PERSON> letras, espaços e pontos
  - Não aceita números ou caracteres especiais
- **Comandos especiais**: "sair" encerra a conversa

### 3. HUMAN_CONTACT_EMAIL
- **Trigger**: Nome válido fornecido
- **Função**: Solicita email de contato
- **Validações**:
  - Formato de email válido
  - Máximo 255 caracteres
- **Comandos especiais**: "sair" encerra a conversa

### 4. END
- **Trigger**: Email válido fornecido
- **Função**: Salva solicitação no banco e confirma registro
- **Ação**: Dados salvos na tabela `human_contact_requests`

## Estrutura de Dados

### Tabela: human_contact_requests
```sql
CREATE TABLE `human_contact_requests` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_uuid` varchar(36) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `reason` varchar(50) NOT NULL DEFAULT 'clinic_not_found',
  `status` varchar(20) NOT NULL DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### Dados Salvos
- **session_uuid**: ID da sessão do usuário
- **phone_number**: Número de telefone do WhatsApp
- **full_name**: Nome completo informado
- **email**: Email de contato
- **reason**: Motivo da solicitação (sempre "clinic_not_found")
- **status**: Status da solicitação (sempre "pending" inicialmente)

## Mensagens do Fluxo

### Clínica não encontrada
```
🔎 Desculpe, mas não encontramos esse nome em nosso sistema.

[Tentar novamente] [Contato humano] [Sair]
```

### Solicitação de nome
```
👤 Por favor, informe seu nome completo para que possamos entrar em contato:
```

### Solicitação de email
```
📧 Agora, por favor, informe seu e-mail de contato:
```

### Confirmação final
```
✅ Perfeito! Sua solicitação de contato foi registrada com sucesso.

🕐 Nossa equipe entrará em contato com você em até 24 horas úteis.

Obrigado e até breve!
```

## Tratamento de Erros

### Nome inválido
```
Por favor, informe um nome válido (apenas letras e espaços, mínimo 2 caracteres).
```

### Email inválido
```
Email inválido. Por favor, informe um endereço de email válido.
```

### Erro no banco de dados
```
Desculpe, ocorreu um erro ao salvar sua solicitação. Por favor, tente novamente ou digite "sair" para encerrar.
```

## Transições de Estado Válidas

```
CLINIC_REQUEST → HUMAN_CONTACT_NAME (usuário seleciona "Contato humano")
HUMAN_CONTACT_NAME → HUMAN_CONTACT_EMAIL (nome válido fornecido)
HUMAN_CONTACT_NAME → END (comando "sair")
HUMAN_CONTACT_EMAIL → END (email válido fornecido ou comando "sair")
```

## Testes Implementados

### Fluxo Completo (testHumanContactFlow)
1. Clínica não encontrada
2. Solicitação de contato humano
3. Fornecimento de nome válido
4. Fornecimento de email válido
5. Confirmação e finalização

### Validações (testHumanContactValidationFlow)
1. Nome muito curto (rejeitado)
2. Nome com números (rejeitado)
3. Nome válido (aceito)
4. Email inválido (rejeitado)
5. Comando de saída (aceito)

## Integração com Sistema Externo

Os dados coletados são salvos no banco de dados e podem ser integrados com sistemas externos através de:
- Consultas diretas à tabela `human_contact_requests`
- Webhooks (se configurados)
- APIs de consulta (se implementadas)

## Monitoramento

Para monitorar o fluxo de contato humano:
1. Verificar logs de sessões no estado `HUMAN_CONTACT_*`
2. Consultar tabela `human_contact_requests` para solicitações pendentes
3. Acompanhar métricas de conversão do fluxo
