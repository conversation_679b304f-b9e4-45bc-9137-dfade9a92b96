import { checkData, baseResponse, dbQuery } from 'capfunctions';

export async function _list(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: ['clCliente'],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    let filter = '';
    const params = [event.clCliente];

    if (event.search) {
      filter += /*sql*/ `
          AND (
            a.usCPFUsuario LIKE ?
            OR a.usNome LIKE ?
            OR a.usEmail LIKE ?
            OR a.usTelefone LIKE ?
          )
        `;
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
    }

    // Pagination parameters
    const page = Number(event.page) || 1;
    const pageSize = Number(event.pageSize) || 25;
    const offset = (page - 1) * pageSize;

    // Prepare multi query combining count and data queries
    const multiQuery = /*sql*/ `
          SELECT COUNT(DISTINCT a.usCPFUsuario) as total
          FROM capUsuario a
          JOIN capProfissionalSaude b ON (a.usCPFUsuario = b.psCPF)
          JOIN capProfSaudeCliente c ON (a.usCPFUsuario = c.psCPF)
          JOIN (SELECT DISTINCT psCPF, esEspecialidade FROM capProfSaudeEspecialidade) d ON (a.usCPFUsuario = d.psCPF)
          WHERE c.clCliente = ?
          ${filter};

          SELECT
            a.usCPFUsuario, 
            a.usNome, 
            a.usNomeMae, 
            a.usTelefone, 
            a.usEmail, 
            a.usCEP, 
            a.usEndereco, 
            a.usNrEnd, 
            a.usComplEnd, 
            a.usBairro, 
            a.usCidade, 
            a.usDatNasc,
            a.usUF, 
            a.usCargo, 
            a.usDepartamento, 
            a.usRegFuncional, 
            a.usAtivo,
            a.psOnboardingPendente,
            b.psCPF, 
            b.psRG, 
            b.psContatoSecundario, 
            b.psCNES, 
            b.psCNS, 
            b.ccConselhoClasse, 
            b.psCCNrReg, 
            b.psCCCidadeReg, 
            b.psCCUFReg, 
            b.psCCOrgEmissor, 
            b.bcBancoNR, 
            b.psAgenciaBanco, 
            b.psContaCorrente, 
            b.psAgenciaDigito, 
            b.psContaCorrenteDigito, 
            b.psContaCorrenteCNPJ, 
            b.pxPIXTipo, 
            b.psPIXChave, 
            b.aeAssEletronicaTipo, 
            b.psAssinaturaEletronica, 
            b.psAtivo,
            c.clCliente,
            JSON_ARRAYAGG(d.esEspecialidade) AS especialidades
          FROM 
              capUsuario a
          JOIN 
              capProfissionalSaude b ON (a.usCPFUsuario = b.psCPF)
          JOIN 
              capProfSaudeCliente c ON (a.usCPFUsuario = c.psCPF)
          JOIN 
              (SELECT DISTINCT psCPF, esEspecialidade FROM capProfSaudeEspecialidade) d ON (a.usCPFUsuario = d.psCPF)
          WHERE 
              c.clCliente = ?
          ${filter}
          GROUP BY
              a.usCPFUsuario
          ORDER BY a.usCPFUsuario
          LIMIT ?
          OFFSET ?;
        `;

    // Duplicate base parameters for both queries and add pagination for the second
    const multiParams = [...params, ...params, pageSize, offset];

    // Execute multi query; result[0] is count, result[1] is data
    const result = await dbQuery(multiQuery, multiParams);
    const total = result[0][0].total;
    const totalPages = Math.ceil(total / pageSize);
    const nextPage = page < totalPages ? page + 1 : null;
    const response = result[1];

    if (response?.length > 0) {
      response.forEach((result) => {
        // Atribuir a string de especialidades a uma variável
        let especialidadesString = result.especialidades;

        // Fazer o parsing da string JSON para obter um array
        let especialidadesArray = JSON.parse(especialidadesString);

        // Substituir a string de especialidades pelo array
        result.especialidades = especialidadesArray;
      });

      return baseResponse.paginated('ok', {
        totalPages,
        currentPage: page,
        nextPage,
        data: response,
      });
    }
    return baseResponse.error('Erro ao listar profissionais');
  } catch (error) {
    console.log('ERROR _list', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}
