import { execQuery, baseResponse, insert, update, lambdaInvoke, dbQuery } from 'capfunctions';
import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import moment from 'moment';

async function _processaFechamento(event) {
    try {
        return await criaAntecipacao(event);
    } catch (err) {
        console.log(err);
        return baseResponse.error('Error ao processar requisição');
    }
}

async function criaAntecipacao(event) {
    const entity = {
        psCPF: event.psCPF,
        ifInstFinanceira: '1',
        anValorSolicitado: event.valorComDesagio.toString(),
        anValorBruto: event.valorSemDesagio.toString(),
    };

    const responseAntecipacao = await insert('capAntecipacao', entity, event);

    if (responseAntecipacao.success) {
        const anNrAntecipacao = responseAntecipacao.results.insertId;

        const [[atendimento], [contrato]] = await dbQuery(
            /*sql*/ `
                SELECT *
                FROM capInstSaudePlantao
                WHERE opNrPlantao IN (?)
                AND clCliente = ?
                ORDER BY dtInclusao DESC
                LIMIT 1;

                SELECT ceQtdDiasMinRecebim
                FROM capInstSaudeContratoEspec
                WHERE isInstSaude = ?
                AND laNome = ?
                AND esEspecialidade = ?
                AND ocNrContrato = ?
                AND clCliente = ?
                AND ceTipoPagamento = ?;
            `,
            [
                event.fechamentos.map((fechamento) => fechamento.opNrPlantao),
                event.clCliente,

                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.ceTipoPagamento,
            ]
        );

        const dataSolicitacao = new Date(event.adDataSolicitacao);
        const dataInicioAtendimento = new Date(atendimento.dtInclusao);

        const antecipacao = await lambdaInvoke('capKuaraIntegracao', {
            method: '_solicitaAntecipacao',
            valor: event.valorSemDesagio,
            cpf: event.psCPF,
            cnpj: event.clCliente,
            taxa: event.taxaDeDesagio,

            numeroDePagamentos: 1,
            tipoDeOperacao: 'Transfer',
            dataDeInicio: moment(dataInicioAtendimento).format('DD-MM-YYYY'),
            diaDoPagamento: dataSolicitacao.getDate(),
            dataPrimeiroPagamento: moment(dataInicioAtendimento).format('YYYY-MM-DD'),
            diaPrimeiroPagamento: contrato.ceQtdDiasMinRecebim,

            anNrAntecipacao
        });

        console.log('Resultado Antecipação: ', antecipacao);

        for (const fechamento of event.fechamentos) {
            const params = {
                anNrAntecipacao,
                isInstSaude: fechamento.isInstSaude,
                laNome: fechamento.laNome,
                esEspecialidade: fechamento.esEspecialidade,
                ocNrContrato: fechamento.ocNrContrato,
                clCliente: fechamento.clCliente,
                opNrPlantao: fechamento.opNrPlantao,
                psCPF: fechamento.psCPF,
                adDataSolicitacao: fechamento.adDataSolicitacao,
                ceTipoPagamento: fechamento.ceTipoPagamento,
            };
    
            await insert('capAditivoProfSaudeAntecipacao', params, event);
        }
        
        try {
            const retgeratitulo = await geratitulo(event, anNrAntecipacao);
            if (retgeratitulo.success && event.fechamentos.length > 0) {

            }
        } catch (error) {
            console.log('Erro ao gerar título', error);

            // O que deve fazer se nao gerar titulo?
        }

        const conditions = event.fechamentos
            .map((fechamento) => {
                return `(
                    isInstSaude = "${fechamento.isInstSaude}" AND 
                    laNome ="${fechamento.laNome}" AND 
                    esEspecialidade ="${fechamento.esEspecialidade}" AND 
                    ocNrContrato = "${fechamento.ocNrContrato}" AND 
                    clCliente ="${fechamento.clCliente}" AND 
                    opNrPlantao = "${fechamento.opNrPlantao}" AND 
                    afNome = "${fechamento.afNome}" AND 
                    afIni = "${fechamento.afIni}" AND 
                    afFim = "${fechamento.afFim}" AND 
                    codFechamento = "${fechamento.codFechamento}") 
                `;
            })
            .join(' OR ');

        const query = /*sql*/ `UPDATE capAtendimentoFechamento
                        set adSituacao = 'antecipacaoSolicitada', anNrAntecipacao = ${anNrAntecipacao}
                       WHERE ${conditions};`;

        const updatedFechamento = await execQuery(query);

        if (event.opTipoPagamento === 'Unitario') {
            const queryAtendimento = /*sql*/`
                UPDATE capInstSaudePlantao
                set opSituacao = 'Concluido'
                WHERE opNrPlantao IN (${event.fechamentos.map((fechamento) => fechamento.opNrPlantao).join(',')});
            `;

            await execQuery(queryAtendimento);
        }

        if (updatedFechamento.success) {
            return baseResponse.ok('Antecipação solicitada com sucesso');
        }
    }

    throw `Erro ao criar antecipação.`;
}

async function geratitulo(event, nrAntecipacao) {
    const sqsClient = new SQSClient({ region: 'us-east-2' });
    console.log('event geratitulo ', event);
    let query;
    let params;

    query = `
        SELECT r.psCPF, c.clNomeCliente, c.clCliente, u.usNome, u.usTelefone, '1975-02-25' DtNasc, u.usEmail
        FROM capCliente c, capProfSaudeCliente r, capUsuario u
        WHERE c.clCliente = ?
            AND r.clCliente = c.clCliente
            AND r.psCPF = ?
            AND r.psCPF = u.usCPFUsuario
    `;
    params = [event.clCliente, event.psCPF];
    const respsacado = await execQuery(query, params);

    if (!respsacado.success) {
        return false;
    }

    const psSacado = JSON.parse(JSON.stringify(respsacado));
    console.log('psSacado ', psSacado);

    query = `
        SELECT u.usCPFUsuario, u.usNome, c.clCliente, c.clNomeCliente, u.usTelefone, '1975-02-25' DtNasc, u.usEmail
        FROM capCliente c, capClienteUsuario r, capUsuario u
        WHERE c.clCliente = ?
            AND r.clCliente = c.clCliente
            AND r.usCPFUsuario = u.usCPFUsuario
            AND r.ucGestor = 1 LIMIT 1
    `;
    params = [event.clCliente];
    const respcli = await execQuery(query, params);

    if (!respcli.success) {
        return false;
    }

    const cliusu = JSON.parse(JSON.stringify(respcli));
    console.log('cliusu ', cliusu.results);

    const titulo = {
        contrato_nr: event.ocNrContrato,
        originadora: cliusu.results[0].clNomeCliente,
        originadoraCNPJCPF: formatarDocumento(psSacado.results[0].clCliente),
        oriTelefone: psSacado.results[0].usTelefone,
        oriDtNasc: psSacado.results[0].DtNasc,
        oriusEmail: psSacado.results[0].usEmail,
        securitizadora: 'Capitale Investment Holding',
        securitizadoraCNPJCPF: '53.639.169/0001-52',
        sacado: psSacado.results[0].usNome,
        sacadoCNPJCPF: formatarDocumento(psSacado.results[0].psCPF),
        clNome: cliusu.results[0].clNomeCliente,
        usTelefone: cliusu.results[0].usTelefone,
        DtNasc: cliusu.results[0].DtNasc,
        usEmail: cliusu.results[0].usEmail,
        responsavel: cliusu.results[0].usNome,
        responsavelCNPJCPF: formatarDocumento(cliusu.results[0].usCPFUsuario),
        aditivo_nr: nrAntecipacao,
        valor: event.valorComDesagio,
        valorBruto: event.valorSemDesagio,
    };

    const queueUrl = 'https://sqs.us-east-2.amazonaws.com/730335345607/titulo.fifo';

    const paramsfila = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(titulo),
        MessageGroupId: 'titulo',
    };
    console.log('params', paramsfila);

    try {
        if (process.env.IS_LOCAL === 'true') {
            console.log('geratitulo', titulo);

            await lambdaInvoke('brocreateaditivo20', {
                Records: [
                    {
                        body: JSON.stringify(titulo),
                    },
                ],
            });

            return { success: true };
        }

        const command = new SendMessageCommand(paramsfila);
        const data = await sqsClient.send(command);
        console.log('Mensagem enviada com sucesso', data.MessageId);
        return { success: true, data };
    } catch (err) {
        console.log('Erro', err);
        throw err;
    }
}

function formatarDocumento(documento) {
    // Remove caracteres não numéricos
    console.log('documento formatar ', documento);
    documento = documento.replace(/\D/g, '');

    // Verifica se é CPF ou CNPJ
    if (documento.length === 11) {
        // CPF
        documento = documento.replace(/(\d{3})(\d)/, '$1.$2');
        documento = documento.replace(/(\d{3})(\d)/, '$1.$2');
        documento = documento.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    } else if (documento.length === 14) {
        // CNPJ
        documento = documento.replace(/(\d{2})(\d)/, '$1.$2');
        documento = documento.replace(/(\d{3})(\d)/, '$1.$2');
        documento = documento.replace(/(\d{3})(\d)/, '$1/$2');
        documento = documento.replace(/(\d{4})(\d{1,2})$/, '$1-$2');
    }

    return documento;
}

export { _processaFechamento };
