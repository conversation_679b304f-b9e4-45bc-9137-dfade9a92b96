# Design Document

## Overview

Esta funcionalidade adiciona um fluxo alternativo para solicitação de contato humano quando o usuário não consegue encontrar sua clínica no sistema. O design integra-se ao state machine existente, adicionando novos estados e transições para capturar dados de contato e finalizar o atendimento.

## Architecture

### State Machine Integration

O design adiciona três novos estados ao state machine existente:

- `HUMAN_CONTACT_REQUEST`: Estado inicial para coleta de dados de contato
- `HUMAN_CONTACT_NAME`: Estado para coleta do nome
- `HUMAN_CONTACT_EMAIL`: Estado para coleta do email

### Flow Diagram

```mermaid
stateDiagram-v2
    CLINIC_REQUEST --> CLINIC_NOT_FOUND : Clínica não encontrada
    CLINIC_NOT_FOUND --> HUMAN_CONTACT_REQUEST : "Solicitar contato humano"
    CLINIC_NOT_FOUND --> CLINIC_REQUEST : "Tentar novamente"
    CLINIC_NOT_FOUND --> END : "Sair"
    
    HUMAN_CONTACT_REQUEST --> HUMAN_CONTACT_NAME : Usuário confirma
    HUMAN_CONTACT_NAME --> HUMAN_CONTACT_EMAIL : Nome válido fornecido
    HUMAN_CONTACT_EMAIL --> END : Email válido fornecido
    
    HUMAN_CONTACT_NAME --> HUMAN_CONTACT_NAME : Nome inválido
    HUMAN_CONTACT_EMAIL --> HUMAN_CONTACT_EMAIL : Email inválido
```

## Components and Interfaces

### 1. MessageBuilder Enhancements

**New Methods:**
- `buildClinicNotFoundWithOptions()`: Substitui `buildClinicNotFound()` com opções de botão
- `buildHumanContactRequest()`: Mensagem inicial para solicitação de contato
- `buildHumanContactNameRequest()`: Solicita nome para contato
- `buildHumanContactEmailRequest()`: Solicita email para contato
- `buildHumanContactConfirmation()`: Confirma que a solicitação foi registrada

### 2. StateMachine Enhancements

**New State Handlers:**
- `handleHumanContactRequestState()`: Processa confirmação de solicitação
- `handleHumanContactNameState()`: Valida e processa nome
- `handleHumanContactEmailState()`: Valida e processa email

**Modified State Handlers:**
- `handleClinicRequestState()`: Atualizado para usar nova mensagem com opções

**New State Transitions:**
```javascript
[STATES.CLINIC_REQUEST]: [STATES.CLINIC_VALIDATION, STATES.HUMAN_CONTACT_REQUEST, STATES.END]
[STATES.HUMAN_CONTACT_REQUEST]: [STATES.HUMAN_CONTACT_NAME, STATES.END]
[STATES.HUMAN_CONTACT_NAME]: [STATES.HUMAN_CONTACT_EMAIL, STATES.END]
[STATES.HUMAN_CONTACT_EMAIL]: [STATES.END]
```

### 3. Database Schema

**New Table: `human_contact_requests`**
```sql
CREATE TABLE human_contact_requests (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_uuid VARCHAR(36) NOT NULL,
  phone_number VARCHAR(20) NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL,
  reason VARCHAR(50) NOT NULL DEFAULT 'clinic_not_found',
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_session_uuid (session_uuid),
  INDEX idx_phone_number (phone_number),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
);
```

**New Database Methods:**
- `saveHumanContactRequest(sessionId, phoneNumber, contactData)`: Salva solicitação de contato
- `getHumanContactRequest(sessionId)`: Recupera solicitação por sessão
- `updateHumanContactRequestStatus(id, status)`: Atualiza status da solicitação

## Data Models

### HumanContactRequest
```javascript
{
  id: number,
  sessionUuid: string,
  phoneNumber: string,
  fullName: string,
  email: string,
  reason: 'clinic_not_found' | 'other',
  status: 'pending' | 'contacted' | 'resolved',
  createdAt: Date,
  updatedAt: Date
}
```

### Session Data Enhancement
```javascript
sessionData: {
  // ... existing fields
  humanContactData: {
    fullName?: string,
    email?: string,
    reason: string
  }
}
```

## Error Handling

### Validation Errors
- **Nome inválido**: Reutiliza `buildInvalidName()` do MessageBuilder existente
- **Email inválido**: Reutiliza `buildInvalidEmail()` do MessageBuilder existente
- **Erro de banco**: Mensagem genérica de erro interno

### State Transition Errors
- **Transição inválida**: Log de erro e retorno ao estado anterior
- **Dados de sessão corrompidos**: Reset da sessão e início do fluxo

### Database Errors
- **Falha ao salvar**: Retry automático (3 tentativas) seguido de mensagem de erro
- **Timeout de conexão**: Fallback para mensagem de erro e finalização da sessão

## Testing Strategy

### Unit Tests
1. **MessageBuilder Tests**
   - Validar formato das novas mensagens
   - Verificar estrutura dos botões interativos
   - Testar personalização com dados do usuário

2. **StateMachine Tests**
   - Testar transições de estado válidas
   - Validar handlers de novos estados
   - Verificar integração com estados existentes

3. **Database Tests**
   - Testar CRUD operations para human_contact_requests
   - Validar constraints e indexes
   - Testar cenários de erro

### Integration Tests
1. **End-to-End Flow**
   - Simular fluxo completo de solicitação de contato
   - Verificar persistência de dados
   - Validar mensagens enviadas

2. **Error Scenarios**
   - Testar comportamento com dados inválidos
   - Simular falhas de banco de dados
   - Verificar recovery de estados inconsistentes

### Performance Tests
- **Database Performance**: Verificar tempo de resposta das queries
- **Memory Usage**: Monitorar uso de memória durante o fluxo
- **Concurrent Users**: Testar múltiplas solicitações simultâneas

## Implementation Considerations

### Backward Compatibility
- Manter compatibilidade com fluxo existente
- Não alterar comportamento de estados existentes
- Preservar estrutura de dados de sessão atual

### Security
- Validar e sanitizar todos os inputs usando `Validators` existente
- Aplicar rate limiting para prevenir spam
- Log de todas as solicitações para auditoria

### Monitoring
- Adicionar métricas para taxa de solicitações de contato
- Monitorar tempo de resposta dos novos endpoints
- Alertas para falhas de salvamento no banco

### Scalability
- Usar indexes apropriados na nova tabela
- Considerar particionamento por data se volume for alto
- Implementar cleanup automático de registros antigos