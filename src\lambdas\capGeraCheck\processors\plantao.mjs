import { AtendimentoRepository } from '../repositories/atendimento.mjs';
import { AtendimentoProcessor } from './atendimento.mjs';
import { FechamentoProcessor } from './fechamento.mjs';

export class PlantaoProcessor {
  /**
   * @param {AtendimentoRepository} atendimentoRepository
   * @param {AtendimentoProcessor} atendimentoProcessor
   * @param {FechamentoProcessor} fechamentoProcessor
   */
  constructor(atendimentoRepository, atendimentoProcessor, fechamentoProcessor) {
    this.atendimentoRepository = atendimentoRepository;
    this.atendimentoProcessor = atendimentoProcessor;
    this.fechamentoProcessor = fechamentoProcessor;
  }

  async process() {
  const atendimentos = await this.atendimentoRepository.getAtendimentos();

    for (const atendimento of atendimentos) {
      await this.atendimentoProcessor.process(atendimento);
    }
    await this.fechamentoProcessor.process();
  }
}
