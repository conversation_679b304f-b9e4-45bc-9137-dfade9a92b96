import { baseResponse, dbQuery, insert } from 'capfunctions';

export async function _listMensageria(event = {}) {
  try {
    if (!event.clCliente) {
      return baseResponse.badRequest('Dados inválidos');
    }

    const query = /*sql*/ `
      SELECT 
        m.*,
        CASE WHEN cm.clCliente IS NOT NULL THEN 1 ELSE 0 END as selected,
        GROUP_CONCAT(cm.cmIncidencia) as incidencias
      FROM capMensageria m
      LEFT JOIN capCliMensagem cm ON (
        m.meCodigo = cm.meCodigo 
        AND cm.clCliente = ?
      )
      GROUP BY m.meCodigo
    `;

    const mensagens = await dbQuery(query, [event.clCliente]);

    return baseResponse.ok(
      'Listado com sucesso',
      mensagens.map((msg) => ({
        ...msg,
        incidencias: msg.incidencias ? msg.incidencias.split(',').map(Number).sort((a, b) => a - b) : [],
      }))
    );
  } catch (error) {
    console.log('Erro ao listar mensageria', error);
    return baseResponse.error('Erro ao listar mensageria');
  }
}

/**
 *
 * @param {{
 *  clCliente: string;
 *  codigo: string;
 *  incidencia: number;
 *  incidencias: number[];
 *  acao: 'incluir' | 'excluir';
 * }} event
 */
export async function _configurarMensageria(event = {}) {
  try {
    if (!event.clCliente || !event.codigo || !event.acao) {
      return baseResponse.badRequest('Dados inválidos');
    }

    const [[modelo], cliMensagens] = await dbQuery(
      /*sql*/ `
        SELECT * FROM capMensageria WHERE meCodigo = ?;
        SELECT * FROM capCliMensagem WHERE meCodigo = ? AND clCliente = ?;
      `,
      [event.codigo, event.codigo, event.clCliente]
    );

    if (!modelo) {
      return baseResponse.notFound('Modelo não encontrado');
    }

    if (event.acao === 'incluir' && modelo.meFrequencia === 'U') {
      if (cliMensagens.length > 0) {
        return baseResponse.badRequest('Modelo já configurado');
      }

      await dbQuery(
        /*sql*/ `
          INSERT INTO capCliMensagem (clCliente, meCodigo, cmIncidencia) VALUES (?, ?, ?);
        `,
        [event.clCliente, event.codigo, 1]
      );

      return baseResponse.ok('Configuração realizada com sucesso');
    }

    if (event.acao === 'incluir' && modelo.meFrequencia !== 'U' && event.incidencias) {
      await dbQuery(
        /*sql*/ `
          DELETE FROM capCliMensagem WHERE clCliente = ? AND meCodigo = ?;
        `,
        [event.clCliente, event.codigo]
      );

      for (const incidencia of event.incidencias) {
        await insert('capCliMensagem', {
          clCliente: event.clCliente,
          meCodigo: event.codigo,
          cmIncidencia: Number(incidencia),
        });
      }

      return baseResponse.ok('Configuração realizada com sucesso');
    }

    if (event.acao === 'excluir' && modelo.meFrequencia === 'U') {
      if (!cliMensagens || cliMensagens.length === 0) {
        return baseResponse.badRequest('Modelo não configurado');
      }

      let query = /*sql*/ `DELETE FROM capCliMensagem WHERE clCliente = ? AND meCodigo = ?`;

      await dbQuery(query, [event.clCliente, event.codigo]);

      return baseResponse.ok('Configuração removida com sucesso');
    }

    if (event.acao === 'excluir' && modelo.meFrequencia !== 'U') {
      if (!cliMensagens || cliMensagens.length === 0) {
        return baseResponse.badRequest('Modelo não configurado');
      }

      let query = /*sql*/ `DELETE FROM capCliMensagem WHERE clCliente = ? AND meCodigo = ?`;

      await dbQuery(query, [event.clCliente, event.codigo]);

      return baseResponse.ok('Configuração removida com sucesso');
    }

    return baseResponse.ok('Nenhuma ação realizada');
  } catch (error) {
    console.log('Erro ao configurar mensageria', error);
    return baseResponse.error('Erro ao configurar mensageria');
  }
}
