{"id": null, "tenantId": "473680", "name": "GS2 - Introdução", "created": null, "flowNodes": [{"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>👋 <PERSON><PERSON><PERSON>, tudo bem?<br>\n<br>\n<PERSON><PERSON><PERSON> por entrar em contato com a Kuará, em parceria com a Capitale Holding (<strong>GS2</strong>).<br>\n<br>\nPodemos começar com algumas perguntas rápidas?</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "skWxAXR", "buttonText": "✅ Claro, vamos lá", "nodeResultId": "main_message-dAXTL"}, {"id": "QfDWSeI", "buttonText": "⏳ Não, obrigado", "nodeResultId": "main_invokeFlow-PgnCO"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-llyYl", "id": "main_buttons-fpONE", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "-31", "posY": "671"}, "isStartNode": true}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>O<PERSON><PERSON>! Esperamos poder contribuir para o seu planejamento financeiro.</p>", "caption": "", "mimeType": ""}], "id": "main_message-dAXTL", "flowNodeType": "Message", "flowNodePosition": {"posX": "1229", "posY": "773"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p><PERSON><PERSON><PERSON> queira encerrar esta conversa, basta escrever: <strong><PERSON><PERSON></strong></p>", "caption": "", "mimeType": ""}], "id": "main_message-AAbqF", "flowNodeType": "Message", "flowNodePosition": {"posX": "1730", "posY": "775"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "yHPjzBI", "flowConditionType": "NotEqual", "variable": "@RespostaLivre", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-qLuXM", "nResultNodeId": "main_invokeFlow-PgnCO"}, "conditionOperator": "None", "id": "main_condition-llyYl", "flowNodeType": "Condition", "flowNodePosition": {"posX": "298", "posY": "1499"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>Podemos começar com algumas perguntas rápidas?</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "FdNAsPN", "buttonText": "✅ Ok, vamos lá", "nodeResultId": "main_message-dAXTL"}, {"id": "ePCtvak", "buttonText": "⏳ Não, obrigado", "nodeResultId": ""}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-llyYl", "id": "main_buttons-qLuXM", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "825", "posY": "1322"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-jNtgc", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "2208.639015821546", "posY": "862.2365553754103"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-PgnCO", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "740", "posY": "1021"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_buttons-fpONEskWxAXR-main_message-dAXTL", "sourceNodeId": "main_buttons-fpONE__skWxAXR", "targetNodeId": "main_message-dAXTL"}, {"id": "reactflow__edge-main_message-dAXTL-main_message-AAbqF", "sourceNodeId": "main_message-dAXTL", "targetNodeId": "main_message-AAbqF"}, {"id": "reactflow__edge-main_buttons-fpONEmain_buttons-fpONE-default-main_condition-llyYl", "sourceNodeId": "main_buttons-fpONE__main_buttons-fpONE-default", "targetNodeId": "main_condition-llyYl"}, {"id": "reactflow__edge-main_condition-llyYltrue-main_buttons-qLuXM", "sourceNodeId": "main_condition-llyYl__true", "targetNodeId": "main_buttons-qLuXM"}, {"id": "reactflow__edge-main_buttons-qLuXMFdNAsPN-main_message-dAXTL", "sourceNodeId": "main_buttons-qLuXM__FdNAsPN", "targetNodeId": "main_message-dAXTL"}, {"id": "reactflow__edge-main_message-AAbqF-main_invokeFlow-jNtgc", "sourceNodeId": "main_message-AAbqF", "targetNodeId": "main_invokeFlow-jNtgc"}, {"id": "reactflow__edge-main_buttons-qLuXMmain_buttons-qLuXM-default-main_condition-llyYl", "sourceNodeId": "main_buttons-qLuXM__main_buttons-qLuXM-default", "targetNodeId": "main_condition-llyYl"}, {"id": "reactflow__edge-main_buttons-fpONEQfDWSeI-main_invokeFlow-PgnCO", "sourceNodeId": "main_buttons-fpONE__QfDWSeI", "targetNodeId": "main_invokeFlow-PgnCO"}, {"id": "reactflow__edge-main_condition-llyYlfalse-main_invokeFlow-PgnCO", "sourceNodeId": "main_condition-llyYl__false", "targetNodeId": "main_invokeFlow-PgnCO"}], "lastUpdated": "2025-07-28T02:14:56.81Z", "isDeleted": false, "transform": {"posX": "348.02945918790056", "posY": "-205.8068912674911", "zoom": "0.5"}, "isPro": true, "channelTypes": ["WA"]}