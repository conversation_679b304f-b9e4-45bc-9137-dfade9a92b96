const {
  storeImageBase64,
  getObjectFromS3,
  getImage,
  lambdaInvoke,
  s3Upload,
  execQuery,
  find,
  
} = require("./functions");

const mysql = require("mysql");

const connOptions = {
  host: '',
  user: '',
  password: '',
  database: '',
  multipleStatements: true,
};



const connection = mysql.createConnection(connOptions);

exports.handler = async function (event, context) {
    try {

//    var evento = JSON.parse(event.Records[0].body);
//    console.log(evento);

    const dataline = formatDate(new Date());
    const datatexto = formatDateTexto(new Date());


    const csvfile = 'capitale_titulos_'+dataline+'.csv';

    var outputFile = 'arquivoscsvbankme/'+csvfile;
    var linha='';

    const Qtitulos = await SelectDB(
    `
    SELECT dataInclusao, titulo, face, DATE_FORMAT(emissao, "%d/%m/%Y") as emissao, DATE_FORMAT(vencimento, "%d/%m/%Y") as vencimento, sacado, CPFsacado, CEPsacado, NRsacado, Complemsacado
    FROM broAntecipacao
    where dataInclusao > str_to_date(? , '%d-%m-%Y')
    group by titulo, face, emissao, vencimento, sacado, CPFsacado, CEPsacado, NRsacado, Complemsacado
    order by dataInclusao
    `,
    [datatexto]
    );
    
    
// precisa fazer o calculo da data de vencimento - inluir na base    
    
    if ( Qtitulos ) {
    var result;
            Object.keys(Qtitulos).forEach(function(key) {
            result = Qtitulos[key];      
            linha = linha + result.titulo+';'+result.face+';'+result.emissao+';'+result.vencimento+';'+result.sacado+';'+result.CPFsacado+';'+result.CEPsacado+';'+result.NRsacado+';'+result.Complemsacado+'\r\n'; 
            });
    } else {
      linha = '';
    }

    console.log('linha: ', linha);
    
    await s3Upload(linha, outputFile);

    const pload = { csvdata: linha,
                    csvfile: csvfile
                  };
                  
    const resp = await lambdaInvoke("broemailcsv", pload);     

    return {
        status: 200,
        message: 'Completed successfully'
    };
    } catch (err) {
        console.error(err.stack);
        throw Error(err.stack);
    } finally {
        console.log('outputFile: ', outputFile);
    }
};

async function SelectDB(query, params) {
  return new Promise(function (resolve, reject) {
    console.log('SelectDB()', { query, params });
    connection.query(query, params, (error, results) => {
      if (error) {
        console.log('Error na função SelectDB()', error);
        reject(false);
      }
//      console.log('results', results);
      if (results.length > 0) {
        resolve(results);
      } else {
        console.log('Nenhum resultado encontrado na função SelectDB()');
        resolve(false);
      }
    });
  });
}


function padTo2Digits(num) {
  return num.toString().padStart(2, '0');
}

function formatDate(date) {
  return (
    [
      date.getFullYear(),
      padTo2Digits(date.getMonth() + 1),
      padTo2Digits(date.getDate()),
    ].join('') +
    '' +
    [
      padTo2Digits(date.getHours()),
      padTo2Digits(date.getMinutes()),
      padTo2Digits(date.getSeconds()),
    ].join('')
  );
}


function formatDateTexto(date) {
  return (
    [
      padTo2Digits(date.getDate()),
      padTo2Digits(date.getMonth() + 1),
      date.getFullYear(),
    ].join('-')
  );
}

