{"id": null, "tenantId": "473680", "name": "GS2 - FAQ - Médicos", "created": null, "flowNodes": [{"interactiveListHeader": {"type": "Text", "text": ""}, "interactiveListBody": "<p>Claro! Aqui estão algumas dúvidas frequentes. Clique abaixo e escolha uma delas para saber mais:</p>\n<p><br></p>", "interactiveListFooter": "", "interactiveListButtonText": "Perguntas Frequentes", "interactiveListSections": [{"id": "fovfYFv", "title": "", "rows": [{"id": "FJuYGPd", "title": "❓Como funciona?", "description": "", "nodeResultId": "main_message-EWyAW"}, {"id": "aThLKQH", "title": "💰 Como contrato?", "description": "", "nodeResultId": "main_message-MavZM"}, {"id": "NtdEBxV", "title": "📆 Quando recebo?", "description": "", "nodeResultId": "main_message-necMP"}, {"id": "NJOTmIk", "title": "💸 Existem custos?", "description": "", "nodeResultId": "main_message-sbAMR"}, {"id": "xJueVLG", "title": "🔒E segurança de dados?", "description": "", "nodeResultId": "main_message-sXGZU"}, {"id": "tjJVMhP", "title": "↩️ Voltar para assinar", "description": "", "nodeResultId": "main_invokeFlow-PjLCK"}]}], "interactiveListUserInputVariable": "RespostaLivre", "interactiveListDefaultNodeResultId": "main_condition-fqCdH", "id": "main_list-s<PERSON><PERSON>", "flowNodeType": "InteractiveList", "flowNodePosition": {"posX": "7937", "posY": "891"}, "isStartNode": true}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>Em parceria com a 👊 @ClinicaHospital &nbsp;e uma instituição financeira, viabilizamos o seu acesso a um produto financeiro exclusivo.<br>\n<br>\nEsse produto consiste na contração de uma operação de crédito 📊, dando em garantia os honorários médicos que você tiver a receber da @ClinicaHospital.</p>", "caption": "", "mimeType": ""}], "id": "main_message-EWyAW", "flowNodeType": "Message", "flowNodePosition": {"posX": "8564", "posY": "933"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>O que gostaria de fazer agora?</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "BRonMyR", "buttonText": "↩️ Assinar os termos", "nodeResultId": "main_invokeFlow-PjLCK"}, {"id": "sCaHnvS", "buttonText": "ℹ️ Saber mais", "nodeResultId": "main_list-s<PERSON><PERSON>"}, {"id": "GGHlVuC", "buttonText": "❌ Fechar a conversa", "nodeResultId": "main_invokeFlow-YTdaW"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-fqCdH", "id": "main_buttons-qgiyQ", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "10151", "posY": "299.4000000000001"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>Após o preenchimento dos dados e aceite dos termos, analisaremos sua habilitação.</p>\n<p><br>\nSe estiver tudo certo, você receberá por WhatsApp uma proposta personalizada 📄 com os valores, taxas e prazos, além de um link para assinatura digital 🔐.<br>\n<br>\nImportante lembrar que poderemos entrar em contato consigo para colher informações adicionais.</p>\n<p><br></p>", "caption": "", "mimeType": ""}], "id": "main_message-MavZM", "flowNodeType": "Message", "flowNodePosition": {"posX": "8990", "posY": "915"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "rMoqmaj", "flowConditionType": "NotEqual", "variable": "@RespostaLivre", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-qgiyQ", "nResultNodeId": "main_invokeFlow-YTdaW"}, "conditionOperator": "None", "id": "main_condition-fqCdH", "flowNodeType": "Condition", "flowNodePosition": {"posX": "8803", "posY": "300.8000000000002"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>📆 Após a assinatura da proposta e do instrumento contratual, os recursos ficam disponíveis em até 48h úteis na sua conta bancária.</p>\n<p><br></p>\n<p>⚠️ Esse prazo considera a conclusão de todas as etapas, incluindo análise da proposta, confirmação pela clínica e envio dos documentos obrigatórios.</p>", "caption": "", "mimeType": ""}], "id": "main_message-necMP", "flowNodeType": "Message", "flowNodePosition": {"posX": "9368", "posY": "997"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>✅ Sim, existem encargos aplicáveis à operação.</p>\n<p><br></p>\n<p>O valor que você receberá será menor do que o total dos seus honorários — essa diferença representa os juros e os custos financeiros envolvidos, que foram definidos previamente com a @ClinicaHospital.</p>\n<p><br></p>\n<p>💡 <strong>Lembrando:</strong> esses encargos serão aplicáveis até à quitação da operação.<br>\n<br>\n<strong>Eventuais atrasos poderão fazer com que o valor do crédito em aberto supere os honorários dados em garantia.</strong></p>", "caption": "", "mimeType": ""}], "id": "main_message-sbAMR", "flowNodeType": "Message", "flowNodePosition": {"posX": "9747", "posY": "975"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>✅ Sim! Todos os dados seguem as normas da Lei Geral de Proteção de Dados (LGPD).</p>\n<p><br></p>\n<p>Usamos seus dados apenas para avaliação e proposta de crédito — nunca compartilhamos com terceiros fora dessa finalidade.&nbsp;</p>", "caption": "", "mimeType": ""}], "id": "main_message-sXGZU", "flowNodeType": "Message", "flowNodePosition": {"posX": "10068", "posY": "1102"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-YTdaW", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "10038.115097669133", "posY": "-44.640914066734695"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-PjLCK", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "10512.115097669133", "posY": "-47.040914066734786"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_list-sciyiFJuYGPd-main_message-EWyAW", "sourceNodeId": "main_list-sciyi__FJuYGPd", "targetNodeId": "main_message-EWyAW"}, {"id": "reactflow__edge-main_buttons-qgiyQsCaHnvS-main_list-sciyi", "sourceNodeId": "main_buttons-qgiyQ__sCaHnvS", "targetNodeId": "main_list-s<PERSON><PERSON>"}, {"id": "reactflow__edge-main_message-EWyAW-main_buttons-qgiyQ", "sourceNodeId": "main_message-EWyAW", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_list-sciyiaThLKQH-main_message-MavZM", "sourceNodeId": "main_list-sciyi__aThLKQH", "targetNodeId": "main_message-MavZM"}, {"id": "reactflow__edge-main_message-MavZM-main_buttons-qgiyQ", "sourceNodeId": "main_message-MavZM", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_buttons-qgiyQmain_buttons-qgiyQ-default-main_condition-fqCdH", "sourceNodeId": "main_buttons-qgiyQ__main_buttons-qgiyQ-default", "targetNodeId": "main_condition-fqCdH"}, {"id": "reactflow__edge-main_list-sciyimain_list-sciyi-default-main_condition-fqCdH", "sourceNodeId": "main_list-sciyi__main_list-sciyi-default", "targetNodeId": "main_condition-fqCdH"}, {"id": "reactflow__edge-main_list-sciyiNtdEBxV-main_message-necMP", "sourceNodeId": "main_list-sciyi__NtdEBxV", "targetNodeId": "main_message-necMP"}, {"id": "reactflow__edge-main_message-necMP-main_buttons-qgiyQ", "sourceNodeId": "main_message-necMP", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_message-sbAMR-main_buttons-qgiyQ", "sourceNodeId": "main_message-sbAMR", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_list-sciyiNJOTmIk-main_message-sbAMR", "sourceNodeId": "main_list-sciyi__NJOTmIk", "targetNodeId": "main_message-sbAMR"}, {"id": "reactflow__edge-main_list-sciyixJueVLG-main_message-sXGZU", "sourceNodeId": "main_list-sciyi__xJueVLG", "targetNodeId": "main_message-sXGZU"}, {"id": "reactflow__edge-main_message-sXGZU-main_buttons-qgiyQ", "sourceNodeId": "main_message-sXGZU", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_condition-fqCdHtrue-main_buttons-qgiyQ", "sourceNodeId": "main_condition-fqCdH__true", "targetNodeId": "main_buttons-qgiyQ"}, {"id": "reactflow__edge-main_condition-fqCdHfalse-main_invokeFlow-YTdaW", "sourceNodeId": "main_condition-fqCdH__false", "targetNodeId": "main_invokeFlow-YTdaW"}, {"id": "reactflow__edge-main_buttons-qgiyQGGHlVuC-main_invokeFlow-YTdaW", "sourceNodeId": "main_buttons-qgiyQ__GGHlVuC", "targetNodeId": "main_invokeFlow-YTdaW"}, {"id": "reactflow__edge-main_buttons-qgiyQBRonMyR-main_invokeFlow-PjLCK", "sourceNodeId": "main_buttons-qgiyQ__BRonMyR", "targetNodeId": "main_invokeFlow-PjLCK"}, {"id": "reactflow__edge-main_list-sciyitjJVMhP-main_invokeFlow-PjLCK", "sourceNodeId": "main_list-sciyi__tjJVMhP", "targetNodeId": "main_invokeFlow-PjLCK"}], "lastUpdated": "2025-07-28T02:09:53.204Z", "isDeleted": false, "transform": {"posX": "-4302.057548834567", "posY": "167.5204570333674", "zoom": "0.5"}, "isPro": true, "channelTypes": ["WA"]}