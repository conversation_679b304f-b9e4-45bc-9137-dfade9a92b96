import bcrypt from 'bcryptjs';
import { handler as brwAuthTokenHandler } from '../brwAuthToken/index.mjs';

import { dbQuery, execQuery, lambdaInvoke, baseResponse } from 'capfunctions';

const onlyNumber = (n) => String(n).replace(/\D/g, '');

export const handler = async (event) => {
  console.log(event);

  const CPF = onlyNumber(event?.username);

  try {
    const [[usuario], [isGestor]] = await dbQuery(
      /*sql*/ `
        SELECT id, usCPFUsuario, usSenha, usEmail, isMaster, psOnboardingPendente FROM capUsuario WHERE usCPFUsuario = ?;
        SELECT 1 FROM brcUsuarioGrupoMenu WHERE usuarioCPF = ? AND gmNome = 'gestorcapitale'
      `,
      [CPF, CPF]
    );

    if (!usuario) {
      return baseResponse.unauthorized('Usuário ou senha invalida');
    }

    const isLogin = bcrypt.compareSync(event.password || '', usuario?.usSenha || '');

    if (!isLogin) {
      return baseResponse.unauthorized('Usuário ou senha invalida');
    }

    if (usuario.isMaster && !isGestor) {
      try {
        await execQuery(
          /*sql*/ `
            INSERT INTO brcUsuarioGrupoMenu (sgCliente, usuarioCPF, sgInstSaude, gmNome)
            SELECT 'caps', ?, 'capitaleholding', 'gestorcapitale'
            WHERE NOT EXISTS (
              SELECT 1 FROM brcUsuarioGrupoMenu 
              WHERE usuarioCPF = ? AND gmNome = 'gestorcapitale'
            );
          `,
          [usuario.usCPFUsuario, usuario.usCPFUsuario]
        );
      } catch (error) {
        console.log('Erro ao inserir o usuario master no grupo de menu', error);
      }
    }

    const tokenResponse = await brwAuthTokenHandler({
      method: '_generate',
      user: usuario.usCPFUsuario,
    });

    if (!tokenResponse.success) {
      console.log('Erro ao tentar gerar o token', { token: tokenResponse });
      return baseResponse.forbidden('Ocorreu algum erro');
    }

    const token = tokenResponse.body;

    const resultUsuario = await execQuery(
      /*sql*/ `
        SELECT
            a.id,
            a.usCPFUsuario as cpf,
            usEmail as email,
            usNome as nome,
            usSenhaNova as resetPassword,
            cu.clCliente,
            cc.clNomeCliente,
            a.psOnboardingPendente,
            a.usAceiteLGPD
          FROM capUsuario a
          LEFT JOIN
            brcUsuarioGrupoMenu c ON (a.usCPFUsuario = c.usuarioCPF)
          LEFT JOIN 
            capClienteUsuario cu ON (a.usCPFUsuario = cu.usCPFUsuario)
          LEFT JOIN 
            capCliente cc on (cc.clCliente = cu.clCliente)
          WHERE a.usCPFUsuario = ?
        `,
      [CPF]
    );

    if (!resultUsuario.success) {
      return baseResponse.unauthorized('Usuario não associado a nenhum tipo');
    }

    const dadosUsuario = resultUsuario.results;

    if (dadosUsuario.length === 0) {
      return baseResponse.unauthorized('Usuario não associado a nenhum tipo');
    }

    const [aceiteTermos] = await dbQuery(
      /*sql*/ `
          SELECT 
            u.id AS usuario_id,
            g.id AS versao_ativa_id,
            g.versao AS versao_ativa,
            g.titulo AS termo_titulo,
            a.termo_versao_id AS ultima_versao_aceita,
            a.aceito_em AS data_ultimo_aceite,
            a.consentimento_lgpd,
            CASE 
                WHEN a.termo_versao_id IS NULL THEN 'Nunca aceitou os termos'
                WHEN a.termo_versao_id != g.id THEN 'Precisa aceitar nova versão'
                ELSE 'Termos em dia'
            END AS status_aceite,
            CASE 
                WHEN a.termo_versao_id IS NULL OR a.termo_versao_id != g.id THEN 1 
                ELSE 0 
            END AS precisa_aceitar
        FROM capUsuario u
        CROSS JOIN gerenciamento_termos_lgpd g
        LEFT JOIN (
            SELECT 
                usuario_id,
                termo_versao_id,
                aceito_em,
                consentimento_lgpd,
                ROW_NUMBER() OVER (PARTITION BY usuario_id ORDER BY aceito_em DESC) as rn
            FROM aceite_termos_lgpd 
            WHERE consentimento_lgpd = 1
        ) a ON u.id = a.usuario_id AND a.rn = 1
        WHERE g.ativo = 1
          AND u.usCPFUsuario = ?
        ORDER BY u.id;
      `,
      [CPF]
    );

    const dadosUsuarioResponse = {
      id: dadosUsuario[0]['id'],
      cpf: CPF,
      email: dadosUsuario[0]['email'],
      nome: dadosUsuario[0]['nome'],
      aceiteLGPD: aceiteTermos?.precisa_aceitar === 0,
      clCliente: '', // O usuario que deve escolher o cliente
      clNomeCliente: '',
      resetPassword: dadosUsuario[0]['resetPassword'],
      psOnboardingPendente: dadosUsuario[0]['psOnboardingPendente'] === 1,
    };

    const [grupos, [minimo], [maximo]] = await dbQuery(
      /*sql*/ `
        SELECT * FROM brcUsuarioGrupoMenu WHERE usuarioCPF = ?;
        SELECT valor FROM capConfiguracao WHERE chave = 'kuara_valor_minimo_solicitacao';
        SELECT valor FROM capConfiguracao WHERE chave = 'kuara_valor_maximo_solicitacao';
      `,
      [CPF]
    );

    console.log('[grupos, [minimo], [maximo]]', [grupos, [minimo], [maximo]]);

    const perfisRes = await dbQuery(
      /*sql*/ `
        SELECT
          d.gmNome AS grupo,
          d.gmDescricao AS perfil,
          g.mrRota as rota,
          g.mrNomeTela AS nomeTela
        FROM
            capUsuario a
                LEFT JOIN
            brcUsuarioGrupoMenu c ON (a.usCPFUsuario = c.usuarioCPF)
                LEFT JOIN
            brcGrupoMenu d ON (c.gmNome = d.gmNome)
                LEFT JOIN
            brcPerfilMenuRotas f ON (f.gmNome = d.gmNome)
                LEFT JOIN
            brcMenuRotas g ON (f.mrNome = g.mrNome)
        WHERE
            a.usCPFUsuario = ?
        AND (g.mrRota is not null AND g.mrRota != '')
        ORDER BY g.mrOrdemMenu DESC
      `,
      [CPF]
    );

    const perfis = Array.isArray(perfisRes)
      ? perfisRes.reduce((acc, curr, index, original) => {
          if (!acc.some((item) => item.grupo === curr.grupo)) {
            acc.push(curr);
          }
          return acc.map((item) => {
            const perfil = {
              grupo: item.grupo,
              perfil: item.perfil,
              rotas: dadosUsuarioResponse.psOnboardingPendente
                ? []
                : original
                    .filter((r) => r.grupo === item.grupo)
                    .map((item) => ({
                      rota: item.rota,
                      nomeTela: item.nomeTela,
                    })),
            };
            delete perfil.rota;
            return perfil;
          });
        }, [])
      : [];

    const data = {
      grupos,
      token,
      dadosUsuario: {
        ...dadosUsuarioResponse,
        perfis,
        kuaraSolicitacaoValorMinimo: minimo?.valor ?? 0,
        kuaraSolicitacaoValorMaximo: maximo?.valor ?? 0,
      },
    };

    return {
      ...baseResponse.ok('Token gerado com sucesso', data),
    };
  } catch (error) {
    console.log('Catch no login', error);
    return baseResponse.error(
      'Ocorreu algum erro, tente novamente mas, se persistir entre em contato'
    );
  }
};
