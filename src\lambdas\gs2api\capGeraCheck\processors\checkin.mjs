import { CheckRepository } from '../repositories/checkin.mjs';

export class CheckProcessor {
  /**
   * @param {CheckRepository} checkRepository
   */
  constructor(checkRepository) {
    this.checkRepository = checkRepository;
  }

  async processAgenda(atendimento, agenda, checkKey, event) {
    // Busca registro de check para esta agenda (se existir)
    const checkRecords = await this.checkRepository.getCheckRecord(atendimento, agenda);

    const startTime = new Date(agenda.agDataIni);
    const endTime = new Date(agenda.agDataFim);

    if (startTime.toString() === 'Invalid Date' || endTime.toString() === 'Invalid Date') {
      console.log('CheckProcessor.processAgenda: Data inválida', {
        'agenda.agDataIni': agenda.agDataIni,
        'agenda.agDataFim': agenda.agDataFim,
      });

      return;
    }

    // Considera as horas a trabalhar já calculadas na query da agenda
    const ocQtRealizadas = agenda.horasTrabalhadas;
    const ocQtAprovadas = agenda.horasTrabalhadas;
    // O intervalo será o valor da agenda (por exemplo, '00:30:00') –
    // esse campo agora é armazenado na tabela de check (ocIntervalo)
    const ocIntervalo = agenda.agIntervalo;

    if (checkRecords.length > 0) {
      // Se já existe um registro, e ainda não possui checkout, atualizamos-o
      const record = checkRecords[0];

      if (!record.ocCheckOut) {
        const where = {
          ocCheckIn: record.ocCheckIn,
          isInstSaude: atendimento.isInstSaude,
          laNome: atendimento.laNome,
          esEspecialidade: atendimento.esEspecialidade,
          ocNrContrato: atendimento.ocNrContrato,
          clCliente: atendimento.clCliente,
          ceTipoPagamento: atendimento.ceTipoPagamento,
          opNrPlantao: atendimento.opNrPlantao,
        };

        await this.checkRepository.updateCheckRecord(
          where,
          {
            ocCheckOut: endTime,
            ocIntervalo: ocIntervalo,
            ocQtRealizadas: ocQtRealizadas,
            ocQtAprovadas: ocQtAprovadas,
            ocQtGlosadas: '00:00:00',
            ocCheckAprovado: 1,
          },
          event
        );
      }

      return;
    }

    // Se nenhum registro existir, insere UM único registro com os dados completos
    await this.checkRepository.insertCheck({
      ...checkKey,
      agData: agenda.agData,
      ocCheckIn: startTime,
      ocCheckOut: endTime,
      ocIntervalo: ocIntervalo,
      ocQtRealizadas: ocQtRealizadas,
      ocQtAprovadas: ocQtAprovadas,
      ocQtGlosadas: '00:00:00',
      ocCheckAprovado: 1,
    });
  }
}
