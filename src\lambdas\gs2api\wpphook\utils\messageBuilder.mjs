import { Validators } from './validators.mjs';

export class MessageBuilder {
  // Initial and greeting messages
  buildInitialHelp() {
    return 'Para iniciar uma conversa sobre os benefícios da parceria com a Capitale Holding (GS2), digite: GS2';
  }

  buildGreeting() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: '👋 Olá, tudo bem?\n\nObrigado por entrar em contato com a Capitale Holding (GS2).\n\nPodemos começar com algumas perguntas rápidas?',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'greeting_yes',
                title: '✅ Claro, vamos lá',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'greeting_no',
                title: '⏳ Não, obrigado',
              },
            },
          ],
        },
      },
    };
  }

  buildGreetingRepeat() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'Por favor, responda com uma das opções:',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'greeting_yes',
                title: '<PERSON><PERSON><PERSON>, vamos lá',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'greeting_no',
                title: '<PERSON><PERSON>, obrigado',
              },
            },
          ],
        },
      },
    };
  }

  // Thank you and next step messages
  buildThankYouAndNext() {
    return 'Obrigado! Esperamos poder contribuir para o seu planejamento financeiro.\n\nCaso queira encerrar esta conversa, basta escrever: **Sair**';
  }

  buildThankYouMessage() {
    return 'Obrigado! Esperamos poder contribuir para o seu planejamento financeiro.';
  }

  // Clinic-related messages
  buildClinicRequest() {
    return '🏥 Qual o nome da clínica ou hospital que nos indicou para você?';
  }

  buildClinicConfirmation(clinic) {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `Você atualmente presta serviços médicos e é remunerado pela:\n\n- Razão Social: ${
            clinic.razao_social
          }\n- CNPJ: ${Validators.formatCNPJ(clinic.cnpj)}`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'clinic_confirm_yes',
                title: 'Sim, isso mesmo',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'clinic_confirm_no',
                title: 'Não, deve ser engano',
              },
            },
          ],
        },
      },
    };
  }

  buildClinicConfirmationRepeat(clinicData) {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `Por favor, confirme se os dados estão corretos:\n\n- Razão Social: ${
            clinicData.razaoSocial
          }\n- CNPJ: ${Validators.formatCNPJ(clinicData.cnpj)}`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'clinic_confirm_yes',
                title: 'Sim, isso mesmo',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'clinic_confirm_no',
                title: 'Não, está incorreto',
              },
            },
          ],
        },
      },
    };
  }

  buildClinicNotFoundWithOptions() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: '🔎 Desculpe, mas não encontramos esse nome em nosso sistema.',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'clinic_try_again',
                title: 'Tentar novamente',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'human_contact_request',
                title: 'Contato humano',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'exit_conversation',
                title: 'Sair',
              },
            },
          ],
        },
      },
    };
  }

  buildInvalidClinicName() {
    return 'Por favor, informe um nome válido da clínica ou hospital.';
  }

  // Human contact request messages
  buildHumanContactRequest() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: '🤝 Entendemos que você gostaria de falar com alguém da nossa equipe.\n\nPara que possamos entrar em contato, precisaremos de algumas informações básicas. Deseja continuar?',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'human_contact_continue',
                title: '✅ Sim, continuar',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'exit_conversation',
                title: '❌ Não, sair',
              },
            },
          ],
        },
      },
    };
  }

  buildHumanContactNameRequest() {
    return '👤 Por favor, informe seu nome completo para que possamos entrar em contato:';
  }

  buildHumanContactEmailRequest() {
    return '📧 Agora, por favor, informe seu e-mail de contato:';
  }

  buildHumanContactConfirmation() {
    return '✅ Perfeito! Sua solicitação de contato foi registrada com sucesso.\n\n🕐 Nossa equipe entrará em contato com você em até 24 horas úteis.\n\nObrigado e até breve!';
  }

  // Personal data collection messages
  buildNameRequest() {
    return 'Vamos precisar que nos informe alguns dados pessoais.\n\n👤 Qual é o seu nome completo, por favor?';
  }

  buildCPFRequest() {
    return '📄 Agora, por favor, informe seu CPF (apenas números).';
  }

  buildEmailRequest() {
    return '📧 Qual seu e-mail para contato?';
  }

  buildInvalidName() {
    return 'Por favor, informe um nome válido (apenas letras e espaços, mínimo 2 caracteres).';
  }

  buildInvalidCPF() {
    return 'CPF inválido. Por favor, informe um CPF válido com 11 dígitos (apenas números).';
  }

  buildInvalidEmail() {
    return 'Email inválido. Por favor, informe um endereço de email válido.';
  }

  // Data confirmation messages
  buildDataConfirmation(userData) {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `✅ Confirme os dados abaixo:\n\n- Nome: ${
            userData.fullName
          }\n- CPF: ${Validators.formatCPF(userData.cpf)}\n- Email: ${userData.email}`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'data_confirm_yes',
                title: 'Está certo!',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'data_confirm_no',
                title: 'Preciso corrigir',
              },
            },
          ],
        },
      },
    };
  }

  buildDataConfirmationRepeat(userData) {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `✅ Confirme os dados abaixo:\n\n- Nome: ${
            userData.fullName
          }\n- CPF: ${Validators.formatCPF(userData.cpf)}\n- Email: ${userData.email}`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'data_confirm_yes',
                title: 'Está certo!',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'data_confirm_no',
                title: 'Preciso corrigir',
              },
            },
          ],
        },
      },
    };
  }

  buildDataCorrectionMenu() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'Qual das informações precisa corrigir?',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'correct_name',
                title: '👤 Nome',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'correct_cpf',
                title: '📄 CPF',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'correct_email',
                title: '📧 Email',
              },
            },
          ],
        },
      },
    };
  }

  buildDataCorrectionMenuRepeat() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'Por favor, selecione uma das opções:',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'correct_name',
                title: '👤 Nome',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'correct_cpf',
                title: '📄 CPF',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'correct_email',
                title: '📧 Email',
              },
            },
          ],
        },
      },
    };
  }

  // Terms and FAQ messages
  buildTermsPresentation() {
    return [
      '📄 Abaixo estão os **Termos e Condições Gerais** da parceria entre nós e a clínica.\n\n**A adesão é opcional e não traz qualquer obrigação imediata.**',
      {
        type: 'document',
        document: {
          link: './GS2 - Introdução/GS2 - Termos e Condições Gerais.pdf',
          caption: 'GS2 - Termos e Condições Gerais.pdf',
          filename: 'GS2 - Termos e Condições Gerais.pdf',
        },
      },
      {
        type: 'interactive',
        interactive: {
          type: 'button',
          body: {
            text: 'Por favor, confirme se está de acordo com os **Termos de Condições Gerais** compartilhados.',
          },
          action: {
            buttons: [
              {
                type: 'reply',
                reply: {
                  id: 'terms_accept',
                  title: '✅ Sim, de acordo',
                },
              },
              {
                type: 'reply',
                reply: {
                  id: 'terms_reject',
                  title: '❌ Não, obrigado',
                },
              },
              {
                type: 'reply',
                reply: {
                  id: 'terms_faq',
                  title: 'ℹ️ Quero saber mais',
                },
              },
            ],
          },
        },
      },
    ];
  }

  buildFAQMenu(clinicName) {
    return {
      type: 'interactive',
      interactive: {
        type: 'list',
        body: {
          text: 'Claro! Aqui estão algumas dúvidas frequentes. Clique abaixo e escolha uma delas para saber mais:',
        },
        action: {
          button: 'Perguntas Frequentes',
          sections: [
            {
              title: '',
              rows: [
                {
                  id: 'faq_como_funciona',
                  title: '❓Como funciona?',
                  description: '',
                },
                {
                  id: 'faq_contrato',
                  title: '💰 Como contrato?',
                  description: '',
                },
                {
                  id: 'faq_prazo',
                  title: '📆 Quando recebo?',
                  description: '',
                },
                {
                  id: 'faq_custos',
                  title: '💸 Existem custos?',
                  description: '',
                },
                {
                  id: 'faq_seguranca',
                  title: '🔒E segurança de dados?',
                  description: '',
                },
                {
                  id: 'faq_back',
                  title: '↩️ Voltar para assinar',
                  description: '',
                },
              ],
            },
          ],
        },
      },
    };
  }

  buildFAQResponse(topic, clinicName = 'clínica') {
    const responses = {
      como_funciona: `Em parceria com 👊 ${clinicName} e uma instituição financeira, viabilizamos o seu acesso a um produto financeiro exclusivo.\n\nEsse produto consiste na contração de uma operação de crédito 📊, dando em garantia os honorários médicos que você tiver a receber da ${clinicName}.`,

      contrato:
        'Após o preenchimento dos dados e aceite dos termos, analisaremos sua habilitação.\n\nSe estiver tudo certo, você receberá por WhatsApp uma proposta personalizada 📄 com os valores, taxas e prazos, além de um link para assinatura digital 🔐.\n\nImportante lembrar que poderemos entrar em contato consigo para colher informações adicionais.',

      prazo:
        '📆 Após a assinatura da proposta e do instrumento contratual, os recursos ficam disponíveis em até 48h úteis na sua conta bancária.\n\n⚠️ Esse prazo considera a conclusão de todas as etapas, incluindo análise da proposta, confirmação pela clínica e envio dos documentos obrigatórios.',

      custos: `✅ Sim, existem encargos aplicáveis à operação.\n\nO valor que você receberá será menor do que o total dos seus honorários — essa diferença representa os juros e os custos financeiros envolvidos, que foram definidos previamente com a ${clinicName}.\n\n💡 **Lembrando:** esses encargos serão aplicáveis até à quitação da operação.\n\n**Eventuais atrasos poderão fazer com que o valor do crédito em aberto supere os honorários dados em garantia.**`,

      seguranca:
        '✅ Sim! Todos os dados seguem as normas da Lei Geral de Proteção de Dados (LGPD).\n\nUsamos seus dados apenas para avaliação e proposta de crédito — nunca compartilhamos com terceiros fora dessa finalidade.',
    };

    return responses[topic] || 'Informação não encontrada.';
  }

  buildFAQFollowUp() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'O que gostaria de fazer agora?',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'faq_back_to_terms',
                title: '↩️ Assinar os termos',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'faq_more',
                title: 'ℹ️ Saber mais',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'exit_conversation',
                title: '❌ Fechar a conversa',
              },
            },
          ],
        },
      },
    };
  }

  // Terms acceptance and completion
  buildTermsAcceptance(userName = '') {
    const greeting = userName ? `**${userName}**, por favor, ` : 'Por favor, ';
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: `${greeting}confirme se está de acordo com os **Termos de Condições Gerais** compartilhados.`,
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'terms_accept',
                title: '✅ Sim, de acordo',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'terms_reject',
                title: '❌ Não, obrigado',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'terms_faq',
                title: 'ℹ️ Quero saber mais',
              },
            },
          ],
        },
      },
    };
  }

  buildTermsRejectionOptions() {
    return {
      type: 'interactive',
      interactive: {
        type: 'button',
        body: {
          text: 'O que gostaria de fazer agora?',
        },
        action: {
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'terms_accept_return',
                title: '↩️ Aceitar os termos',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'terms_faq',
                title: 'ℹ️ Quero saber mais',
              },
            },
            {
              type: 'reply',
              reply: {
                id: 'exit_conversation',
                title: '❌ Fechar a conversa',
              },
            },
          ],
        },
      },
    };
  }

  buildCompletion() {
    return '🎉 *Obrigado!* Em breve entraremos em contato com mais informações e próximos passos.\n\nSe precisar de algo, entre em contato pelo e-mail: <EMAIL>.\n\nAté logo!';
  }

  // Exit and error messages
  buildExitMessage() {
    return 'Ok! Para reiniciar esta conversa, é só escrever aqui: GS2\n\nObrigado e até à próxima!';
  }

  buildErrorMessage() {
    return 'Desculpe, ocorreu um erro interno. Tente novamente em alguns instantes ou digite "sair" para encerrar.';
  }

  buildInvalidResponse() {
    return 'Não entendi sua resposta. Por favor, escolha uma das opções disponíveis.';
  }

  // Helper method to format messages with user name
  personalizeMessage(message, userName) {
    if (userName) {
      return message.replace(/Por favor,/, `${userName}, por favor,`);
    }
    return message;
  }
}
