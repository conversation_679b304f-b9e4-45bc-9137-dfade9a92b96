import { capHandler } from 'capfunctions';
import {
  _insert,
  _update,
  _list,
  _find,
  _forgot,
  _firstlogin,
  _updatePass,
  _aceitarTermosLGPD,
  _getTermosLGPDActive
} from './functions.mjs';
import { _listPerfis } from './methods/perfil.mjs';

export const handler = capHandler({
  _insert,
  _update,
  _list,
  _find,
  _forgot,
  _firstlogin,
  _updatePass,
  _listPerfis,
  _aceitarTermosLGPD,
  _getTermosLGPDActive
});
