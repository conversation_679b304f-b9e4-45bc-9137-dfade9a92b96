import {S3Client, PutObjectCommand, GetObjectCommand, HeadObjectCommand} from "@aws-sdk/client-s3";
import PDFDocument from "pdfkit";

const s3Client = new S3Client({region: 'us-east-2'});
const S3_BUCKET = "ch-capitaleaditivo";

import { SQSClient, SendMessageCommand } from "@aws-sdk/client-sqs";
const sqsClient = new SQSClient({ region: "us-east-2" });

const generatePdf = async(aditivo, topposParam) => {
  return new Promise((resolve, reject) => {
    const doc = new PDFDocument({size: "A4", margin: 50});
    
    doc
      .fillColor("#444444")
      .fontSize(20)
      .text("Capitale Investment Holding", 50, 57, {align: "center"})
      .moveDown();
    
    doc
      .fillColor("#444444")
      .fontSize(12)
      .font("Helvetica-Bold")
      .text("Contrato de Antecipação de Recebíveis", 50, topposParam, {align: "center"});
    
    topposParam = topposParam + 20;
    
    doc
      .strokeColor("#aaaaaa")
      .lineWidth(1)
      .moveTo(50, topposParam + 10)
      .lineTo(550, topposParam + 10)
      .stroke();
    
    topposParam = topposParam + 20;

    doc
      .fontSize(8)
      .font("Helvetica")
      .text("Cliente Número:", 50, topposParam)
      .font("Helvetica-Bold")
      .text(aditivo.aditivo_nr, 150, topposParam)
      .font("Helvetica")
      .text("Declaração de Recebimento Número:", 200, topposParam)
      .font("Helvetica-Bold")
      .text(aditivo.contrato_nr, 400, topposParam)
      .font("Helvetica")
      .text("Sacado:", 50, topposParam + 20)
      .font("Helvetica-Bold")
      .text(aditivo.originadora, 200, topposParam + 20)
      .font("Helvetica")
      .text("CNPJ/CPF:", 350, topposParam + 20)
      .font("Helvetica-Bold")
      .text(aditivo.originadoraCNPJCPF, 400, topposParam + 20)
      .font("Helvetica")
      .text("FIDC:", 50, topposParam + 30)
      .font("Helvetica-Bold")
      .text(aditivo.securitizadora, 200, topposParam + 30)
      .font("Helvetica")
      .text("CNPJ/CPF:", 350, topposParam + 30)
      .font("Helvetica-Bold")
      .text(aditivo.securitizadoraCNPJCPF, 400, topposParam + 30)
      .font("Helvetica")
      .text("Cedente:", 50, topposParam + 40)
      .font("Helvetica-Bold")
      .text(aditivo.sacado, 200, topposParam + 40)
      .font("Helvetica")
      .text("CNPJ/CPF:", 350, topposParam + 40)
      .font("Helvetica-Bold")
      .text(aditivo.sacadoCNPJCPF, 400, topposParam + 40)
      .font("Helvetica")
      .text("Responsável Solidário:", 50, topposParam + 50)
      .font("Helvetica-Bold")
      .text(aditivo.responsavel, 200, topposParam + 50)
      .font("Helvetica")
      .text("CNPJ/CPF:", 350, topposParam + 50)
      .font("Helvetica-Bold")
      .text(aditivo.responsavelCNPJCPF, 400, topposParam + 50)
      .moveDown();
    
    doc
      .strokeColor("#aaaaaa")
      .lineWidth(1)
      .moveTo(50, topposParam + 70)
      .lineTo(550, topposParam + 70)
      .stroke();
    
    topposParam = topposParam + 70;
    
    topposParam = topposParam + 20;
    
    const formatter = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    });
    
    const vlrbruto = formatter.format(aditivo.valorBruto);
    const vlrliq = formatter.format(aditivo.valor);
    
    const texto = `${vlrliq}, que será depositado para a ORIGINADORA na conta a ser fornecida pelo SACADO no prazo de até 24 horas (ou dia útil imediatamente subsequente ao registro das operações), em contrapartida ao valor de face do título performado a ser recolhido pelo FIDC de ${vlrbruto}, em conformidade com os termos e condições constante do INSTRUMENTO PARTICULAR DE CONTRATO DE CESSÃO FIDUCIÁRIA DE CRÉDITOS firmado entre a ORIGINADORA, RESPONSÁVEL SOLIDÁRIO, FIDC e SACADO (“CONTRATO”), cujo DESÁGIO PADRÃO está estabelecido em cinco por cento do volume financeiro de cada operação referente ao prazo base um mês de antecipação. Caso o período de antecipação seja superior, será realizado a correção referente aos dias adicionais com base no DESÁGIO PADRÃO.`;
    const texto2 = '1.  Nos termos das Cláusula 2a e 4a do CONTRATO firmado entre as PARTES, a ORIGINADORA deverá ratificar abaixo pela mera antecipação de recebíveis desta DECLARAÇÃO DE RECEBIMENTO mantendo-se, portanto, responsável por sua liquidação, salvo hipóteses contratuais de vícios na constituição e existência do título performado).';
    const texto3 = '                           (x) Securitização dos Recebíveis (com responsável) ( ) Cessão do Crédito (sem responsável)';
    const texto4 = '    1.1  A ORIGINADORA declara ter recebido neste ato o valor líquido constante neste termo, à vista, compreendendo e concordando em sua totalidade com os valores e taxas que restaram praticadas, conferindo ao FIDC plena, rasa e geral quitação.';
    const texto5 = '2.  Haja vista a cessão e transferência do(s) crédito(s) identificado(s) acima, SACADO e RESPONSÁVEL SOLIDÁRIO, na qualidade de responsável principal e solidários da obrigação, prestaram endosso ao(s) título(s) de crédito ora cedido(s), conforme consta no CONTRATO, para todos os efeitos, valendo a assinatura de ambos naquele instrumento como forma expressa de aceitação deste Termo de Cessão e como prova de que ocorreu o endosso do(s) título(s) performado(s) aqui cedido(s) à CESSIONÁRIA/FIDC, passando este Termo de Cessão a integrar o CONTRATO na forma de anexo.';
    const texto6 = '3.  A ORIGINADORA declara, neste ato, que os Direitos de Crédito ora cedidos estão livres e desembaraçados de quaisquer ônus, gravames ou restrições de qualquer natureza.';
    const texto7 = '4.  Pela assinatura do presente, ocasião em que ambas as partes declaram e concordam, para todos os fins de direito, na forma dos artigos 107, 219 e 220, todos do Código Civil Brasileiro, que o presente instrumento, juntamente com o CONTRATO, cujos termos e condições são ora ratificados, representam a integralidade dos termos entre elas acordados, bem como, nos termos do art. 10, § 2º, da Medida Provisória nº 2.200-2, expressamente concordam em utilizar e reconhecem como válida qualquer forma de comprovação de anuência ao presente instrumento, em formato eletrônico, se valendo ou não de certificado digital emitido no padrão ICP-Brasil, incluindo assinaturas eletrônicas. A formalização das avenças na maneira acima mencionada será suficiente para a validade e integral vinculação das partes ao presente Contrato.';
    const texto8 = '5.  O presente instrumento constitui título executivo extrajudicial, nos termos do artigo 585 ,II do Código de Processo Civil.';
    const texto9 = `SÃO PAULO/SP, ${formatDateExt(new Date())}`;
    
    doc
      .fontSize(8)
      .font("Helvetica")
      .strokeColor("#444444")
      .lineWidth(1)
      .text('Por esta DECLARAÇÃO DE RECEBIMENTO, as PARTES formalizam a operação dos créditos discriminados pela importância líquida de', 50, topposParam)
      .text(texto, 50, topposParam + 10, {align: "justify"})
      .text(texto2, 50, topposParam + 80, {align: "justify"})
      .text(texto3, 50, topposParam + 120, {align: "justify"})
      .text(texto4, 50, topposParam + 140, {align: "justify"})
      .text(texto5, 50, topposParam + 170, {align: "justify"})
      .text(texto6, 50, topposParam + 230, {align: "justify"})
      .text(texto7, 50, topposParam + 260, {align: "justify"})
      .text(texto8, 50, topposParam + 340, {align: "justify"})
      .text(texto9, 50, topposParam + 360)
      .moveTo(50, topposParam + 410)
      .lineTo(250, topposParam + 410)
      .stroke()
      .moveTo(350, topposParam + 410)
      .lineTo(550, topposParam + 410)
      .stroke()
      .text(`Sacado: ${aditivo.responsavel}`, 50, topposParam + 420)
      .text(`Cedente: ${aditivo.sacado}`, 350, topposParam + 420)
      .text(`CPF: ${aditivo.responsavelCNPJCPF}`, 50, topposParam + 430)
      .text(`CPF: ${aditivo.sacadoCNPJCPF}` , 350, topposParam + 430)
      .text('Testemunhas:', 50, topposParam + 450)
      .moveTo(50, topposParam + 490)
      .lineTo(250, topposParam + 490)
      .stroke()
      .moveTo(350, topposParam + 490)
      .lineTo(550, topposParam + 490)
      .stroke()
      .text('Guilherme Beltrami Alesina', 50, topposParam + 500)
      .text('Denis Destri', 350, topposParam + 500)
      .text('CPF: 376.301.048-39', 50, topposParam + 510)
      .text('CPF: 066.197.328-01', 350, topposParam + 510)
    ;
    
    topposParam = topposParam + 510;
    
    doc
      .fontSize(7)
      .text("Capitale Investment Holding      -        Av. Yojori Takaoka 4384, sala 701 - Alphaville, Santana Parnaiba, São Paulo, CEP: 06541-038 Brazil", 
      50, 
      780, 
      {align: "center"}
    );
    
    doc.end();
    const buffers = [];
    doc.on('data', buffers.push.bind(buffers));
    doc.on('end',() => {
      const pdfData = Buffer.concat(buffers);
      resolve(pdfData);
    });
  });
};

//-------------------------------------------------------------------

export const handler = async (events) => {

  console.log('event ', events);
  const retrodafila = [];

  for (const record of events?.Records) {
    retrodafila.push(await rodafila(JSON.parse(record.body)));
  }
  
//  console.log('retrodafila', retrodafila);


 return({ success: true, retrodafila });
};

async function rodafila(event) {
  console.log('message ', event); 

  try {
    const keyPath = `aditivos/aditivo-${event.aditivo_nr}.pdf`;
    const pdfResponse = await generatePdf(event ,100);
    const uploadParams = {
      Bucket: S3_BUCKET,
      Key: keyPath,
      ContentType: "application/pdf",
      Body: pdfResponse,
    };
    
    try {
      const data = await s3Client.send(new PutObjectCommand(uploadParams));
      //console.log('success',data);
      
      /*
      const queueUrl = 'https://sqs.us-east-2.amazonaws.com/730335345607/clicksign.fifo';
      const messageBody = event;
      const params = {
          QueueUrl: queueUrl,
          MessageBody: JSON.stringify(messageBody),
          MessageGroupId: 'clicksign' 
      };*/

    try {
        //const datafila = await sqsClient.send(new SendMessageCommand(params));
        console.log("Mensagem enviada com sucesso, ID:", datafila);
    } catch (err) {
        console.error("Erro ao enviar mensagem: ", err);
    }
      return(data);
    } catch (e) {
      console.log('erro', e);
    }
  } catch (error) {
    console.log('Catch no createaditivo', error);
    return (false);
  }
}


function formatDate(date) {
  const day = date.getDate();
  const month = date.getMonth() + 1;
  const year = date.getFullYear();
  
  return day + "/" + month + "/" + year;
}

function formatDateExt(date) {
  const day = date.getDate();
  const mes = date.getMonth();
  const year = date.getFullYear();
  const meses = ["Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Junho", "Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro"];
  const month = meses[mes];
  
  return day + " de " + month + " de " + year;
}