import { baseResponse, getUserFromToken, UserGroupTypes } from 'capfunctions';

import {
    _insert,
    _update,
    _find,
    _aprovaExecucao,
    _concluir,
    _agenda,
    _listarAgendaPlantao,
    _list,
} from './functions.mjs';

const actions = {
    _insert,
    _update,
    _list,
    _find,
    _aprovaExecucao,
    _concluir,
    _agenda,
    _listarAgendaPlantao,
};

export const handler = async (event) => {
    console.log(event);

    try {
        const userFromToken = await getUserFromToken(event.headers?.Authorization);

        const groupsAllowed = [
            UserGroupTypes.GESTOR_CAPITALE,
            UserGroupTypes.GESTOR_CLIENTE,
            UserGroupTypes.PROF_SAUDE,
            UserGroupTypes.ESCALISTA,
        ];
        const isAllowed = groupsAllowed.some((group) => userFromToken?.body.groups.includes(group));

        if (!isAllowed) {
            return baseResponse.unauthorized('Usu<PERSON>rio sem as permissões necessárias.');
        }

        if (!!userFromToken) {
            if (!(event.body?.method in actions || event.method in actions)) {
                return baseResponse.notFound('Método não encontrado');
            }

            return {
                newToken: userFromToken?.body?.newToken,
                ...(await actions[event.body?.method || event.method]({
                    ...(event.body || event),
                    ...userFromToken,
                })),
            };
        }

        return baseResponse.unauthorized(
            'Token de sessão expirado, por favor, faça login novamente'
        );
    } catch (error) {
        console.log('Erro ao processar a requisição brwAtendimento', error);

        return baseResponse.error('Erro ao processar a requisição. CATCH HANDLER brwAtendimento');
    }
};
