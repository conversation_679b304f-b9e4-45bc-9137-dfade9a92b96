import {
  checkData,
  execQuery,
  baseResponse,
  dbQuery,
  insert,
  update,
  formataHora,
  horarioAtual,
  calculaHorasRealizadas,
  plantaoPorSituacao,
} from 'capfunctions';

async function processCheckOut(result, event) {
  const currentCheckOutTime = await horarioAtual();
  const totalWorkedHours = calculaHorasRealizadas(result.lastCheckIn, currentCheckOutTime);

  const updateData = {
    ocCheckOut: formataHora(currentCheckOutTime),
    ocQtRealizadas: totalWorkedHours,
    ocQtAprovadas: totalWorkedHours,
    ocQtGlosadas: '00:00:00',
  };

  const { lat, long } = event?.appUser || {};
  updateData.ocLatCheckOut = event.xDevice === 'mobile' ? lat ?? null : '99.9999999';
  updateData.ocLongCheckOut = event.xDevice === 'mobile' ? long ?? null : '99.9999999';

  const [agData] = result.lastCheckIn.split(' ');

  const updateConditions = {
    isInstSaude: event.isInstSaude,
    laNome: event.laNome,
    esEspecialidade: event.esEspecialidade,
    ocNrContrato: event.ocNrContrato,
    clCliente: event.clCliente,
    ceTipoPagamento: event.ceTipoPagamento,
    opNrPlantao: event.opNrPlantao,
    ocCheckIn: formataHora(result.lastCheckIn),
    agData,
  };

  const updateResponse = await update('capOperPlantaoCheck', updateData, updateConditions, event);

  if (updateResponse.success) {
    return baseResponse.created('Checkout realizado com sucesso.');
  } else {
    return baseResponse.badRequest(updateResponse);
  }
}

async function processCheckIn(event) {
  const newCheckInData = {
    isInstSaude: event.isInstSaude,
    laNome: event.laNome,
    esEspecialidade: event.esEspecialidade,
    ocNrContrato: event.ocNrContrato,
    clCliente: event.clCliente,
    ceTipoPagamento: event.ceTipoPagamento,
    opNrPlantao: event.opNrPlantao,
    ocCheckIn: await horarioAtual(),
    agData: event.agData,
  };
  
  const { lat, long } = event?.appUser || {};
  newCheckInData.ocLatCheckin = event.xDevice === 'mobile' ? lat ?? null : '99.9999999';
  newCheckInData.ocLongCheckin = event.xDevice === 'mobile' ? long ?? null : '99.9999999';

  const insertResponse = await insert('capOperPlantaoCheck', newCheckInData, event);

  if (insertResponse.success) {
    const atendimentoResponse = await execQuery(
      `
                SELECT * from capInstSaudePlantao 
                WHERE isInstSaude = ?
                AND laNome = ?
                AND esEspecialidade = ?
                AND ocNrContrato = ?
                AND clCliente = ?
                AND opNrPlantao = ?
            `,
      [
        event.isInstSaude,
        event.laNome,
        event.esEspecialidade,
        event.ocNrContrato,
        event.clCliente,
        event.opNrPlantao,
      ]
    );

    const atendimento = atendimentoResponse.results[0];

    if (atendimento.opSituacao === 'AguardExecucao') {
      const infosAtendimento = {
        isInstSaude: event.isInstSaude,
        laNome: event.laNome,
        esEspecialidade: event.esEspecialidade,
        ocNrContrato: event.ocNrContrato,
        clCliente: event.clCliente,
        ceTipoPagamento: event.ceTipoPagamento,
        opNrPlantao: event.opNrPlantao,
      };

      await update(
        'capInstSaudePlantao',
        {
          opSituacao: 'EmExecucao',
        },
        infosAtendimento,
        event
      );
    }

    return baseResponse.created('Check-in realizado com sucesso.');
  } else {
    return baseResponse.badRequest(insertResponse);
  }
}

export async function _realizarCheck(event) {
  try {
    const requiredFields = [
      'isInstSaude',
      'laNome',
      'esEspecialidade',
      'ocNrContrato',
      'clCliente',
      'ceTipoPagamento',
      'opNrPlantao',
      'psCPF',
      'psSequenciaSolicitacao',
    ];

    if (checkData(event, { obrigatorios: requiredFields })) {
      return baseResponse.badRequest('Dados insuficientes.');
    }

    if (await plantaoPorSituacao(event.opNrPlantao, 'Concluido')) {
      return baseResponse.badRequest('Plantão já concluído.');
    }

    const query = /*sql*/ `
        SELECT 
            MAX(p.ocCheckIn) as lastCheckIn,
            CASE
                WHEN EXISTS (
                    SELECT 1 
                    FROM capOperPlantaoCheck pc
                    JOIN capInstSaudePlantao isp ON 
                        pc.laNome = isp.laNome
                        AND pc.esEspecialidade = isp.esEspecialidade
                        AND pc.ocNrContrato = isp.ocNrContrato
                        AND pc.clCliente = isp.clCliente
                        AND pc.opNrPlantao = isp.opNrPlantao
                    WHERE 
                        pc.isInstSaude = ?
                        AND isp.psCPF = ?
                        AND pc.opNrPlantao = ?
                        AND pc.ocCheckOut IS NULL
                        AND TIMESTAMPDIFF(MINUTE, pc.ocCheckIn, CURRENT_TIMESTAMP) <= 1440
                ) THEN 0  -- É um checkout (existe um check-in sem checkout)
                ELSE 1    -- É um novo check-in
            END as isNew
        FROM 
            capOperPlantaoCheck p
        JOIN 
            capInstSaudePlantao sp2 ON 
                p.laNome = sp2.laNome
                AND p.esEspecialidade = sp2.esEspecialidade
                AND p.ocNrContrato = sp2.ocNrContrato
                AND p.clCliente = sp2.clCliente
                AND p.opNrPlantao = sp2.opNrPlantao
        LEFT JOIN 
            capInstSaudePlantaoSolicitacao isps ON
                isps.isInstSaude = p.isInstSaude
                AND isps.laNome = p.laNome
                AND isps.esEspecialidade = p.esEspecialidade
                AND isps.ocNrContrato = p.ocNrContrato
                AND isps.clCliente = p.clCliente
                AND isps.opNrPlantao = p.opNrPlantao
                AND isps.psCPF = sp2.psCPF
        WHERE
            p.isInstSaude = ?
            AND p.laNome = ?
            AND p.esEspecialidade = ?
            AND p.ocNrContrato = ?
            AND p.clCliente = ?
            AND p.ceTipoPagamento = ?
            AND p.opNrPlantao = ?
            AND sp2.psCPF = ?
            AND isps.psSequenciaSolicitacao = ?;
    `;

    const queryParams = [
      event.isInstSaude,
      event.psCPF,
      event.opNrPlantao,
      ...requiredFields.map((field) => event[field]),
    ];

    const queryResult = await execQuery(query, queryParams);
    const result = queryResult.results[0];

    const [plantaoAgenda] = await dbQuery(
      /*sql*/ `
            SELECT agData,  agAtivo, CURRENT_DATE() as currentDate
            FROM capInstSaudePlantaoAgenda
            WHERE agAtivo = 1 
            AND isInstSaude = ?
            AND laNome = ?
            AND esEspecialidade = ?
            AND ocNrContrato = ?
            AND clCliente = ?
            AND ceTipoPagamento = ?
            AND opNrPlantao = ? 
        `,
      [
        event.isInstSaude,
        event.laNome,
        event.esEspecialidade,
        event.ocNrContrato,
        event.clCliente,
        event.ceTipoPagamento,
        event.opNrPlantao,
      ]
    );

    if (!plantaoAgenda?.agData) {
      console.log('Plantão não encontrado na tabela capInstSaudePlantaoAgenda');

      return baseResponse.notFound('Plantão não encontrado.');
    }

    event.agData = plantaoAgenda?.currentDate;

    if (result.isNew === 0) {
      return await processCheckOut(result, event);
    } else {
      return await processCheckIn(event);
    }
  } catch (error) {
    console.error('ERROR _realizarCheck', error);

    return baseResponse.error('Erro ao processar requisição');
  }
}
