import bcrypt from 'bcryptjs';

import {
  createResponse,
  checkData,
  execQuery,
  baseResponse,
  dbQuery,
  insert,
  update,
  onlyNumber,
  isEmailValid,
} from 'capfunctions';

async function _list(event) {
  console.log(event);
  try {
    const isMaster = event.body?.groups?.find((group) => group === 'gestorcliente');

    let additionalQuery = /*sql*/ `AND a.isMaster = 0`;
    if (!isMaster) {
      additionalQuery = /*sql*/ `AND a.isMaster IN (0, 1)`;
    }

    var query, params;
    if (event.isInstSaude) {
      query = /*sql*/ `
        SELECT
            b.isInstSaude,
            a.usCPFUsuario, 
            a.usNome, 
            a.usDatNasc,
            a.usNomeMae, 
            a.usTelefone, 
            a.usEmail, 
            a.usCEP, 
            a.usEndereco, 
            a.usNrEnd, 
            a.usComplEnd, 
            a.usBairro, 
            a.usCidade, 
            a.usUF, 
            a.usCargo, 
            a.usDepartamento, 
            a.usRegFuncional, 
            a.usAtivo,
            GROUP_CONCAT(DISTINCT g.gmNome) as usPerfis
          FROM capUsuario a
          JOIN capInstSaudeUsuario b on (a.usCPFUsuario = b.usCPFUsuario)
          LEFT JOIN capInstSaudeContrato c on (b.isInstSaude = c.isInstSaude)
          LEFT JOIN brcUsuarioGrupoMenu g on (g.usuarioCPF = a.usCPFUsuario)
          WHERE a.usNome like ?
          AND b.isInstSaude = ?
          ${additionalQuery}
          GROUP BY 
            a.usCPFUsuario, 
            a.usNome, 
            a.usNomeMae, 
            a.usDatNasc,
            a.usTelefone, 
            a.usEmail, 
            a.usCEP, 
            a.usEndereco, 
            a.usNrEnd, 
            a.usComplEnd, 
            a.usBairro, 
            a.usCidade, 
            a.usUF, 
            a.usCargo, 
            a.usDepartamento, 
            a.usRegFuncional, 
            a.usAtivo
        `;

      params = ['%' + (event['searchName'] || '') + '%', event['isInstSaude']];
    } else {
      query = /*sql*/ `
        SELECT
          b.clCliente,
          a.usCPFUsuario, 
          a.usNome, 
          a.usNomeMae, 
          a.usDatNasc,
          a.usTelefone, 
          a.usEmail, 
          a.usCEP, 
          a.usEndereco, 
          a.usNrEnd, 
          a.usComplEnd, 
          a.usBairro, 
          a.usCidade, 
          a.usUF, 
          a.usCargo, 
          a.usDepartamento, 
          a.usRegFuncional, 
          a.usAtivo,
          a.psOnboardingPendente,
          b.ucGestor,
          GROUP_CONCAT(DISTINCT g.gmNome) as usPerfis
        FROM capUsuario a
        JOIN capClienteUsuario b on (a.usCPFUsuario = b.usCPFUsuario)
        LEFT JOIN brcUsuarioGrupoMenu g on (g.usuarioCPF = a.usCPFUsuario)
        LEFT JOIN capInstSaudeContrato c on (b.clCliente = c.clCliente)
        WHERE a.usNome like ?
        AND b.clCliente like ?
        ${additionalQuery}
        GROUP BY 
          b.clCliente,
          a.usCPFUsuario, 
          a.usNome, 
          a.usNomeMae, 
          a.usDatNasc,
          a.usTelefone, 
          a.usEmail, 
          a.usCEP, 
          a.usEndereco, 
          a.usNrEnd, 
          a.usComplEnd, 
          a.usBairro, 
          a.usCidade, 
          a.usUF, 
          a.usCargo, 
          a.usDepartamento, 
          a.usRegFuncional, 
          a.usAtivo,
          a.psOnboardingPendente
      `;

      params = ['%' + (event['searchName'] || '') + '%', '%' + (event['clCliente'] || '') + '%'];
    }

    const response = await dbQuery(query, params);

    return baseResponse.created(
      'Listado com sucesso',
      response.map((item) => {
        return {
          ...item,
          usPerfis: item.usPerfis ? item.usPerfis.split(',') : [],
        };
      })
    );
  } catch (error) {
    console.log('ERROR _list', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _find(event) {
  try {
    if (checkData(event, ['usCPFUsuario'])) {
      return baseResponse.error('Dados insuficientes');
    }

    const query = /*sql*/`SELECT
                    a.usCPFUsuario, 
                    a.usNome, 
                    a.usNomeMae, 
                    a.usTelefone, 
                    a.usEmail, 
                    a.usCEP, 
                    a.usDatNasc,
                    a.usEndereco, 
                    a.usNrEnd, 
                    a.usComplEnd, 
                    a.usBairro, 
                    a.usCidade, 
                    a.usUF, 
                    a.usCargo, 
                    a.usDepartamento, 
                    a.usRegFuncional, 
                    a.usAtivo,
                    a.psOnboardingPendente
                  FROM capUsuario a
                  WHERE a.usCPFUsuario = ?
                `;

    const params = [event['usCPFUsuario'] || ''];
    const response = await execQuery(query, params);

    if (response && response.results && response.results.length > 0) {
      return baseResponse.created('Listado com sucesso', response.results && response.results[0]);
    }

    return baseResponse.error('Usuário não encontrado');
  } catch (error) {
    console.log('ERROR _find', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _insert(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'usCPFUsuario',
          'usNome',
          'usTelefone',
          'usEmail',
          'usDatNasc',
          'usCEP',
          'usEndereco',
          'usNrEnd',
          'usBairro',
          'usCidade',
          'usUF',
          'usCargo',
          'usDepartamento',
          'usPerfis',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    if (event.usPerfis.length < 1) {
      return baseResponse.badRequest('Perfis não informados.');
    }

    let taAtualizando = false;

    if (!validaCPFCNPJ(event.usCPFUsuario)) {
      return baseResponse.error('CPF / CNPJ do Usuário invalido');
    }

    if (!isEmailValid(event.clEmail)) {
      //return createResponse(400, 'Formato de email inválido.', '', null);
    }

    const entity = {
      usCPFUsuario: onlyNumber(event.usCPFUsuario),
      usNome: event.usNome.toUpperCase(),
      usNomeMae: (event?.usNomeMae || '').toUpperCase(),
      usTelefone: onlyNumber(event.usTelefone),
      usEmail: event.usEmail,
      usDatNasc: event.usDatNasc,
      usCEP: onlyNumber(event.usCEP),
      usEndereco: event.usEndereco,
      usNrEnd: event.usNrEnd,
      usComplEnd: event.usComplEnd === undefined ? '' : event.usComplEnd,
      usBairro: event.usBairro,
      usCidade: event.usCidade,
      usUF: event.usUF,
      usCargo: event.usCargo,
      usDepartamento: event.usDepartamento,
      usRegFuncional: event.usRegFuncional === undefined ? '' : event.usRegFuncional,
      usAtivo: 1,
      usSenha: bcrypt.hashSync(event.usCPFUsuario.substring(0, 6)),
      psOnboardingPendente: event?.psOnboardingPendente ? 1 : 0,
    };

    var response;

    var checkruser = await dbQuery(' SELECT 1 FROM capUsuario WHERE usCPFUsuario = ? ', [
      event.usCPFUsuario,
    ]);

    if (checkruser.length < 1) {
      response = await insert('capUsuario', entity, event);

      if (!response.success) {
        return baseResponse.badRequest('Erro na Inserção do Usuario.');
      }
    } else {
      taAtualizando = true;
    }

    var relacao, resprel, checkrel;

    if (event.clCliente) {
      checkrel = await dbQuery(
        ' SELECT 1 FROM capClienteUsuario WHERE usCPFUsuario = ? AND clCliente = ?',
        [event.usCPFUsuario, event.clCliente]
      );

      if (checkrel.length < 1) {
        relacao = {
          clCliente: event.clCliente,
          usCPFUsuario: onlyNumber(event.usCPFUsuario),
          ucGestor: event.ucGestor === 1 ? event.ucGestor : null,
        };

        resprel = await insert('capClienteUsuario', relacao, event);
      }

      await dbQuery(
        /*sql*/ `
          DELETE FROM brcUsuarioGrupoMenu WHERE usuarioCPF = ? AND gmNome != 'profsaude'
        `,
        [event.usCPFUsuario]
      );

      if (event.usCPFUsuario === '06619732801') {
        if (event.usPerfis.find((item) => item.value !== 'gestorcapitale')) {
          event.usPerfis.push({ value: 'gestorcapitale' });
        }
      }

      await Promise.all(
        event.usPerfis.map((item) => {
          insert('brcUsuarioGrupoMenu', {
            sgCliente: 'cap',
            usuarioCPF: event.usCPFUsuario,
            sgInstSaude: 'capitaleholding',
            gmNome: item.value,
          });
        })
      );
    } else if (event.isInstSaude) {
      checkrel = await dbQuery(
        ' SELECT 1 FROM capInstSaudeUsuario WHERE usCPFUsuario = ? AND isInstSaude = ?',
        [event.usCPFUsuario, event.isInstSaude]
      );

      if (checkrel.length < 1) {
        relacao = {
          isInstSaude: event.isInstSaude,
          usCPFUsuario: onlyNumber(event.usCPFUsuario),
        };
        resprel = await insert('capInstSaudeUsuario', relacao, event);
        if (resprel.success) console.log('relacionado');
        else console.log('nao relacionado');
      }
    }

    // - CHAMAR LAMBDA PARA ENVIAR EMAIL PARA USUARIO CADAASTRADO

    return baseResponse.created(
      taAtualizando ? 'Usuário atualizado com successo' : 'Usuário cadastrado com successo'
    );
  } catch (error) {
    console.log('ERROR _insert', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _update(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'usCPFUsuario',
          'usNome',
          'usTelefone',
          'usEmail',
          'usCEP',
          'usDatNasc',
          'usEndereco',
          'usNrEnd',
          'usBairro',
          'usCidade',
          'usUF',
          'usCargo',
          'usDepartamento',
          'usPerfis',
        ],
      })
    ) {
      return createResponse(400, 'Dados insuficentes.', '', null);
    }

    if (event.usPerfis.length < 1) {
      return createResponse(400, 'Perfis não informados.', '', null);
    }

    if (!isEmailValid(event.clEmail)) {
      //return createResponse(400, 'Formato de email inválido.', '', null);
    }

    const entity = {
      usNome: event.usNome.toUpperCase(),
      usNomeMae: (event?.usNomeMae || '').toUpperCase(),
      usTelefone: onlyNumber(event.usTelefone),
      usEmail: event.usEmail,
      usCEP: onlyNumber(event.usCEP),
      usEndereco: event.usEndereco,
      usDatNasc: event.usDatNasc,
      usNrEnd: event.usNrEnd,
      usComplEnd: event.usComplEnd === undefined ? '' : event.usComplEnd,
      usBairro: event.usBairro,
      usCidade: event.usCidade,
      usUF: event.usUF,
      usCargo: event.usCargo,
      usDepartamento: event.usDepartamento,
      usRegFuncional: event.usRegFuncional === undefined ? '' : event.usRegFuncional,
      psOnboardingPendente: event?.psOnboardingPendente ? 1 : 0,
    };

    const response = await update(
      'capUsuario',
      entity,
      {
        usCPFUsuario: onlyNumber(event.usCPFUsuario),
      },
      event
    );

    if (response.success) {
      var relacao, resprel, checkrel;

      if (event.clCliente) {
        checkrel = await dbQuery(
          ' SELECT 1 FROM capClienteUsuario WHERE usCPFUsuario = ? AND clCliente = ?',
          [event.usCPFUsuario, event.clCliente]
        );

        if (checkrel.length < 1) {
          relacao = {
            clCliente: event.clCliente,
            usCPFUsuario: onlyNumber(event.usCPFUsuario),
          };
          resprel = await insert('capClienteUsuario', relacao, event);
          if (resprel.success) console.log('relacionado');
          else console.log('nao relacionado');
        }

        var updtData = {
          ucGestor: event.ucGestor === 1 ? event.ucGestor : null,
        };
        var where = {
          usCPFUsuario: event.usCPFUsuario,
        };
        const updtGestor = await update('capClienteUsuario', updtData, where, event);

        if (updtGestor.success) console.log('Atualiazado');

        await dbQuery(
          /*sql*/ `
            DELETE FROM brcUsuarioGrupoMenu WHERE usuarioCPF = ? AND gmNome != 'profsaude'
          `,
          [event.usCPFUsuario]
        );

        await Promise.all(
          event.usPerfis.map((item) => {
            insert('brcUsuarioGrupoMenu', {
              sgCliente: 'cap',
              usuarioCPF: event.usCPFUsuario,
              sgInstSaude: 'capitaleholding',
              gmNome: item.value,
            });
          })
        );
      } else if (event.isInstSaude) {
        checkrel = await dbQuery(
          ' SELECT 1 FROM capInstSaudeUsuario WHERE usCPFUsuario = ? AND isInstSaude = ?',
          [event.usCPFUsuario, event.isInstSaude]
        );

        if (checkrel.length < 1) {
          relacao = {
            isInstSaude: event.isInstSaude,
            usCPFUsuario: onlyNumber(event.usCPFUsuario),
          };
          resprel = await insert('capInstSaudeUsuario', relacao, event);
          if (resprel.success) console.log('relacionado');
          else console.log('nao relacionado');
        }
      }

      return baseResponse.created('Usuário atualizado com successo', response);
    }
    console.info(response.success);

    return baseResponse.error('Erro ao atualizar usuario');
  } catch (error) {
    console.log('ERROR _update', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

function validaCPFCNPJ(num) {
  if (num.length == 14) {
    return validaCNPJ(num);
  }

  if (num.length == 11) {
    return validaCPF(num);
  }
  return false;
}

function validaCPF(cpf) {
  cpf = cpf.replace(/[^\d]+/g, '');
  if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

  let soma = 0;
  let resto;

  for (let i = 1; i <= 9; i++) soma += parseInt(cpf.substring(i - 1, i)) * (11 - i);
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) resto = 0;
  if (resto !== parseInt(cpf.substring(9, 10))) return false;

  soma = 0;
  for (let i = 1; i <= 10; i++) soma += parseInt(cpf.substring(i - 1, i)) * (12 - i);
  resto = (soma * 10) % 11;
  if (resto === 10 || resto === 11) resto = 0;
  if (resto !== parseInt(cpf.substring(10, 11))) return false;

  return true;
}

function validaCNPJ(cnpj) {
  cnpj = cnpj.replace(/[^\d]+/g, '');
  if (cnpj.length !== 14) return false;

  let tamanho = cnpj.length - 2;
  let numeros = cnpj.substring(0, tamanho);
  let digitos = cnpj.substring(tamanho);
  let soma = 0;
  let pos = tamanho - 7;

  for (let i = tamanho; i >= 1; i--) {
    soma += numeros.charAt(tamanho - i) * pos--;
    if (pos < 2) pos = 9;
  }

  let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
  if (resultado != digitos.charAt(0)) return false;

  tamanho = tamanho + 1;
  numeros = cnpj.substring(0, tamanho);
  soma = 0;
  pos = tamanho - 7;

  for (let i = tamanho; i >= 1; i--) {
    soma += numeros.charAt(tamanho - i) * pos--;
    if (pos < 2) pos = 9;
  }

  resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
  if (resultado != digitos.charAt(1)) return false;

  return true;
}

async function _forgot(event) {
  /*
    try {
      if (!event.email) {
        return {
          statusCode: 404,
          success: false,
          message: 'Usuário não encontrado',
        };
      }
      const userQuery = await execQuery(
      `
        SELECT emailUsuario
        FROM capUsuarios
        WHERE emailUsuario = ?
        LIMIT 1
      `,
        [event.email, event.]
      );
      console.log(JSON.stringify({ userQuery }));

      if (userQuery && userQuery.length > 0) {
        const user = userQuery[0];
        const min = 100000;
        const max = 999999;
        const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;
        const senha = 'p' + randomNumber;
        const password = bcrypt.hashSync(senha);
        console.log({ password, senha });

        await execQuery(
         `
          UPDATE capUsuarios
          SET senhaUsuario = ?,
          usuarioSenhaNova = 1
          WHERE emailUsuario = ?
         -- AND sgCliente = ?
        `,
          [password, event.email, event.sgCliente]
        );

        let corpoEmail = '';
        corpoEmail += `Você solicitou uma nova senha para o app Capitale investiment. \n\n`;
        corpoEmail += 'Uma senha temporária foi criada: ' + senha + ' \n\n';
        corpoEmail +=
          'Recomendamos efetuar o login no aplicativo e alterar essa senha para uma de sua escolha. \n\n';
        corpoEmail += 'Atenciosamente, \n';
        corpoEmail +=  '<EMAIL>';
        corpoEmail +=
          '\n\n * ATENÇÃO: Mensagem Automática – Favor não responder *.';

        await lambdaInvoke('asmSendMail', {
          destinatariosEmail: user.emailUsuario,
          corpoEmail,
          assuntoEmail: `${process.env.MOBILE_APP_NAME} - Solicitação de senha`,
        });

        let message = '';
        message += 'Uma senha temporária foi enviada para ' + user.emailUsuario;
        message += '. Verifique a sua caixa de entrada ou pasta de spam.';
        return {
          statusCode: 200,
          success: true,
          message,
        };
      }
      return {
        statusCode: 404,
        success: false,
        message: 'Usuário não encontrado',
      };
    } catch (error) {
      console.log('Catch na função forgotPass()', error);
      return {
        statusCode: 404,
        success: false,
        message: 'Usuário não encontrado',
      };
    }
  */
}

async function _firstlogin(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: ['password', 'confirmPassword', 'lgpdAccepted'],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const password = event['password'];
    const confirmPassword = event['confirmPassword'];
    const lgpdAccepted = event['lgpdAccepted'];

    if (!lgpdAccepted) {
      return baseResponse.badRequest('LGPD precisa ser aceita.');
    }

    if (password !== confirmPassword) {
      return {
        statusCode: 204,
        success: false,
        message: 'Nova senha e confirmação de nova senha não coincidem',
      };
    }

    await execQuery(
      `
          UPDATE capUsuario SET usSenha = ?, usSenhaNova = 0, usAceiteLGPD = 1 WHERE usCPFUsuario = ?
        `,
      [bcrypt.hashSync(password), event['body']['user']]
    );

    return {
      statusCode: 200,
      success: true,
      message: 'Senha atualizada com sucesso',
    };
  } catch (error) {
    console.log('Catch na função _firstlogin()', error);
    return {
      statusCode: 500,
      success: false,
      message:
        'Algo deu errado, reinicie o aplicativo mas se persistir, por favor entre em contato',
    };
  }
}

async function _updatePass(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: ['cpf', 'newPassword', 'confirmPassword', 'lgpdAccepted'],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const cpf = event['cpf'];
    const newPassword = event['newPassword'];
    const confirmPassword = event['confirmPassword'];
    const lgpdAccepted = event['lgpdAccepted'];

    if (!lgpdAccepted) {
      return baseResponse.badRequest('LGPD precisa ser aceita.');
    }

    if (newPassword !== confirmPassword) {
      return {
        statusCode: 204,
        success: false,
        message: 'Nova senha e confirmação de nova senha não coincidem',
      };
    }

    await execQuery(
      /*sql*/ `
                UPDATE capUsuario SET usSenha = ?, usSenhaNova = 0, usAceiteLGPD = 1 WHERE usCPFUsuario = ?
            `,
      [bcrypt.hashSync(newPassword), cpf]
    );

    return {
      statusCode: 200,
      success: true,
      message: 'Senha atualizada com sucesso',
    };
  } catch (error) {
    console.log('Catch na função _updatePass()', error);
    return {
      statusCode: 500,
      success: false,
      message:
        'Algo deu errado, reinicie o aplicativo mas se persistir, por favor entre em contato',
    };
  }
}

async function _aceitarTermosLGPD(event) {
  try {
    console.log(event);

    const userId = event.userId;
    const documentoId = event.documentoId;
    const documento = event.documento;

    await dbQuery(
      /*sql*/ `
        INSERT INTO aceite_termos_lgpd (
          usuario_id, termo_versao_id, consentimento_lgpd,
          endereco_ip, latitude, longitude, user_agent,
          dados_adicionais, termo_versao
        )
        VALUES
        (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `,
      [
        userId,
        documentoId,
        event.acceptanceData.lgpdConsent,
        event.acceptanceData.ipAddress,
        event.acceptanceData.latitude,
        event.acceptanceData.longitude,
        event.acceptanceData.userAgent,
        JSON.stringify(event.acceptanceData.additionalData),
        documento.versao,
      ]
    );

    return {
      statusCode: 200,
      success: true,
      message: '',
    };
  } catch (error) {
    console.log('Catch na função _aceitarTermosLGPD()', error);
    return {
      statusCode: 500,
      success: false,
      message:
        'Algo deu errado, reinicie o aplicativo mas se persistir, por favor entre em contato',
    };
  }
}

async function _getTermosLGPDActive(event) {
  try {
    console.log(event);
 
    const [termo] = await dbQuery(
      /*sql*/ `
        SELECT * FROM gerenciamento_termos_lgpd
        WHERE ativo = 1
        AND tipo_documento = ?
        ORDER BY atualizado_em DESC
        LIMIT 1
      `,
      [event.tipoDoc || 'termos_lgpd']
    );

    return {
      statusCode: 200,
      success: true,
      message: 'Termo de uso encontrado com sucesso',
      data: termo,
    };
  } catch (error) {
    console.log('Catch na função _getTermosLGPDActive()', error);
    return {
      statusCode: 500,
      success: false,
      message:
        'Algo deu errado, reinicie o aplicativo mas se persistir, por favor entre em contato',
    };
  }
}

export {
  _insert,
  _update,
  _list,
  _find,
  _forgot,
  _firstlogin,
  _updatePass,
  _aceitarTermosLGPD,
  _getTermosLGPDActive,
};
