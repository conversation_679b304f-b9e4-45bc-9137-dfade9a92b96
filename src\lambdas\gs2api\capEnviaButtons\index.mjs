import axios from 'axios';

const WHATSAPP_TOKEN = process.env.WHATSAPP_TOKEN;
const PHONE_NUMBER_ID = process.env.PHONE_NUMBER_ID;

export const handler = async (event) => {
    try {
        console.log('evente ', event);
        const { phoneNumber, templateName, language, parambody, buttons } = event.body;
        const { saudacao, dr, nome, agenda, local, periodo, endereco } = parambody;

        const response = await axios.post(
            `https://graph.facebook.com/v22.0/${PHONE_NUMBER_ID}/messages`,
            {
                messaging_product: 'whatsapp',
                to: phoneNumber,
                type: 'template',
                template: {
                    name: templateName,
                    language: { code: language },
                    components: [
                        {
                            type: 'body',
                            parameters: [
                                { type: 'text', parameter_name: 'saudacao', text: saudacao },
                                { type: 'text', parameter_name: 'dr', text: dr },
                                { type: 'text', parameter_name: 'nome', text: nome },
                                { type: 'text', parameter_name: 'agenda', text: agenda },
                                { type: 'text', parameter_name: 'local', text: local },
                                { type: 'text', parameter_name: 'periodo', text: periodo },
                                { type: 'text', parameter_name: 'endereco', text: endereco },
                            ],
                        },
                        {
                            type: 'button',
                            sub_type: 'quick_reply',
                            index: '0',
                            parameters: [
                                {
                                    type: 'payload',
                                    payload: buttons[0], //-- "confirmar_agendamento"
                                },
                            ],
                        },
                        {
                            type: 'button',
                            sub_type: 'quick_reply',
                            index: '1',
                            parameters: [
                                {
                                    type: 'payload',
                                    payload: buttons[1], //"cancelar_agendamento"
                                },
                            ],
                        },
                    ],
                },
            },
            {
                headers: {
                    Authorization: `Bearer ${WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json',
                },
            }
        );

        console.log('success ', response);
        return {
            statusCode: 200,
            body: JSON.stringify({ success: true, data: response.data }),
        };
    } catch (error) {
        console.log('error ', error);
        return {
            statusCode: error.response?.status || 500,
            body: JSON.stringify({ success: false, error: error.message }),
        };
    }
};
