{"id": null, "tenantId": "473680", "name": "GS2 - Termos e Condições - Médicos", "created": null, "flowNodes": [{"flowReplies": [{"flowReplyType": "Text", "data": "<p>📄 Abaixo estão os <strong>Termos e Condições Gerais </strong>da parceria entre nós e a @ClinicaHospital.<br>\n</p>\n<p><strong>A adesão é opcional e não traz qualquer obrigação imediata.</strong></p>", "caption": "", "mimeType": ""}, {"flowReplyType": "Document", "data": "", "caption": "GS2 - Termos e Condições Gerais.pdf", "mimeType": ""}], "id": "main_message-XbWtc", "flowNodeType": "Message", "flowNodePosition": {"posX": "502", "posY": "214"}, "isStartNode": true}, {"newFlowId": "", "id": "main_invokeFlow-<PERSON><PERSON><PERSON>", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "1036", "posY": "394"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_message-XbWtc-main_invokeFlow-<PERSON><PERSON>n", "sourceNodeId": "main_message-XbWtc", "targetNodeId": "main_invokeFlow-<PERSON><PERSON><PERSON>"}], "lastUpdated": "2025-07-28T02:38:40.08Z", "isDeleted": false, "transform": {"posX": "237.72357289052445", "posY": "24.076109311431907", "zoom": "0.6597539553864475"}, "isPro": true, "channelTypes": ["WA"]}