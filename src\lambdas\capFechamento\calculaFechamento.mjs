import { dbQuery } from 'capfunctions';

export async function calculaFechamento({ event, fechamentos }) {
    if (!fechamentos?.length) {
        return {
            valorComDesagio: 0,
            valorSemDesagio: 0,
        };
    }

    let total = 0;
    let valorBrutoTotal = 0;
    let valorBrutoDoPlantao = fechamentos.reduce((acc, fechamento) => {
        return acc + fechamento.afValor;
    }, 0);

    const [contratoEspecResult] = await dbQuery(
        /*sql*/ `
        SELECT *
        FROM capInstSaudeContratoEspec
        WHERE isInstSaude = ?
         AND laNome = ?
         AND esEspecialidade = ?
         AND ocNrContrato = ?
         AND clCliente = ?
         AND ceTipoPagamento = ?`,
        [
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.ceTipoPagamento,
        ]
    );
    console.log({ contratoEspecResult });

    const taxaDeDesagio = contratoEspecResult?.ceTaxaDesagio || 0.06;
    const dias = contratoEspecResult?.ceQtdDiasMinRecebim || 30;

    const taxaDeDesagioAplicada = (1 + taxaDeDesagio) ** (dias / 30);
    const valorComDesagio = valorBrutoDoPlantao / taxaDeDesagioAplicada;

    total = valorComDesagio;
    valorBrutoTotal = valorBrutoDoPlantao;
    console.log('valor desagio: ', valorComDesagio);
    console.log('taxa aplicada: ', taxaDeDesagioAplicada);

    const [aditivoProfissional] = await dbQuery(
        /*sql*/ `
        SELECT * from capAditivoProfSaude 
        WHERE isInstSaude = ? AND
          laNome = ? AND
          esEspecialidade = ? AND
          ocNrContrato = ? AND
          clCliente = ? AND
          opNrPlantao IN (?) AND
          psCPF = ?`,
        [
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            fechamentos.map((fechamento) => fechamento.opNrPlantao),
            event.psCPF,
        ]
    );

    return {
        // horasAprovadas: `${horasTotal}:${minutosTotal >= 10 ? minutosTotal : '0' + minutosTotal}`,
        // dataInicial: 'TODO: Pegar data mais antiga dos fechamentos',
        // dataFinal: moment().utcOffset(-180).format('YYYY-MM-DD hh:mm:ss'),
        // opNrPlantao: event.opNrPlantao,
        valorComDesagio: (Math.round(total * 100) / 100).toFixed(2),
        valorSemDesagio: (Math.round(valorBrutoTotal * 100) / 100).toFixed(2),
        taxaDeDesagio,
        taxaDeDesagioAplicada: taxaDeDesagioAplicada?.toFixed(2) || 0,
        valorHora: contratoEspecResult?.ceValorHoraProf,
        valorFixo: event?.opValorFixo,
        clCliente: event.clCliente,
        ceTipoPagamento: event.ceTipoPagamento,
        adDataSolicitacao: aditivoProfissional.adDataSolicitacao,
        psCPF: event.psCPF,
        laNome: event.laNome,
        esEspecialidade: event.esEspecialidade,
        ocNrContrato: event.ocNrContrato,
        isInstSaude: event.isInstSaude,
        fechamentos,
    };
}
