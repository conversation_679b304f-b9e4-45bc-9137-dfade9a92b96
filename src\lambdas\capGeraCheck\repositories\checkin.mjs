import { dbQuery, insert, update } from 'capfunctions';

export class CheckRepository {
  async getCheckRecord(atendimento, agenda) {
    const sql = /*sql*/ `
      SELECT * FROM capOperPlantaoCheck
      WHERE isInstSaude = ?
        AND laNome = ?
        AND esEspecialidade = ?
        AND ocNrContrato = ?
        AND clCliente = ?
        AND ceTipoPagamento = ?
        AND opNrPlantao = ?
        AND DATE(agData) = DATE(?)
    `;
    const params = [
      atendimento.isInstSaude,
      atendimento.laNome,
      atendimento.esEspecialidade,
      atendimento.ocNrContrato,
      atendimento.clCliente,
      atendimento.ceTipoPagamento,
      atendimento.opNrPlantao,
      agenda.agData,
    ];
    return await dbQuery(sql, params);
  }

  async updateCheckRecord(where, values, event) {
    return await update('capOperPlantaoCheck', values, where, event);
  }

  async insertCheck(checkData) {
    return await insert('capOperPlantaoCheck', checkData);
  }

  async updateApprovedChecks(atendimento) {
    const sql = /*sql*/ `
            UPDATE capOperPlantaoCheck
            SET ocCheckAprovado = 1
            WHERE opNrPlantao = ? 
                AND isInstSaude = ? 
                AND laNome = ? 
                AND esEspecialidade = ? 
                AND ocNrContrato = ? 
                AND clCliente = ? 
                AND ceTipoPagamento = ? 
                AND ocCheckout IS NOT NULL;
        `;

    const params = [
      atendimento.opNrPlantao,
      atendimento.isInstSaude,
      atendimento.laNome,
      atendimento.esEspecialidade,
      atendimento.ocNrContrato,
      atendimento.clCliente,
      atendimento.ceTipoPagamento,
    ];

    return await dbQuery(sql, params);
  }

  async getApprovedChecks(atendimento) {
    const sql = /*sql*/ `
        SELECT 
            p.opTipoFechamento,
            p.opDiaFechamento,
            c.*,
            /* Agrupamento por período de fechamento */
            CASE
                /* Diário: cada dia é um período separado */
                WHEN p.opTipoFechamento = 'Diario' THEN DATE_FORMAT(c.agData, '%Y-%m-%d')
                
                /* Semanal: agrupar por períodos que terminam no dia de fechamento especificado */
                WHEN p.opTipoFechamento = 'Semanal' THEN 
                    CASE
                        /* Se o dia da agenda é igual ao dia de fechamento, ele pertence ao período atual */
                        WHEN DAYOFWEEK(c.agData) = p.opDiaFechamento THEN 
                            DATE_FORMAT(c.agData, '%Y-%m-%d')
                        /* Se o dia da agenda é depois do dia de fechamento na semana, 
                           calcula o próximo dia de fechamento */
                        WHEN DAYOFWEEK(c.agData) > p.opDiaFechamento THEN
                            DATE_FORMAT(
                                DATE_ADD(c.agData, 
                                    INTERVAL (7 - DAYOFWEEK(c.agData) + p.opDiaFechamento) DAY
                                ),
                                '%Y-%m-%d'
                            )
                        /* Se o dia da agenda é antes do dia de fechamento na semana, 
                           calcula o próximo dia de fechamento (que é na própria semana) */
                        ELSE
                            DATE_FORMAT(
                                DATE_ADD(c.agData, 
                                    INTERVAL (p.opDiaFechamento - DAYOFWEEK(c.agData)) DAY
                                ),
                                '%Y-%m-%d'
                            )
                    END
                
                /* Mensal: agrupar por períodos que terminam no dia de fechamento especificado */
                WHEN p.opTipoFechamento = 'Mensal' THEN
                    /* Calcula a data do próximo fechamento a partir da data da agenda */
                    DATE_FORMAT(
                        CASE
                            /* Se o dia da agenda é posterior ao dia de fechamento, 
                               precisamos ir para o próximo mês */
                            WHEN DAY(c.agData) > p.opDiaFechamento THEN
                                /* Avançar para o mês seguinte e usar o dia de fechamento */
                                DATE(
                                    CONCAT(
                                        YEAR(DATE_ADD(c.agData, INTERVAL 1 MONTH)),
                                        '-',
                                        MONTH(DATE_ADD(c.agData, INTERVAL 1 MONTH)),
                                        '-',
                                        LEAST(
                                            p.opDiaFechamento, 
                                            DAY(LAST_DAY(DATE_ADD(c.agData, INTERVAL 1 MONTH)))
                                        )
                                    )
                                )
                            /* Se o dia da agenda é igual ou anterior ao dia de fechamento,
                               o período termina no dia de fechamento deste mesmo mês */
                            ELSE
                                /* Usar o mês atual e o dia de fechamento */
                                DATE(
                                    CONCAT(
                                        YEAR(c.agData),
                                        '-',
                                        MONTH(c.agData),
                                        '-',
                                        LEAST(
                                            p.opDiaFechamento, 
                                            DAY(LAST_DAY(c.agData))
                                        )
                                    )
                                )
                        END,
                        '%Y-%m-%d'
                    )
            END AS periodoFechamento,
            
            /* Adiciona o período inicial para referência - útil para depuração */
            CASE
                WHEN p.opTipoFechamento = 'Mensal' THEN
                    DATE_FORMAT(
                        /* Calcula a data do fechamento anterior para mostrar onde começou o período */
                        CASE
                            /* Se o dia da agenda é posterior ao dia de fechamento, 
                               o período começou no dia de fechamento deste mesmo mês */
                            WHEN DAY(c.agData) > p.opDiaFechamento THEN
                                DATE(
                                    CONCAT(
                                        YEAR(c.agData),
                                        '-',
                                        MONTH(c.agData),
                                        '-',
                                        LEAST(
                                            p.opDiaFechamento, 
                                            DAY(LAST_DAY(c.agData))
                                        )
                                    )
                                )
                            /* Se o dia da agenda é igual ou anterior ao dia de fechamento, 
                               o período começou no dia de fechamento do mês anterior */
                            ELSE
                                DATE(
                                    CONCAT(
                                        YEAR(DATE_SUB(c.agData, INTERVAL 1 MONTH)),
                                        '-',
                                        MONTH(DATE_SUB(c.agData, INTERVAL 1 MONTH)),
                                        '-',
                                        LEAST(
                                            p.opDiaFechamento, 
                                            DAY(LAST_DAY(DATE_SUB(c.agData, INTERVAL 1 MONTH)))
                                        )
                                    )
                                )
                        END,
                        '%Y-%m-%d'
                    )
                WHEN p.opTipoFechamento = 'Semanal' THEN
                    CASE
                        /* Se o dia anterior é o dia de fechamento da semana anterior */
                        WHEN DAYOFWEEK(DATE_SUB(c.agData, INTERVAL 1 DAY)) = p.opDiaFechamento THEN
                            DATE_FORMAT(DATE_SUB(c.agData, INTERVAL 1 DAY), '%Y-%m-%d')
                        /* Se o dia da agenda é depois do dia de fechamento na semana, 
                           calcula o dia de fechamento anterior */
                        WHEN DAYOFWEEK(c.agData) > p.opDiaFechamento THEN
                            DATE_FORMAT(
                                DATE_SUB(c.agData, 
                                    INTERVAL (DAYOFWEEK(c.agData) - p.opDiaFechamento) DAY
                                ),
                                '%Y-%m-%d'
                            )
                        /* Se o dia da agenda é antes ou igual ao dia de fechamento na semana, 
                           calcula o dia de fechamento da semana anterior */
                        ELSE
                            DATE_FORMAT(
                                DATE_SUB(c.agData, 
                                    INTERVAL (DAYOFWEEK(c.agData) + 7 - p.opDiaFechamento) DAY
                                ),
                                '%Y-%m-%d'
                            )
                    END
                ELSE NULL
            END AS periodoInicio
            
        FROM 
            capOperPlantaoCheck c
        INNER JOIN 
            capInstSaudePlantao p 
            ON c.ceTipoPagamento = p.ceTipoPagamento
            AND c.opNrPlantao = p.opNrPlantao
            AND c.isInstSaude = p.isInstSaude
            AND c.laNome = p.laNome
            AND c.esEspecialidade = p.esEspecialidade
            AND c.ocNrContrato = p.ocNrContrato
            AND c.clCliente = p.clCliente
        LEFT JOIN 
            (
                SELECT
                    ceTipoPagamento,
                    opNrPlantao,
                    isInstSaude,
                    laNome,
                    esEspecialidade,
                    ocNrContrato,
                    clCliente,
                    MAX(adDataSolicitacao) AS adDataSolicitacao
                FROM 
                    capAtendimentoFechamento
                GROUP BY
                    ceTipoPagamento,
                    opNrPlantao,
                    isInstSaude,
                    laNome,
                    esEspecialidade,
                    ocNrContrato,
                    clCliente
            ) cf 
            ON cf.ceTipoPagamento = p.ceTipoPagamento
            AND cf.opNrPlantao = p.opNrPlantao
            AND cf.isInstSaude = p.isInstSaude
            AND cf.laNome = p.laNome
            AND cf.esEspecialidade = p.esEspecialidade
            AND cf.ocNrContrato = p.ocNrContrato
            AND cf.clCliente = p.clCliente
        WHERE
            c.opNrPlantao = ?
            AND c.isInstSaude = ? 
            AND c.laNome = ? 
            AND c.esEspecialidade = ? 
            AND c.ocNrContrato = ? 
            AND c.clCliente = ? 
            AND c.ceTipoPagamento = ? 
            AND p.opTipoFechamento <> 'Manual' 
            AND c.codFechamento IS NULL
            AND c.ocCheckOut IS NOT NULL
            AND c.ocCheckAprovado = 1
            AND (
                -- Para fechamento diário: cada dia que já passou
                (
                    p.opTipoFechamento = 'Diario'
                    AND DATE(c.agData) < CURDATE()
                )
                OR
                -- Para fechamento semanal: dias até o último dia de fechamento passado
                (
                    p.opTipoFechamento = 'Semanal'
                    AND DATE(c.agData) BETWEEN IFNULL(cf.adDataSolicitacao, p.opPeriodoIni)
                    AND CASE 
                        -- Se hoje for o dia de fechamento, consideramos até ontem
                        WHEN DAYOFWEEK(CURDATE()) = p.opDiaFechamento THEN
                            DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                        -- Se hoje for depois do dia de fechamento, consideramos até o último dia de fechamento
                        WHEN DAYOFWEEK(CURDATE()) > p.opDiaFechamento THEN
                            DATE_SUB(CURDATE(), INTERVAL (DAYOFWEEK(CURDATE()) - p.opDiaFechamento) DAY)
                        -- Se hoje for antes do dia de fechamento, consideramos até o dia de fechamento da semana passada
                        ELSE
                            DATE_SUB(CURDATE(), INTERVAL (DAYOFWEEK(CURDATE()) + 7 - p.opDiaFechamento) DAY)
                        END
                    AND DATE(c.agData) <= p.opPeriodoFim
                )
                OR
                -- Para fechamento mensal: dias até o último dia de fechamento mensal passado
                (
                    p.opTipoFechamento = 'Mensal'
                    AND DATE(c.agData) BETWEEN IFNULL(cf.adDataSolicitacao, p.opPeriodoIni)
                    AND CASE
                        -- Se hoje for o dia de fechamento, consideramos até ontem
                        WHEN DAY(CURDATE()) = p.opDiaFechamento THEN
                            DATE_SUB(CURDATE(), INTERVAL 1 DAY)
                        -- Se hoje for depois do dia de fechamento, consideramos até o dia de fechamento este mês
                        WHEN DAY(CURDATE()) > p.opDiaFechamento THEN
                            DATE(CONCAT(
                                YEAR(CURDATE()), '-',
                                MONTH(CURDATE()), '-',
                                p.opDiaFechamento
                            ))
                        -- Se hoje for antes do dia de fechamento, consideramos até o dia de fechamento do mês passado
                        ELSE
                            DATE(CONCAT(
                                YEAR(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '-',
                                MONTH(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)), '-',
                                LEAST(
                                    p.opDiaFechamento,
                                    DAY(LAST_DAY(DATE_SUB(CURDATE(), INTERVAL 1 MONTH)))
                                )
                            ))
                        END
                    AND DATE(c.agData) <= p.opPeriodoFim
                )
            )
        ORDER BY 
            periodoFechamento, c.agData;
    `;

    const params = [
      atendimento.opNrPlantao,
      atendimento.isInstSaude,
      atendimento.laNome,
      atendimento.esEspecialidade,
      atendimento.ocNrContrato,
      atendimento.clCliente,
      atendimento.ceTipoPagamento,
    ];

    return await dbQuery(sql, params);
  }
}
