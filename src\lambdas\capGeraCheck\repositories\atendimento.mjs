import { dbQuery } from 'capfunctions';

export class AtendimentoRepository {
  #clClientes = [];
  #whereClientes = '';

  constructor(clClientes = []) {
    this.#clClientes = clClientes;

    if (this.#clClientes.length) {
      this.#whereClientes = /*sql*/ `
        AND atendimentos.clCliente IN (${this.#clClientes.join(',')})
      `;
    }
  }

  async getAtendimentos() {
    const sql = /*sql*/ `
            SELECT 
                atendimentos.isInstSaude,
                atendimentos.laNome,
                atendimentos.esEspecialidade,
                atendimentos.ocNrContrato,
                atendimentos.clCliente,
                atendimentos.ceTipoPagamento,
                atendimentos.opNrPlantao,
                atendimentos.usCPFUsuarioAprovador,
                atendimentos.opDataDivulgacao,
                atendimentos.opAtivo,
                atendimentos.opTipoFechamento,
                atendimentos.opDataFechamento,
                atendimentos.opPeriodoIni,
                atendimentos.opPeriodoFim,
                atendimentos.psCPF,
                atendimentos.opDatarecebimento,
                atendimentos.opDataAntecipacao,
                atendimentos.opQtdHorasRequisitada,
                atendimentos.opQtdHorasRealizadas,
                atendimentos.opValorFixo,
                atendimentos.opValorHora,
                atendimentos.opValorUnit,
                atendimentos.opChaveAcesso,
                atendimentos.opSituacao,
                atendimentos.dtInclusao,
                atendimentos.dtModificacao,
                atendimentos.opDiaFechamento
            FROM
                capInstSaudePlantao as atendimentos
            JOIN 
                capInstSaudePlantaoAgenda as agendas ON 
                atendimentos.laNome = agendas.laNome
                AND atendimentos.opNrPlantao = agendas.opNrPlantao
                AND atendimentos.ceTipoPagamento = agendas.ceTipoPagamento
                AND atendimentos.clCliente = agendas.clCliente
                AND atendimentos.ocNrContrato = agendas.ocNrContrato
                AND atendimentos.esEspecialidade = agendas.esEspecialidade
                AND atendimentos.isInstSaude = agendas.isInstSaude
            LEFT JOIN 
                (
                    SELECT 
                        checks.isInstSaude,
                        checks.laNome,
                        checks.esEspecialidade,
                        checks.ocNrContrato,
                        checks.clCliente,
                        checks.ceTipoPagamento,
                        checks.opNrPlantao,
                        checks.agData,
                        SEC_TO_TIME(SUM(TIME_TO_SEC(TIMEDIFF(checks.ocCheckOut, checks.ocCheckIn)))) as totalHorasTrabalhadas
                    FROM 
                        capOperPlantaoCheck checks
                    GROUP BY 
                        checks.isInstSaude,
                        checks.laNome,
                        checks.esEspecialidade,
                        checks.ocNrContrato,
                        checks.clCliente,
                        checks.ceTipoPagamento,
                        checks.opNrPlantao,
                        checks.agData
                ) as totalCheckInOut ON (
                    totalCheckInOut.isInstSaude = agendas.isInstSaude
                    AND totalCheckInOut.laNome = agendas.laNome
                    AND totalCheckInOut.opNrPlantao = agendas.opNrPlantao
                    AND totalCheckInOut.ceTipoPagamento = agendas.ceTipoPagamento
                    AND totalCheckInOut.clCliente = agendas.clCliente
                    AND totalCheckInOut.ocNrContrato = agendas.ocNrContrato
                    AND totalCheckInOut.esEspecialidade = agendas.esEspecialidade
                    AND DATE(totalCheckInOut.agData) = DATE(agendas.agData)
                )
            WHERE 
                (
                    (atendimentos.opTipoFechamento = 'Manual' AND DATE(agendas.agData) <= CURDATE())
                    OR 
                    (atendimentos.opTipoFechamento = 'Diario' AND DATE(agendas.agData) < CURDATE())
                    OR 
                    (atendimentos.opTipoFechamento = 'Semanal' 
                        AND YEARWEEK(agendas.agData, 1) < YEARWEEK(CURDATE(), 1)
                        AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
                    )
                    OR
                    (atendimentos.opTipoFechamento = 'Mensal' 
                        AND DATE_FORMAT(agendas.agData, '%Y-%m') < DATE_FORMAT(CURDATE(), '%Y-%m')
                        AND DATE(agendas.agData) BETWEEN atendimentos.opPeriodoIni AND atendimentos.opPeriodoFim
                    )
                )
                AND atendimentos.opAtivo = 1
                AND atendimentos.opSituacao IN ('EmExecucao', 'AguardExecucao')
                AND (
                    totalCheckInOut.totalHorasTrabalhadas IS NULL 
                    OR TIME_TO_SEC(totalCheckInOut.totalHorasTrabalhadas) < 
                    ( TIMESTAMPDIFF(SECOND, agDataIni, agDataFim)
                        - TIME_TO_SEC(COALESCE(agIntervalo, '00:00:00')) )
                )
                ${this.#whereClientes}
            GROUP BY 
                atendimentos.opNrPlantao
            ORDER BY 
                atendimentos.opDataDivulgacao DESC;
        `;

    return await dbQuery(sql);
  }
}
