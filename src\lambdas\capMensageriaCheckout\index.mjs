import { sendTemplateMessage } from 'capcorpconf';
import { dbQuery, insert } from 'capfunctions';

export const handler = async () => {
  try {
    const agendas = await dbQuery(/*sql*/ `
      SELECT *
        FROM (
            SELECT 
                ROW_NUMBER() OVER (
                    PARTITION BY p.clCliente, a.opNrPlantao 
                    ORDER BY CAST(c.cmIncidencia AS UNSIGNED)
                ) as rn,
                u.usGenero,
            u.usNome,
            p.laNome,
            la.laEndereco,
            la.laNrEnd,
            la.laBairro,
            la.laCidade,
            la.laUF,
            u.usTelefone,
            a.isInstSaude,
            a.esEspecialidade,
            a.ocNrContrato,
            a.clCliente,
            a.ceTipoPagamento,
            a.opNrPlantao,
            m.meCodigo,
            c.cmIncidencia,
            a.agData,
            a.agHoraIni,
            a.agHoraFim
            FROM capInstSaudePlantao p
            JOIN capInstSaudePlantaoAgenda a
                ON p.isInstSaude = a.isInstSaude
                AND p.laNome = a.laNome
                AND p.esEspecialidade = a.esEspecialidade
                AND p.ocNrContrato = a.ocNrContrato
                AND p.clCliente = a.clCliente
                AND p.ceTipoPagamento = a.ceTipoPagamento
                AND p.opNrPlantao = a.opNrPlantao
            JOIN capProfissionalSaude ps 
                ON ps.psCPF = p.psCPF
            JOIN capUsuario u 
                ON ps.psCPF = u.usCPFUsuario
            JOIN capInstSaudeLocalAtendimento la
                ON la.isInstSaude = p.isInstSaude
                AND la.laNome = p.laNome
            JOIN capCliMensagem c 
                ON p.clCliente = c.clCliente
            JOIN capMensageria m 
                ON c.meCodigo = m.meCodigo
            WHERE 
                a.agData = CURDATE()
                AND m.meFrequencia = 'M'
                AND m.meModeloMeta = 'attcheckout001'
                AND NOW() >= TIMESTAMP(CONCAT(a.agData, ' ', a.agHoraFim))
				AND CAST(c.cmIncidencia AS UNSIGNED) >= TIMESTAMPDIFF(MINUTE, TIMESTAMP(CONCAT(a.agData, ' ', a.agHoraFim)), NOW())
            AND NOT EXISTS (
                  SELECT 1 FROM capMensPlantaoAgenda cmpa
                  WHERE
                    cmpa.isInstSaude = p.isInstSaude
                    AND cmpa.laNome = p.laNome
                    AND cmpa.esEspecialidade = p.esEspecialidade
                    AND cmpa.ocNrContrato = p.ocNrContrato
                    AND cmpa.clCliente = p.clCliente
                    AND cmpa.ceTipoPagamento = p.ceTipoPagamento
                    AND cmpa.opNrPlantao = p.opNrPlantao
                    AND cmpa.meCodigo = m.meCodigo
                    AND cmpa.cmIncidencia = c.cmIncidencia
                    AND cmpa.agData = a.agData
                    AND cmpa.agHoraIni = a.agHoraIni
                    AND cmpa.agHoraFim = a.agHoraFim
                )
        ) sub
        WHERE rn = 1;
    `);

    for (const agenda of agendas) {
      const parambody = {
        dr: agenda.usGenero === 'F' ? 'Dra.' : 'Dr.',
        nome: agenda.usNome,
        hospital: agenda.laNome,
        especialidade: agenda.esEspecialidade,
        local: `${agenda.laEndereco}, ${agenda.laNrEnd} - ${agenda.laBairro}, ${agenda.laCidade} - ${agenda.laUF}`,
      };

      await sendTemplateMessage({
        body: {
          phoneNumber: agenda.usTelefone,
          parambody,
          buttons: [],
          language: 'pt_BR',
          templateName: 'attcheckout001',
        },
      });

      await insert('capMensPlantaoAgenda', {
        isInstSaude: agenda.isInstSaude,
        laNome: agenda.laNome,
        esEspecialidade: agenda.esEspecialidade,
        ocNrContrato: agenda.ocNrContrato,
        clCliente: agenda.clCliente,
        ceTipoPagamento: agenda.ceTipoPagamento,
        opNrPlantao: agenda.opNrPlantao,
        meCodigo: agenda.meCodigo,
        cmIncidencia: agenda.cmIncidencia,
        agData: agenda.agData,
        agHoraIni: agenda.agHoraIni,
        agHoraFim: agenda.agHoraFim,
      });
    }
  } catch (error) {
    console.error(error);
  }
};
