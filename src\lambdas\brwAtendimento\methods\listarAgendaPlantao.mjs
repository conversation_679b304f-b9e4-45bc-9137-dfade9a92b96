import { baseResponse, dbQuery } from "capfunctions";
import { somarTotalHora, somarTotalHoras } from "../functions.mjs";

export async function _listarAgendaPlantao(event) {
  const listaAgenda = await dbQuery(
    /*sql*/ `
          SELECT
              a.*,
              SEC_TO_TIME(UNIX_TIMESTAMP(a.agDataFim) - UNIX_TIMESTAMP(a.agDataIni)) AS tempoAgendado,
              SEC_TO_TIME(
                  IFNULL(SUM(
                  GREATEST(
                      0,
                      LEAST(UNIX_TIMESTAMP(IFNULL(c.ocCheckOut, NOW())), UNIX_TIMESTAMP(a.agDataFim)) -
                      GREATEST(UNIX_TIMESTAMP(c.ocCheckIn), UNIX_TIMESTAMP(a.agDataIni))
                  )
                  ), 0)
              ) AS tempoCheckincheckout,
              CASE
                  WHEN COUNT(c.ocCheckIn) = 0 THEN 'SemCheckIn'
                  WHEN SUM(
                  GREATEST(
                      0,
                      LEAST(UNIX_TIMESTAMP(IFNULL(c.ocCheckOut, NOW())), UNIX_TIMESTAMP(a.agDataFim)) -
                      GREATEST(UNIX_TIMESTAMP(c.ocCheckIn), UNIX_TIMESTAMP(a.agDataIni))
                  )
                  ) >= (UNIX_TIMESTAMP(a.agDataFim) - UNIX_TIMESTAMP(a.agDataIni))
                  THEN 'CoberturaCompleta'
                  ELSE 'CoberturaParcial'
              END AS statusCobertura
              FROM
              capInstSaudePlantaoAgenda a
              LEFT JOIN
              capOperPlantaoCheck c
              ON
              a.isInstSaude = c.isInstSaude AND
              a.laNome = c.laNome AND
              a.esEspecialidade = c.esEspecialidade AND
              a.ocNrContrato = c.ocNrContrato AND
              a.clCliente = c.clCliente AND
              a.ceTipoPagamento = c.ceTipoPagamento AND
              a.opNrPlantao = c.opNrPlantao AND
              a.agData = c.agData
              WHERE a.isInstSaude = ? AND a.laNome = ? AND a.esEspecialidade = ? AND a.ocNrContrato = ? AND a.clCliente = ? AND a.opNrPlantao = ?
              GROUP BY
              a.isInstSaude, a.laNome, a.esEspecialidade, a.ocNrContrato, a.clCliente, a.ceTipoPagamento, a.opNrPlantao, a.agData, a.agHoraIni, a.agHoraFim, a.agDataIni, a.agDataFim
              ORDER BY
              a.isInstSaude, a.laNome, a.opNrPlantao, a.agData;
      `,
    [
      event.isInstSaude,
      event.laNome,
      event.esEspecialidade,
      event.ocNrContrato,
      event.clCliente,
      event.opNrPlantao,
    ]
  );

  if (!listaAgenda) {
    return baseResponse.notFound('Nada encontrado');
  }

  console.log({ listaAgenda });

  const [plantao] = await dbQuery(
    /*sql*/ `
          SELECT * FROM capInstSaudePlantao 
          WHERE (isInstSaude = ?) AND (laNome = ?) AND (esEspecialidade = ?) AND (ocNrContrato = ?) AND (clCliente = ?) AND (ceTipoPagamento = ?) AND (opNrPlantao = ?);  
      `,
    [
      event.isInstSaude,
      event.laNome,
      event.esEspecialidade,
      event.ocNrContrato,
      event.clCliente,
      listaAgenda[0].ceTipoPagamento,
      event.opNrPlantao,
    ]
  );

  const dias = {};

  listaAgenda.forEach((item) => {
    dias[item.agData] = {
      tipoValor: item.agTipoValor,
      ativo: item.agAtivo,
      diaSemana: item.agDiaSem,
      inicio: item.agHoraIni,
      fim: item.agHoraFim,
      intervalo: item.agIntervalo,
      tempoAgendado: item.tempoAgendado,
      tempoCheckincheckout: item.tempoCheckincheckout,
      statusCobertura: item.statusCobertura,
      horas: somarTotalHora({
        inicio: item.agHoraIni,
        fim: item.agHoraFim,
        intervalo: item.agIntervalo,
      }),
    };
  });

  return baseResponse.ok('Listado com sucesso', {
    dataInicial: plantao.opPeriodoIni,
    dataFinal: plantao.opPeriodoFim,
    horasPrevistas: somarTotalHoras(Object.values(dias)),
    diasPrevistos: listaAgenda.length,
    dias,
  });
}
