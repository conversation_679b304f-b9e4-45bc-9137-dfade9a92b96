import { sendMessage } from 'capcorpconf';

/*
Notes:
Identificar genero para inserir Dr. ou Dra. se nao tiver fica Dr(a).
Identificar horario para inserir "Bom dia", "Boa tarde" ou "Boa noite".
*/

/**
 *
 * @param {{
 *     context: {
 *         from: string;
 *         id: string;
 *     };
 *     from: string;
 *     id: string;
 *     timestamp: string;
 *     type: string;
 *     button: {
 *         payload: string;
 *         text: string;
 *     };
 * }} event
 * @returns
 */
export const handler = async (event) => {
    console.log('event: ', event);

    const { from: telefone, id, timestamp, type } = event;
    let respmens;

    if (type === 'button') {
        const { button } = event;
        const { from: telefoneFrom } = event.context;
        const { payload, text } = button;

        if (payload === 'confirmar_agendamento') {
            console.log('Confirmado');
            respmens = await sendMessage({
                number: telefone,
                message: 'Agradeçemos sua confirmação - Agendamento confirmado',
            });
        } else if (payload === 'cancelar_agendamento') {
            console.log('Confirmado');
            respmens = await sendMessage({
                number: telefone,
                message:
                    'Informamos a importancia do confirmamento, por favor entre em contato com sua Escalista - Agendamento não confirmado',
            });
        } else {
            respmens = await sendMessage({
                number: telefone,
                message:
                    'Por favor, utilize os botões para Confirmar ou Não o Agendamentoancia do confirmamento, por favor entre em contato com sua Escalista - Agendamento',
            });
        }
    } else {
        respmens = await sendMessage({
            number: telefone,
            message:
                'Obrigado por entrar em contato com a Capitale Holding, para dúvidas ou contato use <NAME_EMAIL>',
        });
    }
    console.log('respmens: ', respmens);

    const response = {
        statusCode: 200,
        body: JSON.stringify('Success'),
    };

    return response;
};
