import axios from 'axios';

const WHATSAPP_TOKEN = process.env.WHATSAPP_TOKEN;
const PHONE_NUMBER_ID = process.env.PHONE_NUMBER_ID;
const VERIFY_TOKEN = process.env.VERIFY_TOKEN;

/**
 * AWS Lambda Handler - Suporta API Gateway (GET e POST)
 */
export const handler = async (event) => {
    console.log('evento: ', event);
    const { httpMethod, path, queryStringParameters, body } = event;

    if (httpMethod === 'GET' && path === '/webhook') {
        return verifyWebhook(queryStringParameters);
    }

    if (httpMethod === 'POST' && path === '/send') {
        return await sendMessage(JSON.parse(body));
    }

    if (httpMethod === 'POST' && path === '/webhook') {
        return receiveMessage(JSON.parse(body));
    }

    return {
        statusCode: 404,
        body: JSON.stringify({ error: 'Rota não encontrada' }),
    };
};

/**
 * 🔑 Verifica o Webhook do Meta (GET)
 */
const verifyWebhook = (queryParams) => {
    const mode = queryParams['hub.mode'];
    const token = queryParams['hub.verify_token'];
    const challenge = queryParams['hub.challenge'];

    if (mode && token === VERIFY_TOKEN) {
        console.log('✅ Webhook verificado com sucesso!');
        return {
            statusCode: 200,
            body: challenge,
        };
    }

    return {
        statusCode: 403,
        body: JSON.stringify({ error: 'Falha na verificação' }),
    };
};

/**
 * 📩 Enviar mensagem para o WhatsApp Business API (POST)
 */
const sendMessage = async ({ number, message }) => {
    if (!number || !message) {
        return {
            statusCode: 400,
            body: JSON.stringify({ error: 'Número e mensagem são obrigatórios' }),
        };
    }

    try {
        const response = await axios.post(
            `https://graph.facebook.com/v18.0/${PHONE_NUMBER_ID}/messages`,
            {
                messaging_product: 'whatsapp',
                to: number,
                type: 'text',
                text: { body: message },
            },
            {
                headers: {
                    Authorization: `Bearer ${WHATSAPP_TOKEN}`,
                    'Content-Type': 'application/json',
                },
            }
        );

        return {
            statusCode: 200,
            body: JSON.stringify({ success: true, data: response.data }),
        };
    } catch (error) {
        return {
            statusCode: 500,
            body: JSON.stringify({
                error: 'Erro ao enviar mensagem',
                details: error.response?.data || error.message,
            }),
        };
    }
};

/**
 * 📥 Receber mensagens do WhatsApp Business API (POST)
 */
const receiveMessage = (body) => {
    if (body.object) {
        body.entry.forEach((entry) => {
            entry.changes.forEach((change) => {
                if (change.value.messages) {
                    const message = change.value.messages[0];
                    console.log(`📩 Nova mensagem de ${message.from}: ${message.text.body}`);
                }
            });
        });

        return { statusCode: 200, body: 'EVENT_RECEIVED' };
    }

    return { statusCode: 404, body: JSON.stringify({ error: 'Nenhuma mensagem recebida' }) };
};
