import { dbQuery, baseResponse } from 'capfunctions';
import { ENUM, enviaOcorrencia } from './functions.mjs';

const onlyNumber = (n) => String(n).replace(/\D/g, '');

const validate = (event) => {
  const { nome, email, telefone, mensagem, formType } = event;
  return !!nome && !!email && !!onlyNumber(telefone) && !!mensagem && !!formType;
};

export const handler = async (event) => {
  try {
    console.log(event);

    console.log('validate(event)', typeof validate(event));

    if (!validate(event) && !validate(event.body)) {
      return baseResponse.badRequest();
    }

    if (validate(event)) {
      event.body = event;
    }

    /**
     * formType
     * 50 - DPO
     * 10 - faleConosco
     * 30 - ouvidoria
     */
    const formType = Number(event.body.formType);

    if (!formType || ![ENUM.SOLICITACAODPO, ENUM.FALECONOSCO, ENUM.OUVIDORIA].includes(formType)) {
      return baseResponse.badRequest();
    }

    const tableName = {
      50: 'capSolicitacaoDPO',
      10: 'capFaleConosco',
      30: 'capOuvidoria',
    }[formType];

    const columns = [
      'dsNome',
      'dsEmail',
      'dsTelefone',
      'dsMensagem',
      'dsProtocolo',
      'dtCriacao',
      'dsAssunto',
    ];

    let assunto = formType === ENUM.SOLICITACAODPO ? 'Solicitação DPO' : event.body?.assunto;

    try {
      const res = await dbQuery(`
        SELECT current_timestamp ct;
        update capProtocolo set nrProtocolo = 1;
        select lpad(nrProtocolo, 7, '0') as nrProtocolo from capProtocolo;
      `);
      const { ct } = res[0][0];
      const { nrProtocolo } = res[2][0];

      const nrOperadora = 423548;
      const dtNow = new Date(ct);
      const dt = `${dtNow.getFullYear()}${(dtNow.getMonth() + 1).toString().padStart(2, '0')}${dtNow
        .getDate()
        .toString()
        .padStart(2, '0')}`;
      const dsProtocolo = `${nrOperadora}${dt}${formType}${nrProtocolo}`;

      const values = [
        event.body.nome,
        event.body.email,
        onlyNumber(event.body.telefone),
        event.body.mensagem,
        dsProtocolo,
        ct,
        assunto,
      ];
      await dbQuery(
        `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${columns
          .map(() => '?')
          .join(', ')})`,
        values
      );
    } catch (err) {
      console.log('Erro ao gerar protocolo', err);
    }

    const result = await enviaOcorrencia({
      assunto,
      nome: event.body.nome,
      email: event.body.email,
      telefone: event.body.telefone,
      mensagem: event.body.mensagem,
      formType,
      cpf: event.body?.cpf,
    });

    return baseResponse.ok(result.data.protocolo);
  } catch (error) {
    console.log('Catch no capSolicitacaoDPO', error);
    return baseResponse.error(
      'Ocorreu algum erro, tente novamente mas, se persistir entre em contato'
    );
  }
};

/*

CREATE TABLE `capProtocolo` (
  `nrProtocolo` int NOT NULL AUTO_INCREMENT,
  PRIMARY KEY (`nrProtocolo`)
);

delimiter tt
create TRIGGER `trgcapProtocolo` BEFORE update ON `capProtocolo` FOR EACH ROW BEGIN
	set new.nrProtocolo = (select ifnull(max(convert(old.nrProtocolo, signed integer)),0) + 1 from capProtocolo);     
END;
delimiter 

insert into `capProtocolo` values (1);

CREATE TABLE `capSolicitacaoDPO` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
);

CREATE TABLE `capFaleConosco` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
);

CREATE TABLE `capOuvidoria` (
  `dsNome` varchar(200) NOT NULL,
  `dsEmail` varchar(200) DEFAULT NULL,
  `dsTelefone` varchar(50) DEFAULT NULL,
  `dsMensagem` longtext NOT NULL,
  `dtCriacao` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `dsProtocolo` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`dtCriacao`,`dsNome`)
);

*/
