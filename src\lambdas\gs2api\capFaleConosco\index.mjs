import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

const sesClient = new SESClient({ region: 'us-east-2' });

async function sendEmailSES(to, subject, body) {
  const params = {
    Destination: {
      ToAddresses: [to],
    },
    Message: {
      Body: {
        Text: { Data: body },
      },
      Subject: { Data: subject },
    },
    Source: '<EMAIL>',
  };

  try {
    const command = new SendEmailCommand(params);
    const data = await sesClient.send(command);
    console.log('Email enviado:', data.MessageId);
    return data;
  } catch (error) {
    console.error('Erro ao enviar email:', error);
    throw error;
  }
}

export const handler = async (event) => {
  console.log(event);
  try {
    
    await sendEmailSES(
      '<EMAIL>',
      `Contato Fale Conosco - ${event.body.assunto} - ${event.body.nome}`,
      `Formulário enviado de ${event.body.origin}\n\nNome: ${event.body.nome}\nTelefone: ${event.body.telefone}\nEmail: ${event.body.email}\nAssunto: ${event.body.assunto}\nMensagem: ${event.body.mensagem}`
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Email enviado com sucesso!',
      }),
    };
  } catch (error) {
    console.error('Erro ao enviar email via SES:', error);
  }
};
