import {
  baseResponse,
  getUserFromToken
} from 'capfunctions';
import { _insert, _update, _list, _find, _delete } from './functions.mjs';
const actions = { _insert, _update, _list, _find, _delete };

export const handler = async (event) => {
  console.log(event);

  try {
    const userFromToken = await getUserFromToken(event.headers?.Authorization);
    
    if(!!userFromToken){
      if (!(event.body?.method in actions || event.method in actions)) {
        return baseResponse.notFound("Método não encontrado");
      }
  
      return {
        newToken: userFromToken?.body?.newToken,
        ...(await actions[event.body?.method || event.method]({ ...( event.body || event ), ... userFromToken } )),
      };
    }
    
    return baseResponse.unauthorized('Token de sessão expirado, por favor, faça login novamente');
  } catch (error) {
    console.log("Erro ao processar a requisição", error);

    return baseResponse.error("Erro ao processar a requisição. CATCH HANDLER brwGrupoMenu");
  }
};


