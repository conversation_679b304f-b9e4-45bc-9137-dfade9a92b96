#!/usr/bin/env node

/**
 * GS2 Chatbot Flow Test
 * Tests the complete chatbot flow based on JSON specifications
 */

import { StateMachine, STATES } from './services/stateMachine.mjs';
import { Database } from './database.mjs';
import { MessageBuilder } from './utils/messageBuilder.mjs';

// Mock database for testing
class MockDatabase extends Database {
  constructor() {
    super();
    this.sessions = new Map();
    this.phoneToSession = new Map(); // Map phone numbers to session IDs
    this.interactions = [];
    this.userData = new Map();
    this.clinics = [
      { id: 1, name: 'Gastrocentro', razao_social: 'Gastrocentro Ltda', cnpj: '12345678000195' },
      { id: 2, name: 'H2', razao_social: 'Hospital H2 S/A', cnpj: '98765432000112' }
    ];
  }

  async initialize() {
    console.log('Mock database initialized');
    return true;
  }

  async executeQuery(query, params = []) {
    // Mock implementation - don't call parent's executeQuery
    return [];
  }

  async getUserSession(sessionId) {
    return this.sessions.get(sessionId) || null;
  }

  async getLatestSessionByPhone(phoneNumber) {
    const sessionId = this.phoneToSession.get(phoneNumber);
    if (sessionId) {
      return this.sessions.get(sessionId) || null;
    }
    return null;
  }

  async createUserSession(phoneNumber, initialState = 'INITIAL', forceNew = false) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      phone_number: phoneNumber,
      current_state: initialState,
      session_data: {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    this.sessions.set(sessionId, session);
    this.phoneToSession.set(phoneNumber, sessionId);
    return session;
  }

  async updateUserSession(sessionId, state, data = {}) {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.current_state = state;
      session.session_data = data;
      session.updated_at = new Date().toISOString();
    }
    return session;
  }

  async saveUserData(sessionId, clinicId, userData) {
    this.userData.set(sessionId, { clinicId, ...userData });
  }

  async logInteraction(sessionId, messageType, messageContent) {
    this.interactions.push({
      sessionId,
      messageType,
      messageContent,
      timestamp: new Date().toISOString(),
    });
  }

  async findClinic(name) {
    return this.clinics.filter(clinic => 
      clinic.name.toLowerCase().includes(name.toLowerCase())
    );
  }

  async deleteUserSession(sessionId) {
    this.sessions.delete(sessionId);
  }

  generateSessionId() {
    return 'mock-session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }
}

// Test runner class
class ChatbotFlowTest {
  constructor() {
    this.mockDb = new MockDatabase();
    this.messageBuilder = new MessageBuilder();
    this.stateMachine = new StateMachine(this.mockDb, this.messageBuilder);
    this.testUserId = '5511999999999';
    this.testPhoneNumber = '+5511999999999';
  }

  async runTests() {
    console.log('🤖 Starting GS2 Chatbot Flow Tests');
    console.log('=====================================');

    await this.mockDb.initialize();

    try {
      await this.testCompleteFlow();
      await this.testDataCorrectionFlow();
      await this.testFAQFlow();
      await this.testExitFlow();
      
      console.log('\\n✅ All tests completed successfully!');
    } catch (error) {
      console.error('\\n❌ Test failed:', error.message);
      console.error(error.stack);
    }
  }

  async testCompleteFlow() {
    console.log('\\n🧪 Testing Complete Flow (Happy Path)');
    console.log('-------------------------------------');

    // Test 1: Initial trigger
    let response = await this.stateMachine.processMessage(
      this.testUserId,
      'GS2',
      this.testPhoneNumber
    );
    console.log('1. Initial trigger:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.GREETING);

    // Test 2: Greeting acceptance
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Claro, vamos lá',
      this.testPhoneNumber
    );
    console.log('2. Greeting acceptance:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.CLINIC_REQUEST);

    // Test 3: Clinic selection
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Gastrocentro',
      this.testPhoneNumber
    );
    console.log('3. Clinic selection:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.CLINIC_CONFIRMATION);

    // Test 4: Clinic confirmation
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Sim, isso mesmo',
      this.testPhoneNumber
    );
    console.log('4. Clinic confirmation:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.NAME_REQUEST);

    // Test 5: Name input
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Dr. João da Silva',
      this.testPhoneNumber
    );
    console.log('5. Name input:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.CPF_REQUEST);

    // Test 6: CPF input (using valid CPF)
    response = await this.stateMachine.processMessage(
      this.testUserId,
      '11144477735',
      this.testPhoneNumber
    );
    console.log('6. CPF input:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.EMAIL_REQUEST);

    // Test 7: Email input
    response = await this.stateMachine.processMessage(
      this.testUserId,
      '<EMAIL>',
      this.testPhoneNumber
    );
    console.log('7. Email input:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.DATA_CONFIRMATION);

    // Test 8: Data confirmation
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Está certo!',
      this.testPhoneNumber
    );
    console.log('8. Data confirmation:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.TERMS_PRESENTATION);
    
    // Verify that terms presentation includes document
    if (Array.isArray(response.message)) {
      const hasDocument = response.message.some(msg => 
        msg && typeof msg === 'object' && msg.type === 'document'
      );
      if (hasDocument) {
        console.log('   ✅ Terms presentation includes PDF document');
      } else {
        console.log('   ⚠️  Terms presentation missing PDF document');
      }
    }

    // Test 9: Terms acceptance
    response = await this.stateMachine.processMessage(
      this.testUserId,
      'Sim, de acordo',
      this.testPhoneNumber
    );
    console.log('9. Terms acceptance:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.COMPLETION);

    console.log('✅ Complete flow test passed');
  }

  async testDataCorrectionFlow() {
    console.log('\\n🧪 Testing Data Correction Flow');
    console.log('--------------------------------');

    const correctionUserId = '5511888888888';

    // Setup initial data
    await this.stateMachine.processMessage(correctionUserId, 'GS2', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, 'Claro, vamos lá', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, 'H2', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, 'Sim, isso mesmo', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, 'Dr. Maria Santos', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, '11144477735', this.testPhoneNumber);
    await this.stateMachine.processMessage(correctionUserId, '<EMAIL>', this.testPhoneNumber);

    // Test correction flow
    let response = await this.stateMachine.processMessage(
      correctionUserId,
      'Preciso corrigir',
      this.testPhoneNumber
    );
    console.log('1. Request correction:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.DATA_CORRECTION);

    // Test name correction
    response = await this.stateMachine.processMessage(
      correctionUserId,
      'Nome',
      this.testPhoneNumber
    );
    console.log('2. Select name correction:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.NAME_REQUEST);

    // Correct name
    response = await this.stateMachine.processMessage(
      correctionUserId,
      'Dra. Maria Santos Correia',
      this.testPhoneNumber
    );
    console.log('3. Corrected name:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.DATA_CONFIRMATION);

    console.log('✅ Data correction flow test passed');
  }

  async testFAQFlow() {
    console.log('\\n🧪 Testing FAQ Flow');
    console.log('--------------------');

    const faqUserId = '5511777777777';

    // Setup to terms presentation
    await this.setupUserToTermsPresentation(faqUserId);

    // Test FAQ request
    let response = await this.stateMachine.processMessage(
      faqUserId,
      'Quero saber mais',
      this.testPhoneNumber
    );
    console.log('1. Request FAQ:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.FAQ_MENU);

    // Test FAQ topic selection
    response = await this.stateMachine.processMessage(
      faqUserId,
      'Como funciona?',
      this.testPhoneNumber
    );
    console.log('2. FAQ topic selection:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.FAQ_RESPONSE);

    // Test return to terms
    response = await this.stateMachine.processMessage(
      faqUserId,
      'Assinar os termos',
      this.testPhoneNumber
    );
    console.log('3. Return to terms:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.TERMS_PRESENTATION);

    console.log('✅ FAQ flow test passed');
  }

  async testExitFlow() {
    console.log('\\n🧪 Testing Exit Flow');
    console.log('---------------------');

    const exitUserId = '5511666666666';

    // Test exit from greeting
    await this.stateMachine.processMessage(exitUserId, 'GS2', this.testPhoneNumber);
    let response = await this.stateMachine.processMessage(
      exitUserId,
      'Sair',
      this.testPhoneNumber
    );
    console.log('1. Exit from conversation:', this.formatResponse(response));
    this.assertState(response.nextState, STATES.END);

    console.log('✅ Exit flow test passed');
  }

  async setupUserToTermsPresentation(userId) {
    await this.stateMachine.processMessage(userId, 'GS2', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, 'Claro, vamos lá', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, 'Gastrocentro', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, 'Sim, isso mesmo', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, 'Dr. Teste', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, '11144477735', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, '<EMAIL>', this.testPhoneNumber);
    await this.stateMachine.processMessage(userId, 'Está certo!', this.testPhoneNumber);
  }

  formatResponse(response) {
    if (!response) return 'null';
    
    const messageStr = Array.isArray(response.message) 
      ? `[${response.message.length} messages]`
      : typeof response.message === 'object' 
        ? `[${response.message.type} message]`
        : response.message.substring(0, 100) + (response.message.length > 100 ? '...' : '');
    
    return `State: ${response.nextState}, Message: ${messageStr}`;
  }

  assertState(actual, expected) {
    if (actual !== expected) {
      throw new Error(`Expected state ${expected}, got ${actual}`);
    }
  }
}

// Run tests
const testRunner = new ChatbotFlowTest();
testRunner.runTests().catch(console.error);