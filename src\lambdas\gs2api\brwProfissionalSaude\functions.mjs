import bcrypt from 'bcryptjs';
import { handler as capKuaraIntegracao  } from '../capKuaraIntegracao/index.mjs'

import {
  createResponse,
  checkData,
  execQuery,
  baseResponse,
  dbQuery,
  insert,
  update,
  onlyNumber,
  isEmailValid,
  lambdaInvoke,
} from 'capfunctions';

import { _list } from './list.mjs';

const usCargo = 'Profissional de Saude';

async function _listAll(event) {
  if (!event.clCliente) {
    return baseResponse.badRequest('Dados insuficentes.');
  }

  const userCPF = event.body.user;

  const response = await dbQuery(
    /*sql*/ `
          SELECT
            cps.psCPF,
            u.usNome
          FROM 
            capProfissionalSaude cps,
            capUsuario u,
            capProfSaudeCliente cliProf
          WHERE cliProf.clCliente = ? 
          AND cliProf.psCPF = cps.psCPF
          AND cps.psCPF = u.usCPFUsuario
          GROUP BY cps.psCPF
      `,
    [event.clCliente]
  );

  return baseResponse.ok('', response ?? []);
}

async function _find(event) {
  try {
    if (checkData(event, ['usCPFUsuario']))
      return createResponse(500, 'Dados insuficentes.', null, null);

    const query = /*sql*/ `
        SELECT
          a.usCPFUsuario, 
          a.usNome, 
          a.usNomeMae, 
          a.usTelefone, 
          a.usEmail, 
          a.usCEP, 
          a.usEndereco, 
          a.usNrEnd, 
          a.usComplEnd, 
          a.usDatNasc,
          a.usBairro, 
          a.usCidade, 
          a.usUF, 
          a.usCargo, 
          a.usDepartamento, 
          a.usRegFuncional, 
          a.usAtivo,
          b.psCPF, 
          b.psRG, 
          b.psContatoSecundario, 
          b.psCNES, 
          b.psCNS, 
          b.ccConselhoClasse, 
          b.psCCNrReg, 
          b.psCCCidadeReg, 
          b.psCCUFReg, 
          b.psCCOrgEmissor, 
          b.bcBancoNR, 
          b.psAgenciaBanco, 
          b.psContaCorrente, 
          b.psAgenciaDigito, 
          b.psContaCorrenteDigito, 
          b.psContaCorrenteCNPJ, 
          b.pxPIXTipo, 
          b.psPIXChave, 
          b.aeAssEletronicaTipo, 
          b.psAssinaturaEletronica, 
          b.psAtivo,
          a.usEstadoCivil,
          a.usGenero,
          a.usNacionalidade,
          a.psOnboardingPendente,
          JSON_ARRAYAGG(d.esEspecialidade) as especialidades
        FROM capUsuario a
        JOIN capProfissionalSaude b on (a.usCPFUsuario=b.psCPF)
        JOIN capProfSaudeEspecialidade d on (a.usCPFUsuario=d.psCPF)
        WHERE a.usCPFUsuario = ?
      `;
    //                  AND c.clCliente like ?
    //                  JOIN capProfSaudeCliente c on (a.usCPFUsuario=c.psCPF)

    //const params = [ event['usCPFUsuario'] , '%' + (event['clCliente'] || '') + '%' ];
    const params = [event['usCPFUsuario']];
    const response = await execQuery(query, params);

    if (response && response.results && response.results.length > 0) {
      if (response.results[0].especialidades)
        response.results[0].especialidades = JSON.parse(response.results[0].especialidades);

      return baseResponse.created('Listado com sucesso', response.results && response.results[0]);
    }
    return baseResponse.badRequest('Não foi possível encontrar o profissional solicitado');
  } catch (error) {
    console.log('ERROR _find', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _insert(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'clCliente',
          'psCPF',
          'bcBancoNR',
          'psAgenciaBanco',
          'psContaCorrente',
          'psAgenciaDigito',
          'psContaCorrenteDigito',
          'pxPIXTipo',
          'psPIXChave',
          'aeAssEletronicaTipo',
          'usEstadoCivil',
          'usGenero',
          'usNacionalidade',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    if (event.novo) {
      // // - CHAMAR LAMBDA PARA ENVIAR EMAIL PARA USUARIO CADAASTRADO

      const respins = await insert(
        'capUsuario',
        {
          usCPFUsuario: event.psCPF,
          usNome: event.usNome.toUpperCase(),
          usNomeMae: (event.usNomeMae || '').toUpperCase(),
          usTelefone: event.usTelefone,
          usEmail: event.usEmail,
          usDatNasc: event.usDatNasc,
          usCEP: event.usCEP,
          usEndereco: event.usEndereco,
          usNrEnd: event.usNrEnd,
          usComplEnd: event.usComplEnd || '',
          usBairro: event.usBairro,
          usCidade: event.usCidade,
          usUF: event.usUF,
          usCargo,
          usDepartamento: 'PS',
          usRegFuncional: '1',
          usSenha: bcrypt.hashSync(event.psCPF.substring(0, 6)),
          usEstadoCivil: event.usEstadoCivil,
          usGenero: event.usGenero,
          usNacionalidade: event.usNacionalidade,
          usAtivo: 1,
        },
        event
      );

      if (!respins.success) {
        return baseResponse.badRequest('Erro na Inserção do Usuario.');
      }

      const [[tipoPox], [banco]] = await dbQuery(
        /*sql*/ `
                    SELECT pxPIXDescricao as descricao FROM capTipoPIX WHERE pxPIXTipo = ?;
                    SELECT bcBancoNome as nome from capBanco WHERE bcBancoNR = ?;
            `,
        [event.pxPIXTipo, event.bcBancoNR]
      );

      try {
        await capKuaraIntegracao({
          method: '_cadastrarPessoaFisica',
          cpf: event.psCPF,
          nome: event.usNome,
          email: event.usEmail,
          telefone: '+55' + event.usTelefone,
          nacionalidade: event?.usNacionalidade || 'Brazilian',
          data_nascimento: event.usDatNasc,
          genero: event?.genero || '',
          estado_civil: event?.usEstadoCivil || '',
          ocupacao: usCargo,
          especialidade: event?.usEspecialidade || '',
          tipo_documento: 'CPF',
          numero_documento: event?.usNumeroDocumento || event.psCPF,
          data_emissao_documento: event?.usDataEmissaoDocumento || '1970-01-01',
          orgao_expedidor: event?.usOrgaoExpedidor || '',
          estado_documento: event?.usEstadoDocumento || '',
          cep: event?.usCEP || '',
          logradouro: event?.usEndereco || '',
          numero_endereco: event?.usNrEnd || '',
          complemento: event?.usComplEnd || '',
          bairro: event?.usBairro || '',
          cidade: event?.usCidade || '',
          estado: event?.usUF || '',
          nome_banco: banco?.nome,
          codigo_banco: event?.bcBancoNR || '',
          agencia: event?.psAgenciaBanco || '',
          agencia_digito: event?.psAgenciaDigito || '',
          conta_bancaria: event?.psContaCorrente || '',
          conta_digito: event?.psContaCorrenteDigito || '',
          tipo_chave_pix: tipoPox?.descricao,
          chave_pix: event?.psPIXChave || '',
          estado_civil: event?.usEstadoCivil || '',
          genero: event?.usGenero || '',
          nacionalidade: event?.usNacionalidade || '',
        });
      } catch (error) {
        console.error('Erro ao cadastrar pessoa fisica na Kuara', error);
      }
    }

    const entity = {
      psCPF: event.psCPF,
      psRG: event.psRG,
      psCNES: event.psCNES,
      psCNS: event.psCNS,
      ccConselhoClasse: event.ccConselhoClasse,
      psCCNrReg: event.psCCNrReg,
      psCCCidadeReg: event.psCCCidadeReg,
      psCCUFReg: event.psCCUFReg,
      psCCOrgEmissor: event.psCCOrgEmissor,
      bcBancoNR: event.bcBancoNR,
      psAgenciaBanco: event.psAgenciaBanco,
      psContaCorrente: event.psContaCorrente,
      psAgenciaDigito: event.psAgenciaDigito,
      psContaCorrenteDigito: event.psContaCorrenteDigito,
      pxPIXTipo: event.pxPIXTipo,
      psPIXChave: event.psPIXChave,
      aeAssEletronicaTipo: event.aeAssEletronicaTipo,
    };
    //      dtInclusao: 'current_timestamp'
    //      clCliente: event.clCliente, //associar
    const response = await insert('capProfissionalSaude', entity, event);

    if (response.success) {
      if (event.especialidades) {
        const especs = event.especialidades;
        var respesp, entesp;
        for (const espec of especs) {
          entesp = {
            psCPF: event.psCPF,
            esEspecialidade: espec,
          };
          respesp = await insert('capProfSaudeEspecialidade', entesp, event);
          console.log('Inseriu Especialidade', entesp);
          if (!respesp.success) {
            return baseResponse.badRequest(respesp);
          }
        }
      }

      const responseUsuarioGrupoMenu = await insert(
        'brcUsuarioGrupoMenu',
        {
          sgCliente: 'cap',
          usuarioCPF: event.psCPF,
          sgInstSaude: 'capitaleholding',
          gmNome: 'profsaude',
        },
        event
      );

      console.log('Inseriu GrupoMenu', responseUsuarioGrupoMenu);

      const checkrel = await dbQuery(
        ' SELECT 1 FROM capProfSaudeCliente WHERE psCPF = ? AND clCliente = ?',
        [event.psCPF, event.clCliente]
      );

      console.log(checkrel);

      if (checkrel.length < 1) {
        const relacao = {
          clCliente: event.clCliente,
          psCPF: onlyNumber(event.psCPF),
        };

        const resprel = await insert('capProfSaudeCliente', relacao, event);

        if (resprel.success) {
          console.log('relacionado');
        } else {
          console.log('nao relacionado');
        }
      }

      return baseResponse.created('Criado com successo');
    }

    return baseResponse.notFound(response);
  } catch (error) {
    console.log('ERROR _insert', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _update(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'clCliente',
          'psCPF',
          'bcBancoNR',
          'psAgenciaBanco',
          'psContaCorrente',
          'psAgenciaDigito',
          'psContaCorrenteDigito',
          'pxPIXTipo',
          'psPIXChave',
          'aeAssEletronicaTipo',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    if (!isEmailValid(event.clEmail)) {
      //return createResponse(400, 'Formato de email inválido.', '', null);
    }

    const entity = {
      psRG: event.psRG,
      psContatoSecundario: event.psContatoSecundario,
      psCNES: event.psCNES,
      psCNS: event.psCNS,
      ccConselhoClasse: event.ccConselhoClasse,
      psCCNrReg: event.psCCNrReg,
      psCCCidadeReg: event.psCCCidadeReg,
      psCCUFReg: event.psCCUFReg,
      psCCOrgEmissor: event.psCCOrgEmissor,
      bcBancoNR: event.bcBancoNR,
      psAgenciaBanco: event.psAgenciaBanco,
      psContaCorrente: event.psContaCorrente,
      psAgenciaDigito: event.psAgenciaDigito,
      psContaCorrenteDigito: event.psContaCorrenteDigito,
      psContaCorrenteCNPJ: event.psContaCorrenteCNPJ,
      pxPIXTipo: event.pxPIXTipo,
      psPIXChave: event.psPIXChave,
      aeAssEletronicaTipo: event.aeAssEletronicaTipo,
      psAssinaturaEletronica: event.psAssinaturaEletronica,
    };
    //      dtModificacao: 'current_timestamp' truncando

    const response = await update(
      'capProfissionalSaude',
      entity,
      {
        psCPF: onlyNumber(event.psCPF),
      },
      event
    );

    console.log(response);

    const capUsuarioEntity = {
      usNome: event.usNome.toUpperCase(),
      usTelefone: onlyNumber(event.usTelefone),
      usEmail: event.usEmail,
      usCEP: onlyNumber(event.usCEP),
      usEndereco: event.usEndereco,
      usDatNasc: event.usDatNasc,
      usNrEnd: event.usNrEnd,
      usComplEnd: event.usComplEnd === undefined ? '' : event.usComplEnd,
      usBairro: event.usBairro,
      usCidade: event.usCidade,
      usUF: event.usUF,
      usEstadoCivil: event.usEstadoCivil,
      usGenero: event.usGenero,
      usNacionalidade: event.usNacionalidade,
      psOnboardingPendente: event?.psOnboardingPendente ? 1 : 0,
    };

    const responseCapUsuario = await update(
      'capUsuario',
      capUsuarioEntity,
      {
        usCPFUsuario: onlyNumber(event.psCPF),
      },
      event
    );

    if (response.success && responseCapUsuario.success) {
      if (event.especialidades) {
        const especs = event.especialidades;
        var respesp, entesp;

        const especialidadesQuery = `SELECT * from capProfSaudeEspecialidade WHERE psCPF = "${event.psCPF}"`;
        const responseEspecialidadesQuery = await execQuery(especialidadesQuery);

        if (
          responseEspecialidadesQuery.success &&
          responseEspecialidadesQuery?.results?.length > 0
        ) {
          const deletePromises = [];
          for (const especialidade of responseEspecialidadesQuery?.results) {
            const deleteQuery = `
            DELETE from capProfSaudeEspecialidade 
            WHERE 
              psCPF = "${event.psCPF}" 
              AND esEspecialidade = "${especialidade.esEspecialidade}"`;

            deletePromises.push(execQuery(deleteQuery));
          }

          await Promise.all(deletePromises);
        }

        for (const espec of especs) {
          entesp = {
            psCPF: event.psCPF,
            esEspecialidade: espec,
          };
          respesp = await insert('capProfSaudeEspecialidade', entesp, event);
        }
      }

      const checkrel = await dbQuery(
        ' SELECT 1 FROM capProfSaudeCliente WHERE psCPF = ? AND clCliente = ?',
        [event.psCPF, event.clCliente]
      );

      console.log(checkrel);

      if (checkrel.length < 1) {
        const relacao = {
          clCliente: event.clCliente,
          psCPF: onlyNumber(event.psCPF),
        };
        const resprel = await insert('capProfSaudeCliente', relacao, event);
        if (resprel.success) console.log('relacionado');
        else console.log('nao relacionado');
      }

      const [[tipoPox], [banco]] = await dbQuery(
        `
          SELECT pxPIXDescricao as descricao FROM capTipoPIX WHERE pxPIXTipo = ?;
          SELECT bcBancoNome as nome from capBanco WHERE bcBancoNR = ?;
        `,
        [event.pxPIXTipo, event.bcBancoNR]
      );

      await capKuaraIntegracao({
        method: '_cadastrarPessoaFisica',
        cpf: event.psCPF,
        nome: event.usNome,
        email: event.usEmail,
        telefone: '+55' + event.usTelefone,
        nacionalidade: event?.usNacionalidade || 'Brazilian',
        data_nascimento: event.usDatNasc,
        genero: event?.genero || '',
        estado_civil: event?.usEstadoCivil || '',
        ocupacao: usCargo,
        especialidade: event?.usEspecialidade || '',
        tipo_documento: 'CPF',
        numero_documento: event?.usNumeroDocumento || event.psCPF,
        data_emissao_documento: event?.usDataEmissaoDocumento || '1970-01-01',
        orgao_expedidor: event?.usOrgaoExpedidor || '',
        estado_documento: event?.usEstadoDocumento || '',
        cep: event?.usCEP || '',
        logradouro: event?.usEndereco || '',
        numero_endereco: event?.usNrEnd || '',
        complemento: event?.usComplEnd || '',
        bairro: event?.usBairro || '',
        cidade: event?.usCidade || '',
        estado: event?.usUF || '',
        nome_banco: banco?.nome,
        codigo_banco: event?.bcBancoNR || '',
        agencia: event?.psAgenciaBanco || '',
        agencia_digito: event?.psAgenciaDigito || '',
        conta_bancaria: event?.psContaCorrente || '',
        conta_digito: event?.psContaCorrenteDigito || '',
        tipo_chave_pix: tipoPox?.descricao,
        chave_pix: event?.psPIXChave || '',
        estado_civil: event?.usEstadoCivil || '',
        genero: event?.usGenero || '',
        nacionalidade: event?.usNacionalidade || '',
      });

      return baseResponse.created('Atualizado com sucesso', response.results);
    }

    console.info(response.success);

    return baseResponse.error('Erro ao atualizar profissional');
  } catch (error) {
    console.log('ERROR _update', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

export { _insert, _update, _list, _find, _listAll };
