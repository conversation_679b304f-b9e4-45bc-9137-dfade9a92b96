import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

import { getAtendimentos } from './functions.mjs';

const sqsClient = new SQSClient({ region: 'us-east-2' });

const performAction = async (cliente, tipoAtendimento) => {
  try {
    let page = 0;

    while (true) {
      const atendimentos = await getAtendimentos(cliente, tipoAtendimento, page, 10);

      if (!atendimentos || atendimentos.length === 0) {
        console.log('No more data to process.');
        break;
      }

      page = atendimentos[atendimentos.length - 1].opNrPlantao;

      const timestamp = new Date().getTime();
      const deduplicationId = `${page}-${tipoAtendimento}-${timestamp}`;

      const queueUrl =
        'https://sqs.us-east-2.amazonaws.com/730335345607/capFilaClienteFechamento.fifo';

      const params = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(atendimentos),
        MessageGroupId: 'capFilaClienteAtendimento',
        MessageDeduplicationId: deduplicationId,
      };

      const command = new SendMessageCommand(params);
      const response = await sqsClient.send(command);

      console.log({
        MessageId: response.MessageId,
      });
    }
  } catch (error) {
    console.error('Error:', error);
  }
};

export const handler = async (event) => {
  try {
    console.log(event);

    for (const record of event.Records) {
      const body = JSON.parse(record.body);
      const cliente = body.clCliente;
      const tipoAtendimento = body.tipoAtendimento;

      await performAction(cliente, tipoAtendimento);
    }
  } catch (error) {
    console.error('Error:', error);
  }
};
