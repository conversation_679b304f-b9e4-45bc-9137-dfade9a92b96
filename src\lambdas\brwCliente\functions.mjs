import {
    createResponse,
    checkData,
    execQuery,
    baseResponse,
    dbQuery,
    insert,
    update,
    onlyNumber,
    isEmailValid,
    lambdaInvoke,
    find,
} from 'capfunctions';

async function _list(event) {
    console.log('event ', event);
    try {

        const isMaster = event.body?.groups?.find(
            (group) => group === 'gestorcliente'
        );

        let complement = ``;
        const params = ['%' + (event['searchName'] || '') + '%', event.body.user];

        if (!isMaster) {
            complement = /*sql*/`
                inner join capClienteUsuario cliUsu
                on cliUsu.clCliente = a.clCliente and cliUsu.usCPFUsuario = ?
            `;
            params.unshift(event.body.user);
        }

        const query = /*sql*/`
            SELECT
                a.clCliente,
                a.clNomeCliente,
                a.clNomeContato,
                a.clTelefone,
                a.clEmail,
                a.clCEP,
                a.clEndereco,
                a.clNrEnd,
                a.clComplEnd,
                a.clBairro,
                a.clCidade,
                a.clUF,
                a.clAtivo
            FROM capCliente a
            ${complement}
            WHERE a.clNomeCliente like ?
            order by a.clNomeCliente
        `;

        const response = await dbQuery(query, params);
        return baseResponse.ok('', response);
    } catch (error) {
        console.log('ERROR _list', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _find(event) {
    console.log('event ', event);
    if (checkData(event, ['clCliente']))
        return baseResponse.badRequest('Dados insuficentes.');

    const query = `SELECT
                    a.clCliente,
                    a.clNomeCliente,
                    a.clNomeContato,
                    a.clTelefone,
                    a.clEmail,
                    a.clCEP,
                    a.clEndereco,
                    a.clNrEnd,
                    a.clComplEnd,
                    a.clBairro,
                    a.clCidade,
                    a.clUF,
                    a.clAtivo
                  FROM capCliente a
                  WHERE a.clCliente = ?
                `;

    const params = [event['clCliente'] || ''];
    const response = await execQuery(query, params);
    if (response && response.results && response.results.length > 0)
        return baseResponse.created(
            'Clientes listados com sucesso',
            response.results && response.results[0]
        );

    return baseResponse.notFound('Cliente não encontrado');
}

async function _insert(event) {
    console.log('event ', event);
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'clNomeCliente',
                    'clNomeContato',
                    'clTelefone',
                    'clEmail',
                    'clCEP',
                    'clEndereco',
                    'clNrEnd',
                    'clBairro',
                    'clCidade',
                    'clUF',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        if (!validaCPFCNPJ(event.clCliente)) {
            return baseResponse.error('CPF / CNPJ do Cliente invalido');
        }
        const entity = {
            clCliente: onlyNumber(event.clCliente),
            clNomeCliente: event.clNomeCliente,
            clNomeContato: event.clNomeContato,
            clTelefone: onlyNumber(event.clTelefone),
            clEmail: event.clEmail,
            clCEP: onlyNumber(event.clCEP),
            clEndereco: event.clEndereco,
            clNrEnd: event.clNrEnd,
            clComplEnd: event.clComplEnd || '',
            clBairro: event.clBairro,
            clCidade: event.clCidade,
            clUF: event.clUF,
            clAtivo: 1,
        };

        const response = await insert('capCliente', entity, event);

        if (response.success) {
            await lambdaInvoke('capKuaraIntegracao', {
                method: '_cadastrarPessoaJuridica',
                cnpj: entity.clCliente,
                legal_name: entity.clNomeCliente,
                trade_name: entity.clNomeCliente,
                email: entity.clEmail,
                telefone: entity.clTelefone,
                razao_social: entity.clNomeCliente,
                nome_fantasia: entity.clNomeCliente,
                cep: entity.clCEP,
                endereco: entity.clEndereco,
                numero: entity.clNrEnd,
                cidade: entity.clCidade,
                uf: entity.clUF,
            });

            return baseResponse.created('Cliente criado com successo');
        }

        if (response.error?.code === 'ER_DUP_ENTRY') {
            return baseResponse.badRequest('CNPJ/CPJ já cadastrado.');
        }

        return baseResponse.badRequest('Erro inesperado');
    } catch (error) {
        console.log('ERROR _insert', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _update(event) {
    console.log('event ', event);
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'clNomeCliente',
                    'clNomeContato',
                    'clTelefone',
                    'clEmail',
                    'clCEP',
                    'clEndereco',
                    'clNrEnd',
                    'clBairro',
                    'clCidade',
                    'clUF',
                    'clAtivo',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.', checkData);
        }

        if (!validaCPFCNPJ(event.clCliente)) {
            return baseResponse.error('CPF / CNPJ do Cliente invalido');
        }

        if (!isEmailValid(event.clEmail)) {
            //return createResponse(400, 'Formato de email inválido.', '', null);
        }

        const entity = {
            clNomeCliente: event.clNomeCliente,
            clNomeContato: event.clNomeContato,
            clTelefone: onlyNumber(event.clTelefone),
            clEmail: event.clEmail,
            clCEP: onlyNumber(event.clCEP),
            clEndereco: event.clEndereco,
            clNrEnd: event.clNrEnd,
            clComplEnd: event.clComplEnd || '',
            clBairro: event.clBairro,
            clCidade: event.clCidade,
            clUF: event.clUF,
            clAtivo: event.clAtivo,
        };

        const response = await update(
            'capCliente',
            entity,
            { clCliente: onlyNumber(event.clCliente) },
            event
        );

        if (response.success) {
            await lambdaInvoke('capKuaraIntegracao', {
                method: '_cadastrarPessoaJuridica',
                cnpj: onlyNumber(event.clCliente),
                legal_name: entity.clNomeCliente,
                trade_name: entity.clNomeCliente,
                email: entity.clEmail,
                telefone: entity.clTelefone,
                razao_social: entity.clNomeCliente,
                nome_fantasia: entity.clNomeCliente,
                cep: entity.clCEP,
                endereco: entity.clEndereco,
                numero: entity.clNrEnd,
                cidade: entity.clCidade,
                uf: entity.clUF,
                bairro: entity.clBairro,
            });

            return baseResponse.ok('Cliente atualizado', response.results);
        }
        console.info(response.success);

        return baseResponse.error('Erro ao atualizar clientes');
    } catch (error) {
        console.log('ERROR _update', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _listinst(event) {
    console.log('event ', event);
    if (checkData(event, ['clCliente'])) {
        return baseResponse.error('Dados insuficentes');
    }

    try {
        const query = `SELECT c.clCliente, c.clNomeCliente, i.isInstSaude, i.isNome
                    FROM capCliente c, capInstSaude i
                    WHERE c.clCliente = ?
                    and   exists (select 1 from capInstSaudeContrato a
                    			  where a.clCliente = c.clCliente
                                  and   a.isInstSaude = i.isInstSaude )
                  `;
        const params = [event.clCliente];
        const response = await execQuery(query, params);

        if (response.success) {
            return baseResponse.created(
                'Listado com sucesso',
                response.results
            );
        }

        return baseResponse.notFound('Nenhuma instituição de saúde encontrada');
    } catch (error) {
        console.log('ERROR _listinst', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _setgestor(event) {
    console.log('event ', event);
    if (checkData(event, ['clCliente', 'usCPFUsuario'])) {
        return baseResponse.error('Dados insuficentes');
    }

    try {
        const existe = await dbQuery(
            'SELECT 1 FROM capUsuario WHERE usCPFUsuario = ?',
            [event.usCPFUsuario]
        );

        if (existe.length < 1) {
            return baseResponse.notFound('Usuário não encontrado!');
        }

        const entity = {
            sgCliente: 'cap',
            usuarioCPF: event.usCPFUsuario,
            sgInstSaude: 'capitale',
            gmNome: 'gestorcliente',
        };

        const response = await insert('brcUsuarioGrupoMenu', entity, event);
        if (response.success) {
            const entity = {
                ucGestor: 1,
            };
            const where = {
                usCPFUsuario: event.usCPFUsuario,
                clCliente: event.clCliente,
            };

            const response = await update(
                'capClienteUsuario',
                entity,
                where,
                event
            );

            if (response.success) {
                return baseResponse.created(
                    'Gestor atualizado',
                    response.results
                );
            } else {
                return baseResponse.badRequest(response);
            }
        }

        return baseResponse.badRequest(response);
    } catch (error) {
        console.log('ERROR _setgestor', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _listclientescsuario(event) {
    /*
        Sequiser colocar um usuario de uma determinada instituição, adiciona na tabela `brcUsuarioGrupoMenu` 
    */

    if (checkData(event, { obrigatorios: ['usCPFUsuario', 'usPerfil'] })) {
        return baseResponse.badRequest('Dados insuficentes.', checkData);
    }

    let query = ``;

    if (event.usPerfil === 'gestorcapitale') {
        query = `SELECT * FROM capCliente`;
    } else {
        query = `SELECT * FROM capCliente as cc
                   right join capClienteUsuario as ccu on ccu.clCliente=cc.clCliente
                   where ccu.usCPFUsuario=?`;
    }

    const response = await execQuery(query, [event.usCPFUsuario]);

    if (response.success) {
        return baseResponse.created('Listado com sucesso', response.results);
    }

    return baseResponse.notFound('Nenhuma instituição de saúde encontrada');
}

function validaCPFCNPJ(num) {
    if (num.length == 14) {
        return validaCNPJ(num);
    }

    if (num.length == 11) {
        return validaCPF(num);
    }
    return false;
}

function validaCPF(cpf) {
    cpf = cpf.replace(/[^\d]+/g, '');
    if (cpf.length !== 11 || /^(\d)\1{10}$/.test(cpf)) return false;

    let soma = 0;
    let resto;

    for (let i = 1; i <= 9; i++)
        soma += parseInt(cpf.substring(i - 1, i)) * (11 - i);
    resto = (soma * 10) % 11;
    if (resto === 10 || resto === 11) resto = 0;
    if (resto !== parseInt(cpf.substring(9, 10))) return false;

    soma = 0;
    for (let i = 1; i <= 10; i++)
        soma += parseInt(cpf.substring(i - 1, i)) * (12 - i);
    resto = (soma * 10) % 11;
    if (resto === 10 || resto === 11) resto = 0;
    if (resto !== parseInt(cpf.substring(10, 11))) return false;

    return true;
}

function validaCNPJ(cnpj) {
    cnpj = cnpj.replace(/[^\d]+/g, '');
    if (cnpj.length !== 14) return false;

    let tamanho = cnpj.length - 2;
    let numeros = cnpj.substring(0, tamanho);
    let digitos = cnpj.substring(tamanho);
    let soma = 0;
    let pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
        soma += numeros.charAt(tamanho - i) * pos--;
        if (pos < 2) pos = 9;
    }

    let resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
    if (resultado != digitos.charAt(0)) return false;

    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
        soma += numeros.charAt(tamanho - i) * pos--;
        if (pos < 2) pos = 9;
    }

    resultado = soma % 11 < 2 ? 0 : 11 - (soma % 11);
    if (resultado != digitos.charAt(1)) return false;

    return true;
}

export {
    _insert,
    _update,
    _list,
    _find,
    _listinst,
    _setgestor,
    _listclientescsuario,
};
