{"id": null, "tenantId": "473680", "name": "GS2 -  <PERSON><PERSON>", "created": null, "flowNodes": [{"flowReplies": [{"flowReplyType": "Text", "data": "<p>Ok! Para reiniciar esta conversa, é só escrever aqui: <strong>GS2</strong></p>", "caption": "", "mimeType": ""}], "id": "main_message-BnzZs", "flowNodeType": "Message", "flowNodePosition": {"posX": "332", "posY": "231"}, "isStartNode": true}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p><PERSON><PERSON><PERSON> e até à próxima!</p>", "caption": "", "mimeType": ""}], "id": "main_message-RPLYK", "flowNodeType": "Message", "flowNodePosition": {"posX": "877", "posY": "241"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_message-BnzZs-main_message-RPLYK", "sourceNodeId": "main_message-BnzZs", "targetNodeId": "main_message-RPLYK"}], "lastUpdated": "2025-07-28T02:25:45.044Z", "isDeleted": false, "transform": {"posX": "0", "posY": "0", "zoom": "1.01"}, "isPro": false, "channelTypes": ["WA", "IG", "MSG"]}