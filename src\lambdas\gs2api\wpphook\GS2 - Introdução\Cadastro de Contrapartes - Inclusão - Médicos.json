{"id": null, "tenantId": "473680", "name": "Cadastro de Contrapartes - Inclusão - Médicos", "created": null, "flowNodes": [{"methodType": "Post", "url": "https://hook.us2.make.com/od4cnvjqcq4n4g923qeq0fqke8vll4u2", "headers": [], "body": "{  \n  \"Token\": \"@Token\",   \n  \"Nome Completo\": \"@NomeCompleto\",\n  \"Email\": \"@Email\",\n  \"CPF\": \"@CPF\",\n  \"Clínica / Hospital\": \"@ClinicaHospital\"\n}", "testVariables": [], "responseVariables": [], "expectedStatuses": [], "id": "main_webhook-IqyIb", "flowNodeType": "Webhook", "flowNodePosition": {"posX": "638", "posY": "210"}, "isStartNode": false}, {"attributeVariables": [{"type": "ContactInputVariable", "name": "Token", "value": "KjUEIpkRpBXVqLjHoHU"}], "id": "main_updateAttribute-FPqyy", "flowNodeType": "UpdateAttribute", "flowNodePosition": {"posX": "284", "posY": "238"}, "isStartNode": true}], "flowEdges": [{"id": "reactflow__edge-main_updateAttribute-FPqyy-main_webhook-IqyIb", "sourceNodeId": "main_updateAttribute-FPqyy", "targetNodeId": "main_webhook-IqyIb"}], "lastUpdated": "2025-07-29T14:44:06.945Z", "isDeleted": false, "transform": {"posX": "161.6350741483716", "posY": "77.91013083652422", "zoom": "0.6961112031282032"}, "isPro": true, "channelTypes": ["WA"]}