import {
  activateEnvelope,
  addDocToEnvelope,
  addSignatarioAuthRequirement,
  addSignatarioRequirement,
  addSignatariosToDocument,
  criaEnvelope, sendNotification
} from "./functions.mjs";


console.log('process', process.env.CLICKSIGN_API_KEY_PRODUCTION)

const envelopeHandler = async () => {
  const newEnvelope = await criaEnvelope();
  const newEnvelopeId = newEnvelope?.data?.id;
  console.log('envelope', newEnvelope.data)
  if (!newEnvelopeId || newEnvelopeId === "" || newEnvelopeId === null) {
    return null
  }
  
  return newEnvelopeId
}

const docHandler = async (idEnvelope, aditivo) => {
  const addedDocResponse = await addDocToEnvelope(idEnvelope, aditivo);
  const addedDocId = addedDocResponse?.data?.id;
  
  if (!addedDocId || addedDocId === "" || addedDocId === null) {
    return null
  }
  
  
  // update capAntecipacao set idEnvelope
  
  return addedDocId
}

const addSignatariosHandler = async (envelopeId, signerAttributes) => {
  const addedSignatario = await addSignatariosToDocument(envelopeId, signerAttributes);
  const signatarioId = addedSignatario?.data?.id;
  
  if (!signatarioId || signatarioId === "" || signatarioId === null) {
    return null
  }
  
  return signatarioId
}
const addSignatarioRequirementHandler = async (envelopeId, documentId, signerId, signerRole) => {
  const addedSignatario = await addSignatarioRequirement(envelopeId, documentId, signerId, signerRole);
  const signatarioId = addedSignatario?.data?.id;
  
  if (!signatarioId || signatarioId === "" || signatarioId === null) {
    return null
  }
  
  return signatarioId
}

const addSignatarioAuthRequirementHandler = async (envelopeId, documentId, signerId) => {
  const addedSignatario = await addSignatarioAuthRequirement(envelopeId, documentId, signerId);
  const signatarioId = addedSignatario?.data?.id;
  
  if (!signatarioId || signatarioId === "" || signatarioId === null) {
    return null
  }
  
  return signatarioId
}

const activateEnvelopeHandler = async (envelopeId) => {
  const activatedEnvelope = await activateEnvelope(envelopeId);
  const id = activatedEnvelope?.data?.id;
  
  if (!id || id === "" || id === null) {
    return null
  }
  
  return id
}
const sendNotificationHandler = async (envelopeId, signerId) => {
  const notificationSent = await sendNotification(envelopeId, signerId);
  const notificationId = notificationSent?.data?.id;
  
  if (!notificationId || notificationId === "" || notificationId === null) {
    return null
  }
  
  return notificationId
}

export const handler = async (events) => {
  console.log('events ', events);
  const retrodafila = [];

//  const resp = await rodafila(events);
  
  for (const record of events?.Records) {
    retrodafila.push(await rodafila(JSON.parse(record.body)));
  }
  
//  console.log('retrodafila', retrodafila);
  return({ success: true, retrodafila });
};

  
async function rodafila (event) {  

console.log('event da fila', event);

const signatario1 = {
  name: event.responsavel,
  birthday: event.oriDtNasc,
  email: event.oriusEmail,
  phone_number: event.oriTelefone,
  has_documentation: true,
  documentation: event.responsavelCNPJCPF,
};
const signatario2 = {
  name: event.sacado,
  birthday: event.DtNasc,
  email: event.usEmail,
  phone_number: event.usTelefone,
  has_documentation: true,
  documentation: event.sacadoCNPJCPF,
};
const signatario3 = {
  name: "Guilherme Beltrami Alesina",
  birthday: "1992-10-20",
  email: "<EMAIL>",
  phone_number: "11982832931",
  has_documentation: true,
  documentation: "186.893.070-01",
};
const signatario4 = {
  name: "Denis Destri",
  birthday: "1965-08-25",
  email: "<EMAIL>",
  phone_number: "11981605098",
  has_documentation: true,
  documentation: "066.197.328-01",
};
  
  try {
    const envelopeId = await envelopeHandler();
    console.log('envelopeId', envelopeId);

    if (!envelopeId || envelopeId === "" || envelopeId === null) {
      return {
        statusCode: 400,
        body: 'Não foi possível criar o envelope',
      };
    }
    
    const addedDocId = await docHandler(envelopeId, event.aditivo_nr);
    console.log('addedDocId', addedDocId);
    
    if (!addedDocId || addedDocId === "" || addedDocId === null) {
      return {
        statusCode: 400,
        body: 'Erro ao adicionar documento ao envelope',
      };
    }
    
    const signatarioId = await addSignatariosHandler(envelopeId, signatario1);
    const signatarioId2 = await addSignatariosHandler(envelopeId, signatario2);
    const signatarioId3 = await addSignatariosHandler(envelopeId, signatario3);
    const signatarioId4 = await addSignatariosHandler(envelopeId, signatario4);
    console.log('signatarioId', signatarioId)
    console.log('signatarioId2', signatarioId2)
    console.log('signatarioId3', signatarioId3)
    console.log('signatarioId4', signatarioId4)
    if (!signatarioId || !signatarioId2 || !signatarioId3 || !signatarioId4) {
      return {
        statusCode: 400,
        body: 'Erro ao adicionar signatários no documento',
      }
    }
    
    const signerRequirement = await addSignatarioRequirementHandler(envelopeId, addedDocId, signatarioId, 'joint_debtor');
    const signerRequirement2 = await addSignatarioRequirementHandler(envelopeId, addedDocId, signatarioId2, 'creditor');
    const signerRequirement3 = await addSignatarioRequirementHandler(envelopeId, addedDocId, signatarioId3, 'witness');
    const signerRequirement4 = await addSignatarioRequirementHandler(envelopeId, addedDocId, signatarioId4, 'witness');

    if (!signerRequirement || !signerRequirement2 || !signerRequirement3 || !signerRequirement4) {
      return {
        statusCode: 400,
        body: 'Erro ao adicionar requisitos aos signatários',
      }
    }
    
    const signerAuthRequirement = await addSignatarioAuthRequirementHandler(envelopeId, addedDocId, signatarioId);
    const signerAuthRequirement2 = await addSignatarioAuthRequirementHandler(envelopeId, addedDocId, signatarioId2);
    const signerAuthRequirement3 = await addSignatarioAuthRequirementHandler(envelopeId, addedDocId, signatarioId3);
    const signerAuthRequirement4 = await addSignatarioAuthRequirementHandler(envelopeId, addedDocId, signatarioId4);
    
    if (!signerAuthRequirement || !signerAuthRequirement2 || !signerAuthRequirement3 || !signerAuthRequirement4) {
      return {
        statusCode: 400,
        body: 'Erro ao adicionar requisitos de autenticação aos signatários',
      };
    }

    const activatedEnvelope = await activateEnvelopeHandler(envelopeId);
    
    if (!activatedEnvelope) {
      return {
        statusCode: 400,
        body: 'Erro ao ativar envelope',
      };
    }
    
    const sentNotification = await sendNotificationHandler(envelopeId, signatarioId);
    const sentNotification2 = await sendNotificationHandler(envelopeId, signatarioId2);
    const sentNotification3 = await sendNotificationHandler(envelopeId, signatarioId3);
    const sentNotification4 = await sendNotificationHandler(envelopeId, signatarioId4);
    
    if (!sentNotification || !sentNotification2 || !sentNotification3 || !sentNotification4) {
      return {
        statusCode: 400,
        body: 'Erro ao enviar notificação',
      };
    }
    
    
    const successResponse = {
      statusCode: 200,
      body: 'Documento enviado com sucesso',
    };
    
    return successResponse;
  } catch (e) {
    console.log("ERROR HANDLER criaEnvelope", e)
    return {
      statusCode: 403,
      body: 'Não foi possível enviar o documento',
    }
  }
}