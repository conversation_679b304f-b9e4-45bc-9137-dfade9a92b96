import { baseResponse, buscaAtendimento } from 'capfunctions';
import { buscaFechamentos, calculaFechamentoV2 } from '../functions.mjs';

export async function _valoresFechamentoProfissional(event) {
    const [
        atendimento,
        fechamentosAguardandoAprovacao,
        fechamentosAprovados,
        fechamentoAguardandoAntecipacao,
        fechamentosAntecipados,
    ] = await Promise.all([
        buscaAtendimento(event),
        buscaFechamentos(event, 'aguardandoAprovacao'),
        buscaFechamentos(event, 'aprovado'),
        buscaFechamentos(event, 'antecipacaoSolicitada'),
        buscaFechamentos(event, 'antecipado'),
    ]);

    const [aguardandoAprovacao, aprovados, aguardandoAntecipacao, antecipado] = await Promise.all([
        calculaFechamentoV2({
            event,
            atendimento,
            fechamentos: fechamentosAguardandoAprovacao,
        }),
        calculaFechamentoV2({
            event,
            atendimento,
            fechamentos: fechamentosAprovados,
        }),
        calculaFechamentoV2({
            event,
            atendimento,
            fechamentos: fechamentoAguardandoAntecipacao,
        }),
        calculaFechamentoV2({
            event,
            atendimento,
            fechamentos: fechamentosAntecipados,
        }),
    ]);

    return baseResponse.ok('Listado com sucesso', {
        aguardandoAprovacao: aguardandoAprovacao.valorSemDesagio,
        aprovado: aprovados.valorSemDesagio,
        aguardandoAntecipacao: aguardandoAntecipacao.valorSemDesagio,
        antecipado: antecipado.valorSemDesagio,
    });
}
