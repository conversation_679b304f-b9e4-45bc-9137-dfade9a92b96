CREATE TABLE `clinics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `razao_social` varchar(255) NOT NULL,
  `cnpj` varchar(18) NOT NULL,
  `active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_cnpj` (`cnpj`),
  KEY `idx_active` (`active`)
);

CREATE TABLE `interaction_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(50) NOT NULL,
  `message_type` enum('INCOMING','OUTGOING') NOT NULL,
  `message_content` text NOT NULL,
  `timestamp` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>EY `idx_session` (`session_id`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_type` (`message_type`)
);

CREATE TABLE `user_sessions` (
  `id` varchar(50) NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `current_state` enum('INITIAL','GREETING','CLINIC_REQUEST','CLINIC_VALIDATION','CLINIC_CONFIRMATION','NAME_REQUEST','CPF_REQUEST','EMAIL_REQUEST','DATA_CONFIRMATION','DATA_CORRECTION','TERMS_PRESENTATION','FAQ_MENU','FAQ_RESPONSE','TERMS_ACCEPTANCE','COMPLETION','END') NOT NULL DEFAULT 'INITIAL',
  `session_data` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_phone` (`phone_number`),
  KEY `idx_state` (`current_state`),
  KEY `idx_updated` (`updated_at`)
);

CREATE TABLE `user_data` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(50) NOT NULL,
  `clinic_id` int NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `cpf` varchar(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `terms_accepted` tinyint(1) DEFAULT '0',
  `terms_accepted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `clinic_id` (`clinic_id`),
  KEY `idx_cpf` (`cpf`),
  KEY `idx_email` (`email`),
  KEY `idx_session` (`session_id`),
  CONSTRAINT `user_data_ibfk_1` FOREIGN KEY (`session_id`) REFERENCES `user_sessions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_data_ibfk_2` FOREIGN KEY (`clinic_id`) REFERENCES `clinics` (`id`)
);