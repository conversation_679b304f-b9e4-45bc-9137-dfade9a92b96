import { baseResponse, dbQuery } from 'capfunctions';

export async function _listFechamentos(event) {
  try {
    const psCPF = event?.body?.user;

    let params = [psCPF];

    let filter = /*sql*/ `
      AND caf.adSituacao = 'aprovado'
    `;

    if (event.opNrPlantao) {
      filter += /*sql*/ `
                AND caf.opNrPlantao = ?
            `;

      params.push(event.opNrPlantao);
    }

    if (event.search) {
      filter += /*sql*/ `
                AND (
                    caf.afNome LIKE ?
                    OR caf.ocNrContrato LIKE ?
                    OR caf.opNrPlantao LIKE ?
                    OR caf.codFechamento LIKE ?
                )
            `;

      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
    }

    // Pagination parameters
    const page = Number(event.page) || 1;
    const pageSize = Number(event.pageSize) || 25;
    const offset = (page - 1) * pageSize;

    const multiQuery = /*sql*/ `
            SELECT COUNT(DISTINCT caf.codFechamento) as total
            FROM capAtendimentoFechamento caf
            INNER JOIN capOperPlantaoCheck copc ON (
                copc.codFechamento = caf.codFechamento
                AND caf.isInstSaude = copc.isInstSaude
                AND caf.laNome = copc.laNome
                AND caf.esEspecialidade = copc.esEspecialidade
                AND caf.ocNrContrato = copc.ocNrContrato
                AND caf.opNrPlantao = copc.opNrPlantao
            )
            INNER JOIN capInstSaudePlantao ips ON (
                ips.isInstSaude = copc.isInstSaude
                AND ips.laNome = copc.laNome
                AND ips.esEspecialidade = copc.esEspecialidade
                AND ips.ocNrContrato = copc.ocNrContrato
                AND ips.opNrPlantao = copc.opNrPlantao
            )
            WHERE ips.psCPF = ?
            ${filter};

            SELECT
                CONCAT(
                    FLOOR(SUM(TIME_TO_SEC(ocQtAprovadas)) / 3600), 
                    ':', 
                    LPAD(MOD(FLOOR(SUM(TIME_TO_SEC(ocQtAprovadas)) / 60), 60), 2, '0')
                ) AS totalHoras,
                caf.*
            FROM capAtendimentoFechamento caf
            INNER JOIN capOperPlantaoCheck copc ON (
                copc.codFechamento = caf.codFechamento
                AND caf.isInstSaude = copc.isInstSaude
                AND caf.laNome = copc.laNome
                AND caf.esEspecialidade = copc.esEspecialidade
                AND caf.ocNrContrato = copc.ocNrContrato
                AND caf.opNrPlantao = copc.opNrPlantao
            )
            INNER JOIN capInstSaudePlantao ips ON (
                ips.isInstSaude = copc.isInstSaude
                AND ips.laNome = copc.laNome
                AND ips.esEspecialidade = copc.esEspecialidade
                AND ips.ocNrContrato = copc.ocNrContrato
                AND ips.opNrPlantao = copc.opNrPlantao
            )
            WHERE ips.psCPF = ?
            ${filter}
            GROUP BY caf.codFechamento
            ORDER BY caf.codFechamento ASC
            LIMIT ?
            OFFSET ?;
        `;

    const multiParams = [...params, ...params, pageSize, offset];

    const result = await dbQuery(multiQuery, multiParams);
    const total = result[0][0].total;
    const totalPages = Math.ceil(total / pageSize);
    const nextPage = page < totalPages ? page + 1 : null;
    const response = result[1];

    return baseResponse.paginated('', {
      totalPages,
      currentPage: page,
      nextPage,
      data: response,
    });
  } catch (error) {
    console.log('ERROR _listFechamentos', error);

    return baseResponse.error('Erro ao processar requisição');
  }
}
