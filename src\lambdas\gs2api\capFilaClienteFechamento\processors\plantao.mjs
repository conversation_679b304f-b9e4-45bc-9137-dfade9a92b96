import { AtendimentoProcessor } from './atendimento.mjs';
import { FechamentoProcessor } from './fechamento.mjs';

export class PlantaoProcessor {
  /**
   * @param {AtendimentoProcessor} atendimentoProcessor
   * @param {FechamentoProcessor} fechamentoProcessor
   */
  constructor(atendimentoProcessor, fechamentoProcessor) {
    this.atendimentoProcessor = atendimentoProcessor;
    this.fechamentoProcessor = fechamentoProcessor;
  }

  async process(atendimentos) {
    for (const atendimento of atendimentos) {
      await this.atendimentoProcessor.process(atendimento);
    }

    await this.fechamentoProcessor.process();
  }
}
