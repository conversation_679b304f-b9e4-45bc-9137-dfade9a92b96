import { execQuery }from 'capfunctions'

import { SQSClient, SendMessageCommand } from "@aws-sdk/client-sqs";
import { v4 as uuidv4 } from 'uuid';

const sqsClient = new SQSClient({ region: "us-east-2" });


async function processaFechamentos() {
    const fechamentosDiarios = await buscaDiario();
    const fechamentosSemanais = await buscaSemanal()
    const fechamentosMensais = await buscaMensal();

    await enviaParaProcessamento([...fechamentosDiarios, ...fechamentosSemanais, ...fechamentosMensais])
}

async function enviaParaProcessamento(atendimentos) {
    const queueUrl = 'https://sqs.us-east-2.amazonaws.com/************/fechamentoParcial.fifo';

    const MessageGroupId = uuidv4();
    const paramsfila = {
        QueueUrl: queueUrl,
        MessageBody: JSON.stringify(atendimentos),
        MessageGroupId,
        MessageDeduplicationId: MessageGroupId
    };

    console.log('params', paramsfila);

    try {
        const command = new SendMessageCommand(paramsfila);
        const data = await sqsClient.send(command);
        console.log(`${atendimentos?.length} atendimento foram enviadas para processamento.`, data.MessageId);
    } catch (err) {
        console.log('Erro', err);
        throw err;
    }
}

async function buscaDiario() {
    const query = `SELECT isInstSaude, laNome, esEspecialidade, ocNrContrato, opNrPlantao, clCliente
                    FROM capInstSaudePlantao 
                    WHERE 
                    opTipoFechamento = "Diario" AND 
                    opPeriodoFim >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND 
                    opPeriodoIni <= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND
                    opSituacao = 'EmExecucao'`

    const response = await execQuery(query);
    return response.results?.length > 0 ? response.results : []
}

async function buscaSemanal() {
    const query = `
                        SELECT isInstSaude, laNome, esEspecialidade, ocNrContrato, opNrPlantao, clCliente
                        FROM capInstSaudePlantao
                        WHERE
                        opDiaFechamento = DAYOFWEEK(DATE_SUB(CURDATE(), INTERVAL 1 DAY)) AND
                        opTipoFechamento = "Semanal"
                        AND
                        opPeriodoFim >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND
                        opPeriodoIni <= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND
                        opSituacao = 'EmExecucao'
                    `

    const response = await execQuery(query);
    return response.results?.length > 0 ? response.results : []
}

async function buscaMensal() {
    const query = `SELECT isInstSaude, laNome, esEspecialidade, ocNrContrato, opNrPlantao, clCliente
                    FROM capInstSaudePlantao 
                    WHERE 
                    opDiaFechamento = DAY(DATE_SUB(CURDATE(), INTERVAL 1 DAY)) AND
                    opTipoFechamento = "Mensal" AND 
                    opPeriodoFim >= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND
                    opPeriodoIni <= DATE_SUB(CURDATE(), INTERVAL 1 DAY) AND
                    opSituacao = 'EmExecucao'
                    `

    const response = await execQuery(query);
    return response.results?.length > 0 ? response.results : []
}

export {
    processaFechamentos
}
