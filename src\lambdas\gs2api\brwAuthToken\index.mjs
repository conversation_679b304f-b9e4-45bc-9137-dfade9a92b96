import jwt from 'jsonwebtoken';

const region = 'us-east-2';

const SECRETKEY = process.env.SECRET_KEY;
const expiresIn = 60 * Number(process.env.EXPIRES_IN) || 60 * 60 * 5;

class BrwAuthToken {
  constructor(event, SECRETKEY, expiresIn) {
    this.body = event;
    this.SECRETKEY = SECRETKEY;
    this.expiresIn = expiresIn;
  }

  _generate = async () => {
    if (!('user' in this.body)) {
      return {
        success: false,
        message: 'Necessário passar o usuário no payload para gerar o token',
      };
    }
    return {
      success: true,
      body: jwt.sign(
        {
          user: this.body.user
        },
        this.SECRETKEY,
        { expiresIn: this.expiresIn }
      ),
    };
  };

  _verify = async () => {
    if (!('token' in this.body)) {
      return {
        success: false,
        message: 'Necessário passar o token no payload',
      };
    }
    try {
      const decoded = jwt.verify(
        String(this.body.token).replace('Bearer ', ''),
        this.SECRETKEY
      );
      if (!('exp' in decoded) || !('iat' in decoded)) {
        return {
          success: false,
          message: 'Token inválido',
        };
      }

      const newToken = jwt.sign(
        {
          user: decoded.user
        },
        this.SECRETKEY,
        { expiresIn: this.expiresIn }
      );

      return {
        success: true,
        body: { ...decoded, newToken },
        newToken,
      };
    } catch (error) {
      console.log('Catch na função isAuthorized()', error);
      return {
        success: false,
        message: 'Token inválido',
      };
    }
  };

  async run() {
    if (this.body.method in this) {
      return await this[this.body.method]();
    }
    return {
      statusCode: 404,
      message: 'Nada encontrado',
    };
  }
}

export const handler = async (event) => {
  return await new BrwAuthToken(event, SECRETKEY, expiresIn).run();
};
