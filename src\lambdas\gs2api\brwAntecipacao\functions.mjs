import { execQuery, baseResponse } from 'capfunctions';

async function _list(event) {
  console.log(event);

  try {
    var response;

    let params = [];

    let filter = '';

    if (event.dataInicio) {
      filter += /*sql*/ ` AND DATE(a.anDataSolicitacao) >= ? `;
      params.push(event.dataInicio);
    }

    if (event.dataFim) {
      filter += /*sql*/ ` AND DATE(a.anDataSolicitacao) <= DATE(?) `;
      params.push(event.dataFim);
    }

    if (event.profissional) {
      filter += /*sql*/ ` AND a.psCPF = ? `;
      params.push(event.profissional);
    }

    if (event.situacoes && event.situacoes.length > 0) {
      const situacoesFilter = [];
      if (event.situacoes.includes('Solicitado')) {
        situacoesFilter.push(/*sql*/ `a.anValorAprovado IS NULL`);
      }
      if (event.situacoes.includes('Aprovado')) {
        situacoesFilter.push(/*sql*/ `a.anValorAprovado IS NOT NULL AND a.anValorAprovado > 0`);
      }
      if (situacoesFilter.length > 0) {
        filter += /*sql*/ ` AND (${situacoesFilter.join(' OR ')}) `;
      }
    }

    if (event.search) {
      filter += /*sql*/ `
              AND (
                a.anNrAntecipacao LIKE ?
                OR u.usNome LIKE ?
                OR p.opNrPlantao LIKE ?
                OR p.opPeriodoIni LIKE ?
                OR p.opPeriodoFim LIKE ?
                OR p.opQtdHorasRealizadas LIKE ?
                OR p.opQtdHorasRealizadas LIKE ?
                OR p.opValorHora LIKE ?
                OR a.anValorSolicitado LIKE ?
              )
          `;

      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
      params.push(`%${event.search}%`);
    }

    if (event?.psCPF || event?.clCliente) {
      let initialWhere = '';
      if (event?.psCPF) {
        initialWhere = 'a.psCPF = ?';
      } else if (event?.clCliente) {
        initialWhere = 'apsa.clCliente = ?';
      }

      const queryAntecipacoes = /*sql*/ ` 
              SELECT
                a.*,
                IF(a.anValorAprovado IS NOT NULL AND a.anValorAprovado > 0, 'Aprovado', 'Solicitado') as status,
                u.usNome,
                p.opNrPlantao,
                p.opPeriodoIni,
                p.opPeriodoFim,
                (
                  SELECT  SEC_TO_TIME(SUM(TIME_TO_SEC(ocQtAprovadas))) as horas 
                      FROM capOperPlantaoCheck c
                    WHERE c.isInstSaude = p.isInstSaude
                      AND c.laNome = p.laNome
                      AND c.esEspecialidade = p.esEspecialidade
                      AND c.ocNrContrato = p.ocNrContrato
                      AND c.clCliente = p.clCliente
                      AND c.opNrPlantao = p.opNrPlantao
                ) opQtdHorasRealizadas,
                p.opValorHora
              FROM
                capAntecipacao a
              LEFT JOIN
                capAditivoProfSaudeAntecipacao apsa ON a.anNrAntecipacao = apsa.anNrAntecipacao
              LEFT JOIN
                capInstSaudePlantao p ON apsa.opNrPlantao = p.opNrPlantao 
              INNER JOIN capUsuario u ON a.psCPF = u.usCPFUsuario
              WHERE ${initialWhere}
              ${filter}
              ORDER BY a.anNrAntecipacao desc
          `;

      response = await execQuery(queryAntecipacoes, [event?.psCPF || event?.clCliente, ...params]);
    } else {
      const queryAntecipacoes = /*sql*/ ` 
              SELECT
                a.*,
                IF(a.anValorAprovado IS NOT NULL AND a.anValorAprovado > 0, 'Aprovado', 'Solicitado') as status,
                u.usNome,
                p.opNrPlantao,
                p.opPeriodoIni,
                p.opPeriodoFim,
                (
                  SELECT  SEC_TO_TIME(SUM(TIME_TO_SEC(ocQtAprovadas))) as horas 
                      FROM capOperPlantaoCheck c
                    WHERE c.isInstSaude = p.isInstSaude
                      AND c.laNome = p.laNome
                      AND c.esEspecialidade = p.esEspecialidade
                      AND c.ocNrContrato = p.ocNrContrato
                      AND c.clCliente = p.clCliente
                      AND c.opNrPlantao = p.opNrPlantao
                ) opQtdHorasRealizadas,
                p.opValorHora
              FROM
                capAntecipacao a
              LEFT JOIN
                capAditivoProfSaudeAntecipacao apsa ON a.anNrAntecipacao = apsa.anNrAntecipacao
              LEFT JOIN
                capInstSaudePlantao p ON apsa.opNrPlantao = p.opNrPlantao 
              INNER JOIN capUsuario u ON a.psCPF = u.usCPFUsuario
              WHERE 1 = 1
              ${filter}
              ORDER BY a.anNrAntecipacao desc
          `;

      response = await execQuery(queryAntecipacoes, params);
    }

    if (response.success) {
      const antecipacoes = {};

      response.results.forEach((row) => {
        const {
          anNrAntecipacao,
          anDataSolicitacao,
          psCPF,
          usNome,
          ifInstFinanceira,
          icNomeAprovador,
          anDataAprovacao,
          anValorSolicitado,
          anValorAprovado,
          usCPFUsuarioAprovador,
          anTaxaAntecipacao,
          dtInclusao,
          dtModificacao,
          opNrPlantao,
          opPeriodoIni,
          opPeriodoFim,
          opQtdHorasRealizadas,
          opValorHora,
          status,
        } = row;

        if (!antecipacoes[anNrAntecipacao]) {
          antecipacoes[anNrAntecipacao] = {
            anNrAntecipacao,
            anDataSolicitacao,
            psCPF,
            usNome,
            ifInstFinanceira,
            icNomeAprovador,
            anDataAprovacao,
            anValorSolicitado,
            anValorAprovado,
            usCPFUsuarioAprovador,
            anTaxaAntecipacao,
            dtInclusao,
            dtModificacao,
            status,
            plantoes: [],
          };
        }

        if (opNrPlantao) {
          antecipacoes[anNrAntecipacao].plantoes.push({
            opNrPlantao,
            opPeriodoIni,
            opPeriodoFim,
            opQtdHorasRealizadas,
            opValorHora,
            vlrTotal: 0,
          });
        }
      });

      const formattedData = Object.values(antecipacoes);
      return baseResponse.ok('Listado com sucesso', formattedData);
    } else {
      baseResponse.notFound(response);
    }
  } catch (error) {
    console.log('ERROR _list', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

export { _list };
