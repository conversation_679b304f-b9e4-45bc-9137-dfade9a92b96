import { checkData, execQuery, baseResponse, dbQuery } from 'capfunctions';

export async function _listChecks(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'ceTipoPagamento',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        if (!event.psCPF) {
            return baseResponse.ok('', []);
        }

        const [solicitacao] = await dbQuery(
            /*sql*/ `
                SELECT s.psDataSolicitacao, a.dtModificacao
                FROM capInstSaudePlantaoSolicitacao s, capInstSaudePlSolicitAprovado a
                WHERE s.isInstSaude = ?
                and s.laNome = ?
                and s.esEspecialidade = ?
                and s.ocNrContrato = ?
                and s.clCliente = ?
                and s.opNrPlantao = ?
                and s.psCPF = ?
                and a.isInstSaude = s.isInstSaude
                and a.laNome = s.laNome
                and a.esEspecialidade = s.esEspecialidade
                and a.ocNrContrato = s.ocNrContrato
                and a.clCliente = s.clCliente
                and a.opNrPlantao = s.opNrPlantao
                and a.psCPF = s.psCPF
                and a.psSequenciaSolicitacao = s.psSequenciaSolicitacao
            `,
            [
                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.opNrPlantao,
                event.psCPF,
            ]
        );

        if (!solicitacao) {
            return baseResponse.notFound('Solicitação não encontrada.');
        }

        const psDataSolicitacao = solicitacao.psDataSolicitacao;
        const psDataAprovacao = solicitacao.dtModificacao;

        let query = /*sql*/ `
            SELECT 
                uuid() as id,
                c.*,
                DATE_FORMAT(c.ocCheckIn, '%Y-%m-%d %H:%i') as ocCheckIn,
                DATE_FORMAT(c.ocCheckOut, '%Y-%m-%d %H:%i') as ocCheckOut,
                c.ocQtAprovadas as hrsTrabalhadas,
                CASE 
                    WHEN pa.agTipoValor = 0 THEN 
                        CASE c.ceTipoPagamento
                            WHEN 'Hora' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorHora
                            WHEN 'Fixo' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorFixo
                            WHEN 'Unitario' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorUnit
                            ELSE 0
                        END
                    WHEN pa.agTipoValor = 1 THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorExtra1
                    WHEN pa.agTipoValor = 2 THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorExtra2
                    ELSE 0
                END AS valor
            FROM 
                capOperPlantaoCheck c
            INNER JOIN 
                capInstSaudePlantaoAgenda pa 
                ON c.agData = pa.agData 
                AND c.isInstSaude = pa.isInstSaude 
                AND c.laNome = pa.laNome 
                AND c.esEspecialidade = pa.esEspecialidade 
                AND c.ocNrContrato = pa.ocNrContrato 
                AND c.clCliente = pa.clCliente 
                AND c.ceTipoPagamento = pa.ceTipoPagamento 
                AND c.opNrPlantao = pa.opNrPlantao
            INNER JOIN 
                capInstSaudePlantao sp 
                ON pa.isInstSaude = sp.isInstSaude 
                AND pa.laNome = sp.laNome 
                AND pa.esEspecialidade = sp.esEspecialidade 
                AND pa.ocNrContrato = sp.ocNrContrato 
                AND pa.clCliente = sp.clCliente 
                AND pa.ceTipoPagamento = sp.ceTipoPagamento 
                AND pa.opNrPlantao = sp.opNrPlantao

                WHERE sp.psCPF = ?
                AND c.isInstSaude = ?
                AND c.laNome = ?
                AND c.esEspecialidade = ?
                AND c.ocNrContrato = ?
                AND c.clCliente = ?
                AND c.opNrPlantao = ?
        `;

        const params = [
            event.psCPF,
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
        ];

        if (event.statusCheck === 'EmExecucao') {
            query = query.concat(
                /*sql*/ `AND c.codFechamento is NULL AND c.ocUsuarioAprovacao is NULL`
            );
        }

        if (event.statusCheck === 'EnviadoAprovacao') {
            query = query.concat(
                /*sql*/ `AND c.codFechamento is NOT NULL AND c.ocSituacao = 'EnviadoAprovacao'`
            );
        }

        if (event.statusCheck === 'Fechado') {
            query = query.concat(
                /*sql*/ `AND c.codFechamento is NOT NULL AND c.ocSituacao = 'Fechado'`
            );
        }

        if (event.fechamentos?.length > 0) {
            query = query.concat(
                /*sql*/ ` and c.codFechamento in (${event.fechamentos.join(',')})`
            );
        }

        query = query.concat(/*sql*/ ` ORDER BY c.ocCheckIn ASC`);

        const response = await execQuery(query, params);

        const [plantao] = await dbQuery(
            /*sql*/ `
                SELECT a.opPeriodoIni, a.opPeriodoFim, b.afValor
                FROM capInstSaudePlantao a
                LEFT JOIN capAtendimentoFechamento b ON b.opNrPlantao = a.opNrPlantao
                WHERE a.opNrPlantao = ?
            `,
            [event.opNrPlantao]
        );

        if (response.success) {
            let queryHoras = /*sql*/ `
                SELECT
                    TIME(SUM(timediff(cop.ocCheckout, cop.ocCheckIn))) as horasTotais
                FROM
                    capOperPlantaoCheck cop,
                    capInstSaudePlantao p
                WHERE p.psCPF = ?
                AND p.laNome = cop.laNome
                AND p.esEspecialidade = cop.esEspecialidade
                AND p.ocNrContrato = cop.ocNrContrato
                AND p.clCliente = cop.clCliente
                AND p.opNrPlantao = cop.opNrPlantao
                AND p.ceTipoPagamento = cop.ceTipoPagamento
        
                AND p.isInstSaude = ?
                AND cop.laNome = ?
                AND cop.esEspecialidade = ?
                AND cop.ocNrContrato = ?
                AND cop.clCliente = ?
                AND cop.opNrPlantao = ?
                AND cop.ceTipoPagamento = ?
            `;

            const paramsHoras = [
                event.psCPF,
                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.opNrPlantao,
                event.ceTipoPagamento,
            ];

            if (event.rangeIni && event.rangeFim) {
                queryHoras = queryHoras.concat(' and ocCheckIn > ? and ocCheckOut < ?');
                paramsHoras.push(event.rangeIni, event.rangeFim);
            }

            const [horasTotais] = await dbQuery(queryHoras, paramsHoras);

            const responseJson = {
                horasTrabalhadas: horasTotais.horasTotais,
                opPeriodoIni: plantao.opPeriodoIni,
                opPeriodoFim: plantao.opPeriodoFim,
                afValor: plantao.afValor
                    ? plantao.afValor.toLocaleString('pt-BR', {
                          style: 'currency',
                          currency: 'BRL',
                      })
                    : 0,
                psDataSolicitacao,
                psDataAprovacao,
                checks: response.results,
            };

            return baseResponse.ok('Listado com sucesso!', responseJson);
        }

        return baseResponse.badRequest('Não foi possível realizar a listagem!');
    } catch (error) {
        console.log('ERROR _listchecks', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}
