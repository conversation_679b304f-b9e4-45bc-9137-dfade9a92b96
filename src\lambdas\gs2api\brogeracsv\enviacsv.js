const AWS = require('aws-sdk');
const SES = new AWS.SES({ region: process.env.REGION || 'us-east-1' });

var mimemessage = require('mimemessage');

var mailContent = mimemessage.factory({ contentType: 'multipart/mixed', body: [] });

exports.handler = async function (event) {
  //console.log('event', event);

  mailContent.header('From', '<EMAIL>');
  mailContent.header('To', '<EMAIL>');
  mailContent.header('Subject', 'Contrato de Antecipação de Recebíveis');

  var alternateEntity = mimemessage.factory({
    contentType: 'multipart/alternate',
    body: [],
  });

  var plainEntity = mimemessage.factory({
    body: 'Voce esta recebendo este email para conferencia e assinatura do Contrato de Antecipação Recebíveis \
          asssine e enviei para o banco BankMe',
  });

  alternateEntity.body.push(plainEntity);

  mailContent.body.push(alternateEntity);

  var attachmentEntity = mimemessage.factory({
    contentType: 'text/plain',
    contentTransferEncoding: 'base64',
    //body: event.pdf64.toString('base64').replace(/([^**\0**]{76})/g, "$1\n")
    body: event.pdf64.replace(/([^**\0**]{76})/g, '$1\n'),
  });

  attachmentEntity.header('Content-Disposition', 'attachment ;filename=' + event.arqadt);

  mailContent.body.push(attachmentEntity);

  const res = await SES.sendRawEmail({ RawMessage: { Data: mailContent.toString() } }).promise();

  console.log('envio email:', res);

  return res;
};
