import { dbQuery } from 'capfunctions';

export const handler = async (event) => {
  console.log(event);

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify(''),
    };
  }

  if (!event.body?.creditNoteId || !event.body?.status || !event.body?.firstPaymentDate) {
    return {
      statusCode: 400,
      body: JSON.stringify('Missing required fields'),
    };
  }

  const creditNoteId = event?.creditNoteId;
  const status = event?.status;
  const firstPaymentDate = event?.firstPaymentDate;

  let response = {
    statusCode: 200,
    body: JSON.stringify(''),
  };

  try {
    const [antecipacao] = await dbQuery(
      /*sql*/ `
        SELECT *
        FROM capAntecipacao
        WHERE anKuaraId = ?
        `,
      [creditNoteId]
    );

    if (!antecipacao) {
      return {
        statusCode: 400,
        body: JSON.stringify('Antecipacao não encontrada'),
      };
    }

    await dbQuery(
      /*sql*/ `
        INSERT INTO capAntecipacaoHistorico (anNrAntecipacao, anKuaraStatus)
        VALUES (?, ?);

        UPDATE capAntecipacao
        SET anKuaraStatus = ?
        WHERE anNrAntecipacao = ?;
        `,
      [antecipacao.anNrAntecipacao, status, status, antecipacao.anNrAntecipacao]
    );
  } catch (error) {
    console.log(error);

    response = {
      statusCode: 500,
      body: JSON.stringify(''),
    };
  }

  return response;
};
