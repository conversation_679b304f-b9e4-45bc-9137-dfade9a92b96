import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Document Handler for WhatsApp Business API
 * Handles PDF document uploads and messaging
 */
export class DocumentHandler {
  constructor(whatsappToken, phoneNumberId) {
    this.whatsappToken = whatsappToken;
    this.phoneNumberId = phoneNumberId;
    this.mediaUploadUrl = `https://graph.facebook.com/v17.0/${phoneNumberId}/media`;
  }

  /**
   * Upload document to WhatsApp Media API
   * @param {string} filePath - Path to the document file
   * @returns {Promise<string|null>} - Media ID or null if failed
   */
  async uploadDocument(filePath) {
    try {
      // Resolve the file path relative to the wpphook directory
      const fullPath = path.resolve(__dirname, '..', filePath);

      if (!fs.existsSync(fullPath)) {
        console.error(`Document file not found: ${fullPath}`);
        return null;
      }

      // Read file as buffer
      const fileBuffer = fs.readFileSync(fullPath);
      const fileName = path.basename(fullPath);

      // Create form data for upload
      const formData = new FormData();
      formData.append('file', new Blob([fileBuffer], { type: 'application/pdf' }), fileName);
      formData.append('type', 'application/pdf');
      formData.append('messaging_product', 'whatsapp');

      // Upload to WhatsApp Media API
      const response = await fetch(this.mediaUploadUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.whatsappToken}`,
        },
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('WhatsApp Media upload failed:', errorData);
        return null;
      }

      const responseData = await response.json();
      console.log('Document uploaded successfully:', responseData.id);
      return responseData.id;
    } catch (error) {
      console.error('Error uploading document to WhatsApp:', error);
      return null;
    }
  }

  /**
   * Send document message using uploaded media ID
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} mediaId - WhatsApp media ID
   * @param {string} caption - Document caption
   * @param {string} filename - Document filename
   * @returns {Promise<boolean>} - Success status
   */
  async sendDocumentMessage(phoneNumber, mediaId, caption = '', filename = '') {
    try {
      const messageUrl = `https://graph.facebook.com/v17.0/${this.phoneNumberId}/messages`;

      const requestBody = {
        messaging_product: 'whatsapp',
        to: phoneNumber,
        type: 'document',
        document: {
          id: mediaId,
          caption: caption,
          filename: filename,
        },
      };

      const response = await fetch(messageUrl, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.whatsappToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('WhatsApp document message failed:', errorData);
        return false;
      }

      const responseData = await response.json();
      console.log('Document message sent successfully:', responseData.messages?.[0]?.id);
      return true;
    } catch (error) {
      console.error('Error sending document message:', error);
      return false;
    }
  }

  /**
   * Upload and send document in one operation
   * @param {string} phoneNumber - Recipient phone number
   * @param {string} filePath - Path to document file
   * @param {string} caption - Document caption
   * @param {string} filename - Document filename
   * @returns {Promise<boolean>} - Success status
   */
  async uploadAndSendDocument(phoneNumber, filePath, caption = '', filename = '') {
    console.log('Uploading and sending document:', { phoneNumber, filePath, caption, filename });

    // Upload document first
    const mediaId = await this.uploadDocument(filePath);
    if (!mediaId) {
      console.error('Failed to upload document, cannot send message');
      return false;
    }

    // Send document message
    return await this.sendDocumentMessage(phoneNumber, mediaId, caption, filename);
  }

  /**
   * Get document file info
   * @param {string} filePath - Path to document file
   * @returns {Object|null} - File info or null if not found
   */
  getDocumentInfo(filePath) {
    try {
      const fullPath = path.resolve(__dirname, '..', filePath);

      if (!fs.existsSync(fullPath)) {
        return null;
      }

      const stats = fs.statSync(fullPath);
      const fileName = path.basename(fullPath);

      return {
        fileName,
        fullPath,
        size: stats.size,
        extension: path.extname(fullPath),
        exists: true,
      };
    } catch (error) {
      console.error('Error getting document info:', error);
      return null;
    }
  }
}
