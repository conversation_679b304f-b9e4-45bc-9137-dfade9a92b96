import {
  createResponse,
  checkData,
  execQuery,
  baseResponse,
  dbQuery,
  insert,
  update,
  deleteEntity,
  onlyNumber,
  isEmailValid,
  lambdaInvoke,
}
from 'capfunctions';

async function _list(event) {
  try {
    console.log(event);
    if (checkData(event, ['clCliente'])) {
      return baseResponse.error("Dados insuficentes");
    }

    const query = `SELECT
                  a.ocNrContrato, a.clCliente, b.clNomeCliente, a.isInstSaude, c.isNome, a.ocDataContrato, a.ocValorFixo, a.ocValorVariavel
                  FROM capInstSaudeContrato a
                  left join capCliente b on a.clCliente = b.clCliente
                  left join capInstSaude c on a.isInstSaude = c.isInstSaude
                  WHERE a.clCliente = ?
                  `;
    const params = [event.clCliente];
    const response = await execQuery(query, params);
    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _list", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _find(event) {
  try {
    if (checkData(event, ['ocNrContrato'])) {
      return baseResponse.error("Dados insuficentes");
    }

    const query = `SELECT
                  a.ocNrContrato, a.clCliente, b.clNomeCliente, a.isInstSaude, c.isNome, a.ocDataContrato, a.ocValorFixo, a.ocValorVariavel
                  FROM capInstSaudeContrato a
                  left join capCliente b on a.clCliente = b.clCliente
                  left join capInstSaude c on a.isInstSaude = c.isInstSaude
                  WHERE a.ocNrContrato = ?
                  `;


    const params = [event.ocNrContrato];
    const response = await execQuery(query, params);

    if (response && response.results && response.results.length > 0)
      return baseResponse.ok('', response.results && response.results[0])

    return baseResponse.error("Não encontrado");

  }
  catch (error) {
    console.log("ERROR _find", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _insert(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'clCliente', 'isInstSaude', 'ocDataContrato', 'ocValorFixo', 'ocValorVariavel'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    if (!isEmailValid(event.isEmail)) {
      //return createResponse(400, 'Formato de email inválido.', '', null);
    }

    const entity = {
      clCliente: event.clCliente,
      isInstSaude: event.isInstSaude,
      ocDataContrato: event.ocDataContrato,
      ocValorFixo: event.ocValorFixo,
      ocValorVariavel: event.ocValorVariavel
    };

    const response = await insert('capInstSaudeContrato', entity, event);

    if (response.success) {
      return baseResponse.created('Criado com successo');
    }

    return baseResponse.notFound('Não encontrado');
  }
  catch (error) {
    console.log("ERROR _insert", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _update(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'ocNrContrato', 'clCliente', 'isInstSaude', 'ocDataContrato', 'ocValorFixo', 'ocValorVariavel'
        ],
      })
    ) {
      return baseResponse.error("Dados insuficentes");
    }

    //if (!isEmailValid(event.isEmail)) {
    //return createResponse(400, 'Formato de email inválido.', '', null);
    //}

    const entity = {
      ocDataContrato: event.ocDataContrato,
      ocValorFixo: event.ocValorFixo,
      ocValorVariavel: event.ocValorVariavel
    };
    //      dtModificacao: 'current_timestamp' truncando

    const response = await update('capInstSaudeContrato', entity, {
      clCliente: event.clCliente,
      isInstSaude: event.isInstSaude,
      ocNrContrato: event.ocNrContrato
    }, event);

    if (response.success) {
      return baseResponse.ok('Atualizado com sucesso', response.results)
    }
    console.info(response.success);

    return baseResponse.error("Erro ao atualizar contrato");
  }
  catch (error) {
    console.log("ERROR _update", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _listlocalespecs(event) {
  console.log(event);
  try {
    const query = `SELECT esEspecialidade FROM capInstSaudeEspecialidade WHERE isInstSaude = ? and laNome = ?
                  `;
    const params = [event.isInstSaude, event.laNome];
    const response = await execQuery(query, params);
    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _listlocalespecs", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _listlocalespeccontratos(event) {
  if (
    checkData(event, {
      obrigatorios: [
        'isInstSaude', 'laNome', 'ocNrContrato',
      ],
    })
  ) {
    return baseResponse.error("Dados insuficentes");
  }

  try {
    const query = `SELECT cise.esEspecialidade, cisce.ceTipoPagamento, cisce.ceValorFixoProf, cisce.ceValorHoraProf, cisce.ceValorUnitProf FROM capInstSaudeEspecialidade cise
                    INNER JOIN capInstSaudeContratoEspec cisce on (
                      cise.isInstSaude = cisce.isInstSaude AND
                      cise.laNome = cisce.laNome AND
                      cise.esEspecialidade = cisce.esEspecialidade
                    )
                    WHERE cise.isInstSaude = ?  and cise.laNome = ? and cisce.ocNrContrato = ? 
                  `;
    const params = [event.isInstSaude, event.laNome, event.ocNrContrato];

    const response = await execQuery(query, params);
    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _listlocalespecs", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _listcontratolocalespecs(event) {
  console.log(event);
  try {
    const query = `SELECT 
                      isInstSaude, 
                      laNome, esEspecialidade, 
                      ocNrContrato, clCliente, 
                      ceValorHora, ceQtdHoraMes, 
                      ceValorFixoProf, ceValorHoraProf, ceValorHoraProf,
                      ceTaxaDesagio, ceQtdDiasMinRecebim, ceTipoPagamento,ceValorUnitProf
                    FROM capInstSaudeContratoEspec
                    WHERE isInstSaude = ?
                    and   laNome = ?
                    and   ocNrContrato = ?
                    and   clCliente = ?
                  `;
    const params = [event.isInstSaude, event.laNome, event.ocNrContrato, event.clCliente];
    const response = await execQuery(query, params);
    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _listcontratolocalespecs", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _listLocaisInst(event) {
  console.log(event);
  try {
    const query = `SELECT laNome, isInstSaude, laEndereco, laNrEnd, laComplEnd, laBairro, laCidade, laUF, laDescricao, laCEP
                    FROM capInstSaudeLocalAtendimento
                    WHERE isInstSaude = ?
                  `;
    const params = [event.isInstSaude];
    const response = await execQuery(query, params);
    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _listlocaisinst", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}


async function _insertlocalespec(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude', 'laNome', 'esEspecialidade', 'ocNrContrato', 'clCliente', 'ceTipoPagamento', 'ceValorHora',
          'ceQtdHoraMes', 'ceTaxaDesagio', 'ceQtdDiasMinRecebim'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const entity = {
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      esEspecialidade: event.esEspecialidade,
      ocNrContrato: event.ocNrContrato,
      clCliente: event.clCliente,
      ceTipoPagamento: event.ceTipoPagamento,
      ceValorHora: event.ceValorHora,
      ceQtdHoraMes: event.ceQtdHoraMes,
      ceValorHoraProf: event.ceValorHoraProf,
      ceValorFixoProf: event.ceValorFixoProf,
      ceValorUnitProf: event.ceValorUnitProf,
      ceTaxaDesagio: event.ceTaxaDesagio,
      ceQtdDiasMinRecebim: event.ceQtdDiasMinRecebim
    };

    const response = await insert('capInstSaudeContratoEspec', entity, event);

    if (response.success) {
      return baseResponse.created('Criado com successo');
    }
      
    if (response.error.code === "ER_DUP_ENTRY") {
      return baseResponse.badRequest('Tipo de pagamento já existente para essa especialidade!')
    }
    
    return baseResponse.badRequest(response);
  }
  catch (error) {
    console.log("ERROR _insertlocalespec", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _updatelocalespec(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude', 'laNome', 'esEspecialidade', 'ocNrContrato', 'clCliente', 'ceTipoPagamento', 'ceValorHora',
          'ceQtdHoraMes', 'ceTaxaDesagio', 'ceQtdDiasMinRecebim'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const entity = {
      "ceValorHora": event.ceValorHora,
      "ceQtdHoraMes": event.ceQtdHoraMes,
      "ceValorHoraProf": event.ceValorHoraProf,
      "ceValorFixoProf": event.ceValorFixoProf,
      "ceValorUnitProf": event.ceValorUnitProf,
      "ceTaxaDesagio": event.ceTaxaDesagio,
      'ceQtdDiasMinRecebim': event.ceQtdDiasMinRecebim
    };

    if (event.ceTipoPagamento) {
      entity.ceTipoPagamento = event.ceTipoPagamento;
    }
    
    if (event.ceValorUnitProf) {
      entity.ceValorUnitProf = event.ceValorUnitProf;
      entity.ceValorFixoProf = null;
      entity.ceValorHoraProf = null;
    }

    if (event.ceValorFixoProf) {
      entity.ceValorFixoProf = event.ceValorFixoProf;
      entity.ceValorHoraProf = null;
      entity.ceValorUnitProf = null;
    }

    if (event.ceValorHoraProf) {
      entity.ceValorHoraProf = event.ceValorHoraProf;
      entity.ceValorFixoProf = null;
      entity.ceValorUnitProf = null;
    }

    const response = await update('capInstSaudeContratoEspec', entity, {
      "clCliente": event.clCliente,
      "ocNrContrato": event.ocNrContrato,
      "isInstSaude": event.isInstSaude,
      "laNome": event.laNome,
      "esEspecialidade": event.esEspecialidade,
    }, event);

    if (response.success) {
      return baseResponse.ok('Atualizado com sucesso', response.results)
    }

    return baseResponse.error("Erro ao atualizar");
  }
  catch (error) {
    console.log("ERROR _updatelocalespec", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _deletelocalespec(event) {
  console.log(event);
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude', 'laNome', 'esEspecialidade', 'ocNrContrato', 'clCliente', 'ceTipoPagamento'
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const response = await deleteEntity('capInstSaudeContratoEspec', {
      ocrNrContrato: event.ocrNrContrato,
      clCliente: event.clCliente,
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      esEspecialidade: event.esEspecialidade,
      ceTipoPagamento: event.ceTipoPagamento
    }, event)
    if (response.success)
      return baseResponse.created('Excluido com sucesso', response.results)

    return baseResponse.error("Erro ao excluir");
  }
  catch (error) {
    console.log("ERROR _deletelocalespec", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

export {
  _insert,
  _update,
  _list,
  _find,
  _listlocalespecs,
  _insertlocalespec,
  _updatelocalespec,
  _deletelocalespec,
  _listcontratolocalespecs,
  _listLocaisInst,
  _listlocalespeccontratos
};
