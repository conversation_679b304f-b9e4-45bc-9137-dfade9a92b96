{"id": null, "tenantId": "473680", "name": "GS2 - Autenticação - Médicos", "created": null, "flowNodes": [{"flowReplies": [{"flowReplyType": "Text", "data": "<p>🏥 Qual o nome da clínica ou hospital que nos indicou para você?</p>", "caption": "", "mimeType": ""}], "userInputVariable": "ClinicaHospital", "answerValidation": {"type": "None", "minValue": "", "maxValue": "", "regex": "", "fallback": "I'm afraid I didn't understand, could you try again, please?", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-xNBYp", "flowNodeType": "Question", "flowNodePosition": {"posX": "-253", "posY": "343"}, "isStartNode": true}, {"flowNodeConditions": [{"id": "pDLKOWU", "flowConditionType": "Equal", "variable": "@ClinicaHospital", "value": "Gastrocentro"}], "conditionResult": {"yResultNodeId": "main_updateAttribute-FYkPw", "nResultNodeId": "main_condition-SstSa"}, "conditionOperator": "None", "id": "main_condition-oJSys", "flowNodeType": "Condition", "flowNodePosition": {"posX": "738", "posY": "329"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>Você atualmente presta serviços médicos e é remunerado pela:<br>\n<br>\n- <strong>Razão Social:</strong> @RazaoSocial<br>\n- <strong>CNPJ:</strong> &nbsp;@CNPJ</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "OntdTsr", "buttonText": "✅ Sim, isso mesmo.", "nodeResultId": "main_message-spdjN"}, {"id": "gzrEzod", "buttonText": "❌ Deve ser engano.", "nodeResultId": "main_question-wMimn"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-hdrQp", "id": "main_buttons-PmyoT", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "2188", "posY": "-206"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "wkIbRDC", "flowConditionType": "Equal", "variable": "@ClinicaHospital", "value": "H2"}], "conditionResult": {"yResultNodeId": "main_updateAttribute-mALIS", "nResultNodeId": "main_question-ApXio"}, "conditionOperator": "None", "id": "main_condition-SstSa", "flowNodeType": "Condition", "flowNodePosition": {"posX": "1117", "posY": "501"}, "isStartNode": false}, {"attributeVariables": [{"type": "ContactInputVariable", "name": "RazaoSocial", "value": "Gastrocentro CTG - Centro Mineiro de Endoscopia Digestiva Avançada Ltda."}, {"type": "ContactInputVariable", "name": "CNPJ", "value": "25.166.937/0001-62"}], "id": "main_updateAttribute-FYkPw", "flowNodeType": "UpdateAttribute", "flowNodePosition": {"posX": "1503", "posY": "344"}, "isStartNode": false}, {"attributeVariables": [{"type": "ContactInputVariable", "name": "RazaoSocial", "value": "H2 Tecnologia e Serviços Médicos Ltda."}, {"type": "ContactInputVariable", "name": "CNPJ", "value": "04.189.941/0001-76"}], "id": "main_updateAttribute-mALIS", "flowNodeType": "UpdateAttribute", "flowNodePosition": {"posX": "1574", "posY": "516"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>🔎 <strong><PERSON><PERSON><PERSON><PERSON>, mas não encontramos esse nome em nosso sistema.</strong></p>\n<p><br>\n<PERSON>r favor, tente de novo ou escreva sair para encerrar a conversa.</p>", "caption": "", "mimeType": ""}], "userInputVariable": "ClinicaHospital", "answerValidation": {"type": "None", "minValue": "", "maxValue": "", "regex": "", "fallback": "I'm afraid I didn't understand, could you try again, please?", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-ApXio", "flowNodeType": "Question", "flowNodePosition": {"posX": "1528", "posY": "880"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "ujnImbX", "flowConditionType": "Equal", "variable": "@ClinicaHospital", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_invokeFlow-eMRoY", "nResultNodeId": "main_condition-oJSys"}, "conditionOperator": "None", "id": "main_condition-WYdXc", "flowNodeType": "Condition", "flowNodePosition": {"posX": "203", "posY": "343"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-eMRoY", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "596", "posY": "90"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>Por favor, escreva novamente o nome da clínica ou hospital que nos indicou.<br>\n<br>\nSe preferir, pode encerrar a conversa escrevendo: <strong>Sair</strong></p>", "caption": "", "mimeType": ""}], "userInputVariable": "ClinicaHospital", "answerValidation": {"type": "None", "minValue": "", "maxValue": "", "regex": "", "fallback": "I'm afraid I didn't understand, could you try again, please?", "failsCount": "3"}, "isMediaAccepted": false, "expectedAnswers": null, "id": "main_question-wMimn", "flowNodeType": "Question", "flowNodePosition": {"posX": "2561", "posY": "335"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-EqGhb", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "3171", "posY": "-67"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p><PERSON><PERSON><PERSON>!</p>", "caption": "", "mimeType": ""}], "id": "main_message-spdjN", "flowNodeType": "Message", "flowNodePosition": {"posX": "2698", "posY": "-135"}, "isStartNode": false}, {"flowNodeConditions": [{"id": "uAfJjTb", "flowConditionType": "Equal", "variable": "@RespostaLivre", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_invokeFlow-eMRoY", "nResultNodeId": "main_buttons-PmyoT"}, "conditionOperator": "None", "id": "main_condition-hdrQp", "flowNodeType": "Condition", "flowNodePosition": {"posX": "1194", "posY": "-98"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_condition-SstSatrue-main_updateAttribute-mALIS", "sourceNodeId": "main_condition-SstSa__true", "targetNodeId": "main_updateAttribute-mALIS"}, {"id": "reactflow__edge-main_condition-SstSafalse-main_question-ApXio", "sourceNodeId": "main_condition-SstSa__false", "targetNodeId": "main_question-ApXio"}, {"id": "reactflow__edge-main_question-xNBYp-main_condition-WYdXc", "sourceNodeId": "main_question-xNBYp", "targetNodeId": "main_condition-WYdXc"}, {"id": "reactflow__edge-main_updateAttribute-mALIS-main_buttons-PmyoT", "sourceNodeId": "main_updateAttribute-mALIS", "targetNodeId": "main_buttons-PmyoT"}, {"id": "reactflow__edge-main_updateAttribute-FYkPw-main_buttons-PmyoT", "sourceNodeId": "main_updateAttribute-FYkPw", "targetNodeId": "main_buttons-PmyoT"}, {"id": "reactflow__edge-main_buttons-PmyoTgzrEzod-main_question-wMimn", "sourceNodeId": "main_buttons-PmyoT__gzrEzod", "targetNodeId": "main_question-wMimn"}, {"id": "reactflow__edge-main_question-wMimn-main_condition-WYdXc", "sourceNodeId": "main_question-wMimn", "targetNodeId": "main_condition-WYdXc"}, {"id": "reactflow__edge-main_question-ApXio-main_condition-WYdXc", "sourceNodeId": "main_question-ApXio", "targetNodeId": "main_condition-WYdXc"}, {"id": "reactflow__edge-main_buttons-PmyoTOntdTsr-main_message-spdjN", "sourceNodeId": "main_buttons-PmyoT__OntdTsr", "targetNodeId": "main_message-spdjN"}, {"id": "reactflow__edge-main_message-spdjN-main_invokeFlow-EqGhb", "sourceNodeId": "main_message-spdjN", "targetNodeId": "main_invokeFlow-EqGhb"}, {"id": "reactflow__edge-main_condition-WYdXcfalse-main_condition-oJSys", "sourceNodeId": "main_condition-WYdXc__false", "targetNodeId": "main_condition-oJSys"}, {"id": "reactflow__edge-main_condition-WYdXctrue-main_invokeFlow-eMRoY", "sourceNodeId": "main_condition-WYdXc__true", "targetNodeId": "main_invokeFlow-eMRoY"}, {"id": "reactflow__edge-main_condition-oJSysfalse-main_condition-SstSa", "sourceNodeId": "main_condition-oJSys__false", "targetNodeId": "main_condition-SstSa"}, {"id": "reactflow__edge-main_condition-oJSystrue-main_updateAttribute-FYkPw", "sourceNodeId": "main_condition-oJSys__true", "targetNodeId": "main_updateAttribute-FYkPw"}, {"id": "reactflow__edge-main_buttons-PmyoTmain_buttons-PmyoT-default-main_condition-hdrQp", "sourceNodeId": "main_buttons-PmyoT__main_buttons-PmyoT-default", "targetNodeId": "main_condition-hdrQp"}, {"id": "reactflow__edge-main_condition-hdrQptrue-main_invokeFlow-eMRoY", "sourceNodeId": "main_condition-hdrQp__true", "targetNodeId": "main_invokeFlow-eMRoY"}, {"id": "reactflow__edge-main_condition-hdrQpfalse-main_buttons-PmyoT", "sourceNodeId": "main_condition-hdrQp__false", "targetNodeId": "main_buttons-PmyoT"}], "lastUpdated": "2025-07-28T02:44:00.457Z", "isDeleted": false, "transform": {"posX": "-50.916223579738016", "posY": "156.86463407937237", "zoom": "0.5000000000000001"}, "isPro": true, "channelTypes": ["WA"]}