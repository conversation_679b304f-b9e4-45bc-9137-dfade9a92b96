import { dbQuery } from 'capfunctions';

export class AgendaRepository {
  async getAgendasForAtendimento(atendimento) {
    const sql = /*sql*/ `
      SELECT 
          *,
          agendas.isInstSaude,
          agendas.laNome,
          agendas.esEspecialidade,
          agendas.ocNrContrato,
          agendas.clCliente,
          agendas.ceTipoPagamento,
          agendas.opNrPlantao,
          agendas.agData,
          agendas.agDiaSem,
          agendas.agDataIni,
          agendas.agDataFim,
          agendas.agIntervalo,
          agendas.dtInclusao,
          agendas.dtModificacao,
          agendas.agAtivo,
          -- Calcula as horas a trabalhar (total do período descontando o intervalo)
          TIME_FORMAT(
              SEC_TO_TIME(
                  TIMESTAMPDIFF(SECOND, agDataIni, agDataFim)
                  - TIME_TO_SEC(COALESCE(agIntervalo, '00:00:00'))
              ),
              '%H:%i:%s'
          ) AS horasTrabalhadas
      FROM 
          capInstSaudePlantaoAgenda agendas
      LEFT JOIN 
          (
              SELECT 
                  checks.isInstSaude,
                  checks.laNome,
                  checks.esEspecialidade,
                  checks.ocNrContrato,
                  checks.clCliente,
                  checks.ceTipoPagamento,
                  checks.opNrPlantao,
                  checks.agData,
                  SEC_TO_TIME(SUM(TIME_TO_SEC(TIMEDIFF(checks.ocCheckOut, checks.ocCheckIn)))) as totalHorasTrabalhadas
              FROM 
                  capOperPlantaoCheck checks
              GROUP BY 
                  checks.isInstSaude,
                  checks.laNome,
                  checks.esEspecialidade,
                  checks.ocNrContrato,
                  checks.clCliente,
                  checks.ceTipoPagamento,
                  checks.opNrPlantao,
                  checks.agData
          ) as totalCheckInOut ON (
              totalCheckInOut.isInstSaude = agendas.isInstSaude
              AND totalCheckInOut.laNome = agendas.laNome
              AND totalCheckInOut.opNrPlantao = agendas.opNrPlantao
              AND totalCheckInOut.ceTipoPagamento = agendas.ceTipoPagamento
              AND totalCheckInOut.clCliente = agendas.clCliente
              AND totalCheckInOut.ocNrContrato = agendas.ocNrContrato
              AND totalCheckInOut.esEspecialidade = agendas.esEspecialidade
              AND DATE(totalCheckInOut.agData) = DATE(agendas.agData)
          )
      WHERE
          agendas.agAtivo = 1
          AND agendas.isInstSaude = ?
          AND agendas.laNome = ?
          AND agendas.esEspecialidade = ?
          AND agendas.ocNrContrato = ?
          AND agendas.clCliente = ?
          AND agendas.ceTipoPagamento = ?
          AND agendas.opNrPlantao = ?
          AND (
              totalCheckInOut.totalHorasTrabalhadas IS NULL 
              OR TIME_TO_SEC(totalCheckInOut.totalHorasTrabalhadas) < ( TIMESTAMPDIFF(SECOND, agDataIni, agDataFim) - TIME_TO_SEC(COALESCE(agIntervalo, '00:00:00')) )
          )
          AND DATE(agendas.agData) < DATE(current_timestamp())
      ORDER BY 
          agendas.agData DESC;
    `;
    const params = [
      atendimento.isInstSaude,
      atendimento.laNome,
      atendimento.esEspecialidade,
      atendimento.ocNrContrato,
      atendimento.clCliente,
      atendimento.ceTipoPagamento,
      atendimento.opNrPlantao,
    ];
    return await dbQuery(sql, params);
  }
}
