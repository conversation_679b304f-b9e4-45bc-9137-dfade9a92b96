const AWS = require("aws-sdk");
const S3 = new AWS.S3({ region: process.env.REGION || "us-west-2" });
const S3_BUCKET = process.env.S3_BUCKET || "capitale-csv";

const mysql = require("mysql");

const connOptions = {
  host: '',
  user: 'admin',
  password: '',
  database: '',
  multipleStatements: true,
};



const connection = mysql.createConnection(connOptions);


async function execQuery(query, params = []) {
  console.log("execQuery => ", { query, params });
  return new Promise(function (resolve, reject) {
    connection.query(query, params, function (err, response) {
      console.log(`Resultado da query ${query}`, `results: ${response}`);
      if (err) {
        console.log(`Erro na query ${query}`, `erro: ${err}`);
        reject(err);
      } else {
        resolve(response);
      }
    });
  });
}


async function find(table, { columns, where }) {
  let query = `SELECT ${columns.length > 0 ? columns.join(", ") : "*"} FROM ${table}`;
  query += " WHERE ";
  query += (where);
//  query += Object.keys(where)
//    .map((k) => `${k} > ?`)
//    .join(" and ");

  const params = Object.values(where);
  return await execQuery(query, params);
}

const s3Upload = async (inputStream, pathImage) => {
  try {
    const params = {
        Bucket: S3_BUCKET,
        Key: pathImage,
        Body: inputStream,
        ContentType: "text/csv"
    };
    return await S3.upload(params).promise();
  } catch (error) {
    console.log("Catch no metodo s3Upload()", error);
  }
};

const storeImageBase64 = async (image, type, pathImage) => {
  try {
    const decodedImage = Buffer.from(image, "base64");
    return await S3.upload({
      Bucket: S3_BUCKET,
      Body: decodedImage,
      Key: pathImage,
      ContentType: type,
    }).promise();
  } catch (error) {
    console.log("Catch no metodo storeImageBase64()", error);
  }
};


const getObjectFromS3 = async (Key) => {
  const params = {
    Bucket: 'capitale_images',
    Key,
  };
  return await S3.getObject(params).promise();
};

function getImage() {
  const data = S3
    .getObject({
      Bucket: "capitale_images",
      Key: "capitale_capital_favicon2.png",
    })
    .promise();
  return data;
}

const lambdaInvoke = (FunctionName, Payload) => {
  const aws = require("aws-sdk");
  const lambda = new aws.Lambda({
    region: process.env.REGION || "us-west-2", // oregon
  });

  return new Promise((resolve, reject) => {
    lambda.invoke(
      {
        FunctionName,
        Payload: JSON.stringify(Payload, null, 2),
      },
      (err, data) => {
        if (err) {
          reject(err);
        }
        let payload = JSON.parse(data.Payload);
        resolve(payload);
      }
    );
  });
};

module.exports = {
  storeImageBase64,
  getObjectFromS3,
  getImage,
  lambdaInvoke,
  s3Upload,
  execQuery,
  find,

};
