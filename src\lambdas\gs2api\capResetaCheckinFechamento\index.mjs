import { dbQuery } from 'capfunctions';

export const handler = async () => {
  console.log('Iniciando processo para desfazer TODOS os fechamentos...');

  try {
    const fechamentos = await dbQuery('SELECT * FROM capCodFechamento');

    console.log(`Encontrados ${fechamentos.length} fechamentos para desfazer`);

    if (fechamentos.length === 0) {
      return {
        statusCode: 200,
        body: JSON.stringify({ message: 'Nenhum fechamento encontrado para desfazer' }),
      };
    }
    
    const resultChecks = await dbQuery(/*sql*/ `
        DELETE FROM capOperPlantaoCheck
    `);

    console.log(`Reabertos ${resultChecks.affectedRows} checks na tabela 'capOperPlantaoCheck'`);

    const resultAtendimentos = await dbQuery(/*sql*/ `
      UPDATE capInstSaudePlantao 
      SET opSituacao = 'EmExecucao'
      WHERE opSituacao = 'Concluido' AND opAtivo = 1
    `);

    console.log(
      `Atualizados ${resultAtendimentos.affectedRows} registros na tabela 'capInstSaudePlantao'`
    );

    const resultDeleteAtendimentoFechamentos = await dbQuery(/*sql*/ `
      DELETE FROM capAtendimentoFechamento
    `);

    console.log(
      `Removidos ${resultDeleteAtendimentoFechamentos.affectedRows} registros da tabela 'capAtendimentoFechamento'`
    );

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Todos os fechamentos foram desfeitos com sucesso',
      }),
    };
  } catch (error) {
    console.error('Erro ao desfazer fechamentos:', error);

    return {
      statusCode: 500,
      body: JSON.stringify({
        error: 'Erro ao desfazer fechamentos',
        message: error.message,
      }),
    };
  }
};
