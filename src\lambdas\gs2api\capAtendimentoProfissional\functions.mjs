import {
    createResponse,
    checkData,
    execQuery,
    baseResponse,
    dbQuery,
    insert,
    update,
    onlyNumber,
    isEmailValid,
    lambdaInvoke,
} from 'capfunctions';

import { _realizarCheck } from './methods/realizarCheck.mjs';

async function _list(event) {
    const cpf = event?.body?.user;

    if (!cpf) {
        return baseResponse.notFound('Usuário não encontrado.');
    }

    let conditions = '';
    if (event.opSituacoes?.length > 0) {
        conditions += ` AND cisp.opSituacao IN (${event.opSituacoes
            .map((situacao) => `'${situacao}'`)
            .join(',')})`;
    }

    if (event.psSituacoes?.length > 0) {
        conditions += ` AND (cisps.psSituacao IN (${event.psSituacoes
            .map((situacao) => `'${situacao}'`)
            .join(',')})`;

        if (event.psSituacaoNull) {
            conditions += ' OR cisps.psSituacao IS NULL';
        }

        conditions += ')';
    } else {
        if (event.psSituacaoNull) {
            conditions += ' AND cisps.psSituacao IS NULL';
        }
    }

    if (process.env.IS_LOCAL !== "true") {
        conditions += ` AND cisp.opPeriodoFim >= NOW()`;
    }

    const query = /*sql*/ `
        SELECT 
            cisp.isInstSaude,
            cisp.laNome,
            cisp.esEspecialidade,
            cisp.ocNrContrato,
            cisp.clCliente,
            cisp.opNrPlantao,
            cisp.opAtivo,
            cisp.opPeriodoIni,
            cisp.opPeriodoFim,
            cisp.psCPF,
            cisp.opSituacao,
            cisp.ceTipoPagamento,
            cisp.opValorFixo,
            cisp.opValorUnit,
            cisp.opTipoFechamento,
            cisps.psSituacao
        FROM
            capInstSaudePlantao cisp
            INNER JOIN
            capProfSaudeEspecialidade cpse ON cpse.esEspecialidade = cisp.esEspecialidade
            INNER JOIN capProfSaudeCliente cpsc ON (cisp.clCliente = cpsc.clCliente AND cpsc.psCPF = ${cpf} )
            LEFT JOIN
            capInstSaudePlantaoSolicitacao cisps ON (cisps.isInstSaude = cisp.isInstSaude
                AND cisps.laNome = cisp.laNome
                AND cisps.esEspecialidade = cisp.esEspecialidade
                AND cisps.ocNrContrato = cisp.ocNrContrato
                AND cisps.clCliente = cisp.clCliente
                AND cisps.opNrPlantao = cisp.opNrPlantao
                AND cisps.psCPF = ${cpf})
        WHERE
            ((cpse.psCPF = ${cpf}
                AND cisp.psCPF = ${cpf})
                OR (cisp.psCPF IS NULL))
                ${conditions}
        GROUP BY cisp.opNrPlantao
    `;

    const response = await execQuery(query);

    if (response.success) {
        return baseResponse.ok('Listado com sucesso', response.results);
    }

    return baseResponse.error('Erro ao realizar listagem de atendimentos');
}

async function _solicitarAtendimento(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'ceTipoPagamento',
                    'opNrPlantao',
                    'psCPF',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const entity = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            ocNrContrato: event.ocNrContrato,
            clCliente: event.clCliente,
            ceTipoPagamento: event.ceTipoPagamento,
            psCPF: event.psCPF,
            opNrPlantao: event.opNrPlantao,
            psSituacao: 'Solicitado',
        };

        const response = await insert('capInstSaudePlantaoSolicitacao', entity, event);

        if (response.success) {
            const entityPlantao = {
                opSituacao: 'Solicitado',
            };

            const wherePlantao = {
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                ocNrContrato: event.ocNrContrato,
                opNrPlantao: event.opNrPlantao,
                clCliente: event.clCliente,
                ceTipoPagamento: event.ceTipoPagamento,
            };

            await update('capInstSaudePlantao', entityPlantao, wherePlantao, event);

            return baseResponse.created('Solicitação criada com sucesso');
        }

        return baseResponse.error('Erro ao criar solicitação');
    } catch (error) {
        console.log('ERROR _solicitarAtendimento', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _find(event) {
    const cpf = event?.body?.user;

    if (!cpf) {
        return baseResponse.notFound('Usuário não encontrado.');
    }

    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'ocNrContrato',
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'opNrPlantao',
                ],
            })
        )
            return baseResponse.error('Dados insuficentes');

        const query = /*sql*/ `
                SELECT 
                    cisp.clCliente,
                    cisp.ocNrContrato,
                    cisp.isInstSaude,
                    cisp.laNome,
                    cisp.esEspecialidade,
                    cisp.opNrPlantao,
                    cisp.usCPFUsuarioAprovador,
                    cisp.opDataDivulgacao,
                    cisp.opAtivo,
                    cisp.opDataFechamento,
                    cisp.opPeriodoIni,
                    cisp.opPeriodoFim,
                    cisp.opQtdHorasRequisitada,
                    cisp.psCPF,
                    cisp.opQtdHorasRealizadas,
                    cisp.opValorHora,
                    cisp.opChaveAcesso,
                    cisp.opSituacao,
                    cisp.opValorUnit,
                    cisp.opDiaFechamento,
                    cisp.opValorFixo,
                    cisp.opTipoFechamento,
                    cisp.ceTipoPagamento,
                    cis.isNome,
                    cisps.psDataSolicitacao,
                    cisps.psSituacao,
                    cisps.psSequenciaSolicitacao,

                    IF(
                        cisp.opAtivo = 1
                        AND cisp.opSituacao IN ('AguardExecucao', 'EmExecucao')
                        AND cisp.opPeriodoIni <= NOW()
                        AND cisp.opPeriodoFim >= NOW(),
                        1,
                        0
                    ) AS podeFazerCheckinCheckout
                    
                FROM
                    capInstSaudePlantao cisp
                        INNER JOIN
                    capInstSaude cis ON cis.isInstSaude = cisp.isInstSaude
                        LEFT JOIN
                    capInstSaudePlantaoSolicitacao cisps ON (cisps.clCliente = cisp.clCliente
                        AND cisps.ocNrContrato = cisp.ocNrContrato
                        AND cisps.isInstSaude = cisp.isInstSaude
                        AND cisps.laNome = cisp.laNome
                        AND cisps.esEspecialidade = cisp.esEspecialidade
                        AND cisps.opNrPlantao = cisp.opNrPlantao
                        AND cisps.psCPF = "${cpf}")
                WHERE
                    cisp.clCliente = "${event.clCliente}"
                        AND cisp.ocNrContrato = "${event.ocNrContrato}"
                        AND cisp.isInstSaude = "${event.isInstSaude}"
                        AND cisp.laNome = "${event.laNome}"
                        AND cisp.esEspecialidade = "${event.esEspecialidade}"
                        AND cisp.opNrPlantao = "${event.opNrPlantao}"
                  `;

        const response = await execQuery(query);

        if (response && response.results && response.results.length > 0) {
            var responseBody = response.results[0];
            return baseResponse.created('Listado com sucesso', responseBody);
        }

        return baseResponse.error('Erro ao buscar atendimento');
    } catch (error) {
        console.log('ERROR _find', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _listChecks(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'ocNrContrato',
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'opNrPlantao',
                    'psCPF',
                ],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        const query = /*sql*/ `
            SELECT 
                uuid() as id,
                c.*,
                DATE_FORMAT(c.ocCheckIn, '%Y-%m-%d %H:%i') as ocCheckIn,
                DATE_FORMAT(c.ocCheckOut, '%Y-%m-%d %H:%i') as ocCheckOut,
                c.ocQtAprovadas as hrsTrabalhadas,
                CASE 
                    WHEN pa.agTipoValor = 0 THEN 
                        CASE c.ceTipoPagamento
                            WHEN 'Hora' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorHora
                            WHEN 'Fixo' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorFixo
                            WHEN 'Unitario' THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorUnit
                            ELSE 0
                        END
                    WHEN pa.agTipoValor = 1 THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorExtra1
                    WHEN pa.agTipoValor = 2 THEN TIME_TO_SEC(c.ocQtAprovadas) / 3600 * sp.opValorExtra2
                    ELSE 0
                END AS valor
            FROM 
                capOperPlantaoCheck c
            INNER JOIN 
                capInstSaudePlantaoAgenda pa 
                ON c.agData = pa.agData 
                AND c.isInstSaude = pa.isInstSaude 
                AND c.laNome = pa.laNome 
                AND c.esEspecialidade = pa.esEspecialidade 
                AND c.ocNrContrato = pa.ocNrContrato 
                AND c.clCliente = pa.clCliente 
                AND c.ceTipoPagamento = pa.ceTipoPagamento 
                AND c.opNrPlantao = pa.opNrPlantao
            INNER JOIN 
                capInstSaudePlantao sp 
                ON pa.isInstSaude = sp.isInstSaude 
                AND pa.laNome = sp.laNome 
                AND pa.esEspecialidade = sp.esEspecialidade 
                AND pa.ocNrContrato = sp.ocNrContrato 
                AND pa.clCliente = sp.clCliente 
                AND pa.ceTipoPagamento = sp.ceTipoPagamento 
                AND pa.opNrPlantao = sp.opNrPlantao

                WHERE sp.psCPF = ?
                AND c.isInstSaude = ?
                AND c.laNome = ?
                AND c.esEspecialidade = ?
                AND c.ocNrContrato = ?
                AND c.clCliente = ?
                AND c.opNrPlantao = ?
        `;

        const response = await dbQuery(query, [
            event.psCPF,
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
        ]);

        return baseResponse.ok('Listado com sucesso', response);
    } catch (error) {
        console.log('ERROR _listChecks', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _listSolicitacoes(event) {
    try {
        const cpf = event?.body?.user;

        if (!cpf) {
            return baseResponse.notFound('Usuário não encontrado.');
        }

        const query = /*sql*/`
            SELECT * FROM capInstSaudePlantaoSolicitacao cisps
            WHERE psCPF = ?
            order by psDataSolicitacao desc;
        `;

        const response = await dbQuery(query, [cpf]);

        return baseResponse.ok('', response);
    } catch (error) {
        console.error('ERROR _listSolicitacoes', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _agenda(event) {
    try {
        const cpf = event?.body?.user;

        if (!cpf) {
            return baseResponse.notFound('Usuário não encontrado.');
        }

        if (
            checkData(event, {
                obrigatorios: ['inicio', 'fim'],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        let query = '';
        const params = [event.inicio, event.fim];

        let conditions = /*sql*/ `AND cisp.opSituacao IN ('AguardExecucao' , 'EmExecucao', 'Concluido') AND cisp.psCPF = '${cpf}'`;

        // NOTE: Se necessario filtrar por cliente um dia, apenas descomente e envie um array de clCliente no body
        //
        // if (event.clClientes?.length > 0) {
        //     const clClientes = event.clClientes.join(',');
        //     conditions = `${conditions} AND p.clCliente in (${clClientes})`
        // }

        query = /*sql*/ `
            SELECT 
                cispa.*,
                cisp.opSituacao,
                cispa.agDataIni,
                cispa.agDataFim,
                cispa.agDataIni as dtperiodoIni,
                cispa.agDataFim as dtPeriodoFim,
                cispa.agHoraIni,
                cispa.agHoraFim,
                cisp.psCPF,
                cu.usNome,
                cc.clNomeCliente
            FROM
                capInstSaudePlantaoAgenda as cispa
            LEFT JOIN
                capInstSaudePlantao as cisp ON cisp.opNrPlantao = cispa.opNrPlantao
            INNER JOIN
                capUsuario as cu
            ON
                cu.usCPFUsuario = cisp.psCPF
            INNER JOIN
                capCliente as cc
            ON
                cc.clCliente = cispa.clCliente
            WHERE
                cispa.agData BETWEEN ? AND ?
            AND
                cispa.opNrPlantao IS NOT NULL
            AND
                cispa.agAtivo = 1
            ${conditions}
            ORDER BY
                cispa.agData , cispa.agHoraIni;
        `;

        let agendas = await dbQuery(query, params);

        agendas = await Promise.all(
            agendas.map(async (agenda) => {
                agenda.dtNomedia = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sab'][
                    agenda.agDiaSem
                ];

                agenda.checks = await dbQuery(
                    /*sql*/ `
                        SELECT p.*,
                            ocQtAprovadas as hrsTrabalhadas
                        FROM 
                            capOperPlantaoCheck  p,
                            capInstSaudePlantao sp
                        WHERE sp.psCPF = ?
                        AND p.laNome = sp.laNome
                        AND p.esEspecialidade = sp.esEspecialidade
                        AND p.ocNrContrato = sp.ocNrContrato
                        AND p.clCliente = sp.clCliente
                        AND p.opNrPlantao = sp.opNrPlantao
            
                        AND p.isInstSaude = ?
                        AND p.laNome = ?
                        AND p.esEspecialidade = ?
                        AND p.ocNrContrato = ?
                        AND p.clCliente = ?
                        AND p.opNrPlantao = ?
                    `,
                    [
                        agenda.psCPF,
                        agenda.isInstSaude,
                        agenda.laNome,
                        agenda.esEspecialidade,
                        agenda.ocNrContrato,
                        agenda.clCliente,
                        agenda.opNrPlantao,
                    ]
                );

                return agenda;
            })
        );

        const objetoTransformado = {};

        agendas?.forEach((item) => {
            const { agData } = item;

            if (!objetoTransformado[agData]) {
                objetoTransformado[agData] = [];
            }

            objetoTransformado[agData].push(item);
        });

        return baseResponse.ok('listado com sucesso.', objetoTransformado);
    } catch (error) {
        console.log('ERROR _agenda', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}

export {
    _list,
    _solicitarAtendimento,
    _find,
    _listChecks,
    _realizarCheck,
    _listSolicitacoes,
    _agenda,
};
