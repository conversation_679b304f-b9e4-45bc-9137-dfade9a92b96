import {
  checkData,
  deleteEntity,
  execQuery,
  insert,
  update,
  find,
  baseResponse,
}
from "capfunctions";

const mainTable = "brcPerfilMenu";

async function _list(event) {
  try {

    const query = `SELECT 
                    a.pmNome as nome, 
                    a.pmNomeTela as nomeTela 
                  FROM ${mainTable} a 
                  WHERE pmNome like ?
                `;

    const params = ["%" + (event['nome'] || "") + "%"];
    const response = await execQuery(query, params);

    if (response.success)
      return baseResponse.ok('Listado com sucesso', response.results)

    return baseResponse.error("Erro ao realizar listagem");
  }
  catch (error) {
    console.log("ERROR _list", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _find(event) {
  try {
    const findParam = {
      select: { "nome": "pmNome", "nomeTela": "pmNomeTela" },
      where: { "nome": "pmNome" }
    }

    if (checkData(event, Object.keys(findParam.where)))
      return baseResponse.error("Dados insuficentes");

    const query = `SELECT 
                  a.pmNome as nome, 
                  a.pmNomeTela as nomeTela 
                FROM ${mainTable} a 
                WHERE pmNome = ?
              `;

    const params = [(event['nome'] || "")];
    const response = await execQuery(query, params);

    if (response && response.results && response.results.length > 0)
      return baseResponse.ok('', response.results && response.results[0])

    return baseResponse.error("Erro ao realizar busca");
  }
  catch (error) {
    console.log("ERROR _find", error);
    return baseResponse.error("Erro ao processar requisição");
  }

}

async function _insert(event) {
  try {
    if (checkData(event, ["nome", "nomeTela"]))
      return baseResponse.error("Dados insuficentes");

    const entity = {
      pmNome: event.nome,
      pmNomeTela: event.nomeTela,
    }

    const response = await insert(mainTable, entity, event);
    if (response.success)
      return baseResponse.created('Criado com sucesso', response.results)

    return baseResponse.error("Erro ao criar perfil menu");
  }
  catch (error) {
    console.log("ERROR _inser", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _update(event) {
  try {
    if (checkData(event, ["nome"]))
      return baseResponse.error("Dados insuficentes");

    const body = { "nomeTela": "pmNomeTela" };

    const entity = {}
    Object.keys(body).forEach(v => entity[body[v]] = event[v])
    const response = await update(mainTable, entity, { "pmNome": event.nome }, event)

    if (response.success)
      return baseResponse.ok('Atualizado com sucesso', response.results)

    return baseResponse.error("Erro ao atualizar");
  }
  catch (error) {
    console.log("ERROR _update", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _delete(event) {
  try {
    if (checkData(event, ["nome"]))
      return baseResponse.error("Dados insuficentes");

    const response = await deleteEntity(mainTable, { "pmNome": event.nome }, event)

    if (response.success)
      return baseResponse.ok('Removido com sucesso', response.results)

    return baseResponse.error("Erro ao remover");
  }
  catch (error) {
    console.log("ERROR _delete", error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

export { _insert, _update, _list, _find, _delete };
