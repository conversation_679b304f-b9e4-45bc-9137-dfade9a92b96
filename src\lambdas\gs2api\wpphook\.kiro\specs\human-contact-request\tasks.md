# Implementation Plan

- [x] 1. Add new states to StateMachine constants

  - Add HUMAN_CONTACT_REQUEST, HUMAN_CONTACT_NAME, and HUMAN_CONTACT_EMAIL to STATES object
  - Update state transition validation rules in isValidTransition method
  - _Requirements: 5.1, 5.2_

- [x] 2. Create database schema and methods for human contact requests

  - [x] 2.1 Add database table creation SQL for human_contact_requests

    - Create migration script or add to existing schema.sql
    - Include all required fields: id, session_uuid, phone_number, full_name, email, reason, status, timestamps
    - Add appropriate indexes for performance
    - _Requirements: 2.1, 2.2, 2.3_

  - [x] 2.2 Implement database methods in Database class

    - Add saveHumanContactRequest method to save contact request data
    - Add getHumanContactRequest method to retrieve requests by session
    - Add updateHumanContactRequestStatus method for status updates
    - Include proper error handling and retry logic
    - _Requirements: 2.1, 2.2, 2.3_

- [x] 3. Enhance MessageBuilder with new message methods

  - [x] 3.1 Replace buildClinicNotFound with buildClinicNotFoundWithOptions

    - Create interactive button message with three options: "Tentar novamente", "Solicitar contato humano", "Sair"
    - Maintain same error message text but add button options
    - _Requirements: 1.1_

  - [x] 3.2 Add human contact flow messages

    - Implement buildHumanContactRequest method for initial contact request confirmation
    - Implement buildHumanContactNameRequest method to ask for name
    - Implement buildHumanContactEmailRequest method to ask for email
    - Implement buildHumanContactConfirmation method for final confirmation
    - _Requirements: 1.2, 3.1, 3.2_

- [x] 4. Implement new state handlers in StateMachine

  - [x] 4.1 Create handleHumanContactRequestState method

    - Process user confirmation to proceed with contact request
    - Transition to HUMAN_CONTACT_NAME state on confirmation
    - Handle exit command to END state
    - _Requirements: 1.2, 5.3_

  - [x] 4.2 Create handleHumanContactNameState method

    - Validate name input using existing Validators.validateName
    - Store name in session data and transition to HUMAN_CONTACT_EMAIL
    - Show error message and stay in same state for invalid input
    - _Requirements: 1.3, 4.1, 4.4_

  - [x] 4.3 Create handleHumanContactEmailState method

    - Validate email input using existing Validators.validateEmail
    - Save complete contact request to database using new database methods
    - Send confirmation message and transition to END state
    - Handle validation errors by staying in same state
    - _Requirements: 1.4, 1.5, 2.1, 4.2, 4.4_

- [x] 5. Update existing state handler for clinic request

  - Modify handleClinicRequestState to use new buildClinicNotFoundWithOptions message
  - Add transition logic to HUMAN_CONTACT_REQUEST state when user selects contact option
  - Maintain existing behavior for "try again" and "exit" options
  - _Requirements: 1.1_

- [x] 6. Add state transition validation

  - Update isValidTransition method to include new state transitions
  - Add validation for CLINIC_REQUEST -> HUMAN_CONTACT_REQUEST transition
  - Add validation for HUMAN_CONTACT_REQUEST -> HUMAN_CONTACT_NAME transition
  - Add validation for HUMAN_CONTACT_NAME -> HUMAN_CONTACT_EMAIL transition
  - Add validation for HUMAN_CONTACT_EMAIL -> END transition
  - _Requirements: 5.2_

- [x] 7. Create unit tests for new functionality

  - [x] 7.1 Test new MessageBuilder methods

    - Test buildClinicNotFoundWithOptions returns correct interactive message structure
    - Test all human contact flow messages have proper format
    - Verify button IDs and titles are correctly set
    - _Requirements: 1.1, 3.1_

  - [x] 7.2 Test new StateMachine state handlers

    - Test handleHumanContactRequestState with valid and invalid inputs
    - Test handleHumanContactNameState with valid and invalid names
    - Test handleHumanContactEmailState with valid and invalid emails
    - Verify proper state transitions and session data updates
    - _Requirements: 1.2, 1.3, 1.4, 4.1, 4.2_

  - [x] 7.3 Test database operations

    - Test saveHumanContactRequest with valid data
    - Test error handling for database failures
    - Test getHumanContactRequest retrieval functionality
    - _Requirements: 2.1, 2.2_

- [x] 8. Create integration tests for complete flow


  - Test end-to-end flow from clinic not found to contact request completion
  - Verify database persistence of contact request data
  - Test error scenarios and recovery
  - Validate session state management throughout the flow
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5, 2.1, 3.1, 3.2_
