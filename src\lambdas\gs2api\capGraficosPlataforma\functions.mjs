import {
    baseResponse,
    execQuery,
    checkData,
    dbQuery
} from 'capfunctions'

async function _opDadosCompletos(event) {
    const atendimentosSituacao = await _atendimentosSituacao(event.body.user)
    const atendimentosMensal = await _atendimentosMensal(event.body.user)

    return baseResponse.ok("Dados listados com sucesso!", { atendimentosSituacao, atendimentosMensal });
}

async function _clDadosCompletos(event) {
    if (
        checkData(event, { obrigatorios: ['clCliente'] })
    ) {
        return baseResponse.badRequest('clCliente não enviado.');
    }

    const usuariosCliente = await _usuariosCliente(event.clCliente);
    const contratosInstSaudeCliente = await _contratosInstSaudeCliente(event.clCliente)
    const contratosEspecialidadesCliente = await _contratosEspecialidadesCliente(event.clCliente)
    const contratos = await _contratos(event.clCliente)
    const atendimentoSituacaoCliente = await _atendimentoSituacaoCliente(event.clCliente)


    return baseResponse.ok("Dados listados com sucesso!", { usuariosCliente, contratosInstSaudeCliente, contratosEspecialidadesCliente, contratos, atendimentoSituacaoCliente });
}

async function _gcDadosCompletos(event) {

    const [
        taxaDesagio,
        qntProfSaude,
        contratos,
        volumeFinanceiroTotal,
        atendimentos,
        qntUsuarios,
        volumeFinanceiroMensal,
        clientes
    ] = await Promise.all([
        _taxaDesagio(event),
        _qntProfSaude(event),
        _contratosGestorCapitale(event),
        _volumeFinanceiro(event),
        _atendimentosGestor(event),
        _qntUsuario(event),
        _volumeFinanceiro(event),
        _qntClientes(event)
    ]);

    return baseResponse.ok("Dados listados com sucesso!", {
        taxaDesagio,
        qntProfSaude,
        qntUsuarios,
        atendimentos,
        contratos,
        clientes,
        volumeFinanceiroTotal,
        volumeFinanceiroMensal
    })

}

//TODO REMOVER
async function _gcDadosMensais(event) {
    const [
        volumeFinanceiroMensal
    ] = await Promise.all([
        _volumeFinanceiro(event),
    ])

    return baseResponse.ok("Dados listados com sucesso!", {
        volumeFinanceiroMensal
    })
}


async function _qntClientes(event) {
    const conditions = queryWhereMesClientes({ event, colunaMes: 'dtInclusao', tabelaCliente: 'clCliente' })
    const query = `SELECT count(*) as quantidade FROM capCliente ${conditions}`
    const response = await execQuery(query);
    return response.results[0]?.quantidade || [];

}

async function _atendimentosGestor(event) {
    let conditions = queryWhereMesClientes({ event, colunaMes: 'cisp.dtInclusao', tabelaCliente: 'cisp.clCliente' })

    const query = `SELECT 
                    opSituacao,
                    COUNT(*) AS quantidade,
                    cc.clCliente,
                    cc.clNomeCliente,
                    YEAR(cisp.dtInclusao) AS ano,
                    MONTH(cisp.dtInclusao) AS mes
                FROM
                    capInstSaudePlantao cisp
                        INNER JOIN
                    capCliente cc ON cc.clCliente = cisp.clCliente
                ${conditions}
                GROUP BY opSituacao , clCliente , YEAR(cisp.dtInclusao) , MONTH(cisp.dtInclusao)`

    const response = await execQuery(query);
    const transformedData = {
        total: 0,
        totalAguardandoExecucao: 0,
        totalAberto: 0,
        totalSolicitado: 0,
        totalEmExecucao: 0,
        totalAntecipado: 0,
        totalConcluido: 0,
        clientes: {}
    };

    const tagsSituacoes = {
        "AguardExecucao": "totalAguardandoExecucao",
        "Aberto": "totalAberto",
        "Solicitado": "totalSolicitado",
        "EmExecucao": "totalEmExecucao",
        "Antecipado": "totalAntecipado",
        "Concluido": "totalConcluido"
    };

    // Processa cada resultado
    response.results?.forEach(result => {
        const { opSituacao, quantidade, clNomeCliente, ano, mes } = result;

        // Atualiza os totais gerais
        if (tagsSituacoes[opSituacao]) {
            transformedData.total += quantidade;
            if (tagsSituacoes[opSituacao]) {
                transformedData[tagsSituacoes[opSituacao]] += quantidade;
            }

            // Verifica se o cliente já existe na estrutura
            if (!transformedData.clientes[clNomeCliente]) {
                transformedData.clientes[clNomeCliente] = { total: 0, ano: {} };
            }

            // Atualiza total do cliente
            transformedData.clientes[clNomeCliente].total += quantidade;

            // Verifica se o ano já existe para o cliente
            if (!transformedData.clientes[clNomeCliente].ano[ano]) {
                transformedData.clientes[clNomeCliente].ano[ano] = {};
            }

            // Verifica se o mês já existe para o ano do cliente
            if (!transformedData.clientes[clNomeCliente].ano[ano][mes]) {
                transformedData.clientes[clNomeCliente].ano[ano][mes] = {
                    total: 0,
                    AguardExecucao: 0,
                    Aberto: 0,
                    Solicitado: 0,
                    EmExecucao: 0,
                    Antecipado: 0,
                    Concluido: 0
                };
            }

            // Atualiza os totais do mês
            const mesData = transformedData.clientes[clNomeCliente].ano[ano][mes];
            mesData.total += quantidade;
            if (mesData[opSituacao] !== undefined) {
                mesData[opSituacao] += quantidade;
            }
        }
    });

    return transformedData
}


async function _volumeFinanceiro(event) {

    let conditions = ``
    if (event.meses?.length) {

        const meses = event.meses.map(filtro => {
            return `(YEAR(afFim) = ${filtro.ano} AND MONTH(afFim) = ${filtro.mes})`;
        }).join(' OR ');

        conditions = /*sql*/`AND (${meses})`
    }

    if (event.clientes?.length) {
        const clientesCondition = event.clientes.map(cliente => `'${cliente}'`).join(',');

        conditions = conditions.length ? `${conditions} AND caf.clCliente IN (${clientesCondition})` :
            `AND caf.clCliente IN (${clientesCondition})`;

    }

    const query = /*sql*/`
        SELECT 
            ROUND(SUM(caf.afValor), 2) AS bruto,
            YEAR(afFim) AS ano,
            MONTH(afFim) AS mes,
            'aguardandoAprovacao' as adSituacao,
            caf.clCliente AS clCliente,
            COUNT(*) AS qnt,
            caf.anNrAntecipacao AS anNrAntecipacao,
            cc.clNomeCliente AS nomeCliente
        FROM capAtendimentoFechamento caf
        INNER JOIN capCliente cc ON cc.clCliente = caf.clCliente
        WHERE caf.adSituacao = 'aprovado'
            ${conditions}
        GROUP BY mes, ano, adSituacao, clCliente

        UNION ALL

        SELECT 
            ROUND(SUM(a.anValorBruto), 2) AS bruto,
            YEAR(caf.afFim) AS ano,
            MONTH(caf.afFim) AS mes,
            caf.adSituacao,
            caf.clCliente AS clCliente,
            COUNT(*) AS qnt,
            a.anNrAntecipacao AS anNrAntecipacao,
            c.clNomeCliente AS nomeCliente
        FROM capAntecipacao a
        INNER JOIN capAtendimentoFechamento caf ON caf.anNrAntecipacao = a.anNrAntecipacao
        INNER JOIN capCliente c ON c.clCliente = caf.clCliente
        WHERE caf.adSituacao IN ('antecipacaoSolicitada', 'antecipado')
            ${conditions}
        GROUP BY mes, ano, adSituacao, clCliente;
    `;

    const response = await dbQuery(query);

    if (response.length > 0) {
        const result = {
            total: 0,
            totalAguardandoAprovacao: 0,
            totalAntecipacaoSolicitada: 0,
            totalAntecipado: 0,
            clientes: {},
        };

        response.forEach((item) => {
            result.total += item.bruto;

            if (item.adSituacao === "aguardandoAprovacao") {
                result.totalAguardandoAprovacao += item.bruto;
            }
            else if (item.adSituacao === "antecipacaoSolicitada") {
                result.totalAntecipacaoSolicitada += item.bruto;
            }
            else if (item.adSituacao === "antecipado") {
                result.totalAntecipado += item.bruto;
            }

            if (!result.clientes[item.nomeCliente]) {
                result.clientes[item.nomeCliente] = {
                    total: 0,
                    totalAguardandoAprovacao: 0,
                    totalAntecipacaoSolicitada: 0,
                    totalAntecipado: 0,
                    ano: {},
                };
            }

            result.clientes[item.nomeCliente].total += item.bruto;

            if (item.adSituacao === "aguardandoAprovacao") {
                result.clientes[item.nomeCliente].totalAguardandoAprovacao += item.bruto;
            }
            else if (item.adSituacao === "antecipacaoSolicitada") {
                result.clientes[item.nomeCliente].totalAntecipacaoSolicitada += item.bruto;
            }
            else if (item.adSituacao === "antecipado") {
                result.clientes[item.nomeCliente].totalAntecipado += item.bruto;
            }

            if (!result.clientes[item.nomeCliente].ano[item.ano]) {
                result.clientes[item.nomeCliente].ano[item.ano] = {};
            }
            if (!result.clientes[item.nomeCliente].ano[item.ano][item.mes]) {
                result.clientes[item.nomeCliente].ano[item.ano][item.mes] = {};
            }
            if (!result.clientes[item.nomeCliente].ano[item.ano][item.mes][item.adSituacao]) {
                result.clientes[item.nomeCliente].ano[item.ano][item.mes][item.adSituacao] = {
                    liquido: 0,
                    bruto: 0,
                };
            }

            result.clientes[item.nomeCliente].ano[item.ano][item.mes][item.adSituacao].liquido += item.liquido;
            result.clientes[item.nomeCliente].ano[item.ano][item.mes][item.adSituacao].bruto += item.bruto;
        });


        return result;
    }

    return []
}

async function _taxaDesagio(event) {
    let conditions = queryWhereMesClientes({ event, colunaMes: 'ocDataContrato', tabelaCliente: 'cisce.clCliente' })


    const query = `SELECT 
                    COALESCE(AVG(ceTaxaDesagio), 0) AS media,
                    COALESCE(MAX(ceTaxaDesagio), 0) AS maior_valor,
                    COALESCE(MIN(ceTaxaDesagio), 0) AS menor_valor,
                    ocDataContrato
                FROM
                    capInstSaudeContratoEspec cisce
                        INNER JOIN
                    capInstSaudeContrato cisc ON (cisce.isInstSaude = cisc.isInstSaude
                        AND cisce.ocNrContrato = cisc.ocNrContrato
                        AND cisce.clCliente = cisc.clCliente)
                    ${conditions};`

    const response = await execQuery(query);
    return response.results[0] || {};
}

async function _qntUsuario(event) {

    const conditions = queryWhereMesClientes({ event, colunaMes: 'dtInclusao', tabelaCliente: 'clCliente' })

    let query = `SELECT count(*) as quantidade FROM capClienteUsuario ${conditions};`

    const response = await execQuery(query);
    return response.results[0]?.quantidade || 0;
}

async function _qntProfSaude(event) {

    const conditions = queryWhereMesClientes({ event, colunaMes: 'dtInclusao', tabelaCliente: 'clCliente' })

    let query = `SELECT count(*) as quantidade FROM capProfSaudeCliente ${conditions};`

    const response = await execQuery(query);
    return response.results[0]?.quantidade || 0;
}

async function _contratosGestorCapitale(event) {
    let conditions = queryWhereMesClientes({ event, colunaMes: 'ocDataContrato', tabelaCliente: 'cisc.clCliente' })

    const query = `SELECT 
                    COUNT(*) AS quantidade,
                    cisc.clCliente,
                    YEAR(ocDataContrato) AS ano,
                    MONTH(ocDataContrato) AS mes,
                    cc.clNomeCliente
                FROM
                    capInstSaudeContrato cisc
                INNER JOIN capCliente cc on (cc.clCliente = cisc.clCliente)
                ${conditions}
                GROUP BY cisc.clCliente , YEAR(ocDataContrato) , MONTH(ocDataContrato)`

    const response = await execQuery(query);


    const transformedData = {
        total: 0,
        mediaPorCliente: 0,
        maior: 0,
        menor: Infinity,
        clientes: {}
    };

    let totalQuantidade = 0;
    let totalClientes = 0;

    response.results.forEach(result => {
        const { quantidade, clCliente, ano, mes, clNomeCliente } = result;

        totalQuantidade += quantidade;

        if (!transformedData.clientes[clNomeCliente]) {
            transformedData.clientes[clNomeCliente] = { total: 0, ano: {} };
            totalClientes++;
        }

        transformedData.clientes[clNomeCliente].total += quantidade;

        transformedData.maior = Math.max(transformedData.maior, transformedData.clientes[clNomeCliente].total);
        transformedData.menor = Math.min(transformedData.menor, transformedData.clientes[clNomeCliente].total);

        if (!transformedData.clientes[clNomeCliente].ano[ano]) {
            transformedData.clientes[clNomeCliente].ano[ano] = {};
        }


        if (!transformedData.clientes[clNomeCliente].ano[ano][mes]) {
            transformedData.clientes[clNomeCliente].ano[ano][mes] = { total: 0 };
        }

        transformedData.clientes[clNomeCliente].ano[ano][mes].total += quantidade;
    });

    transformedData.total = totalQuantidade;
    transformedData.mediaPorCliente = totalQuantidade / totalClientes;

    return transformedData;
}

// OLD --------------------- TODO REMOVER QUANDO MEXER NO GRAFICO DO CLIENTE
async function _usuariosCliente(clCliente) {
    const conditions = queryWhereMesClientes({ event, colunaMes: 'dtInclusao', tabelaCliente: 'clCliente' })

    let query = `SELECT count(*) as quantidade FROM capClienteUsuario ${conditions};`

    const response = await execQuery(query);
    return response.results[0]?.quantidade || 0;
}

async function _contratosInstSaudeCliente(clCliente) {
    const query = `SELECT count(*) as quantidade FROM capInstSaudeContrato where clCliente= ? group by clCliente `
    const response = await execQuery(query, [clCliente]);
    return response.results || [];
}

async function _contratosEspecialidadesCliente(clCliente) {
    const query = `SELECT esEspecialidade, count(*) as quantidade FROM capInstSaudeContratoEspec where clCliente = ?  group by esEspecialidade; `
    const response = await execQuery(query, [clCliente]);
    return response.results || [];
}

async function _contratos(clCliente) {
    const query = `SELECT count(*) as quantidade FROM capInstSaudeContrato where clCliente= ? group by clCliente;`
    const response = await execQuery(query, [clCliente]);
    return response.results || [];
}

async function _atendimentoSituacaoCliente(clCliente) {
    const query = `SELECT opSituacao, count(*) as quantidade FROM capInstSaudePlantao where clCliente = ? group by opSituacao;`
    const response = await execQuery(query, [clCliente]);
    return response.results || [];
}

async function _atendimentosSituacao(cpf) {
    const query = `SELECT opSituacao, COUNT(*) AS quantidade
                   FROM capInstSaudePlantao
                   WHERE psCpf = ?
                   AND opPeriodoIni >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                   GROUP BY opSituacao;`

    const response = await execQuery(query, [cpf])

    return response.results || []

}

async function _atendimentosMensal(cpf) {
    const query = `SELECT MONTH(opPeriodoIni) AS mes, COUNT(*) AS quantidade
                   FROM capInstSaudePlantao
                   WHERE psCpf = ?
                   AND opPeriodoIni >= DATE_SUB(CURDATE(), INTERVAL 3 MONTH)
                   GROUP BY MONTH(opPeriodoIni);`

    const response = await execQuery(query, [cpf])

    return response.results || []

}

function queryWhereMesClientes({ event, colunaMes, tabelaCliente }) {
    let conditions = ``
    if (event.meses?.length) {

        const meses = event.meses.map(filtro => {
            return `(YEAR(${colunaMes}) = ${filtro.ano} AND MONTH(${colunaMes}) = ${filtro.mes})`;
        }).join(' OR ');

        conditions = `WHERE (${meses})`
    }

    if (event.clientes?.length) {
        const clientesCondition = event.clientes.map(cliente => `'${cliente}'`).join(',');

        conditions = conditions.length ?
            `${conditions} AND ${tabelaCliente} IN (${clientesCondition})` :
            `WHERE ${tabelaCliente} IN (${clientesCondition})`;

    }

    return conditions;
}

export { _opDadosCompletos, _clDadosCompletos, _gcDadosCompletos, _gcDadosMensais }
