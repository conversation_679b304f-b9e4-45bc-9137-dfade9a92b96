import { baseResponse } from 'capfunctions';
import { capitalService } from './functions.mjs';

const actions = {
    _cadastrarPessoaFisica: capitalService.cadastrarPessoaFisica,
    _cadastrarPessoaJuridica: capitalService.cadastrarPessoaJuridica,
    _solicitaAntecipacao: capitalService.solicitaAntecipacao,
};

export const handler = async (event) => {
    console.log(event);
    const method = event?.body?.method || event?.method;

    try {
        return await actions[method](event.body || event);
    } catch (error) {
        console.log(method, 'Erro ao processar a requisição', error);

        return baseResponse.error(
            'Erro ao processar a requisição. Caso persista, entre em contato com o suporte.'
        );
    }
};
