import { LambdaClient, InvokeCommand } from '@aws-sdk/client-lambda';
import { VERIFY_TOKEN } from 'capcorpconf';

/*
    Base URL:
    https://ahq5qmwcyl.execute-api.us-east-2.amazonaws.com/prod/capWebhookWhatsapp
*/

export const handler = async (event) => {
    try {
        console.log('handler()', event);

        const { httpMethod, queryStringParameters, body } = event;

        if (httpMethod === 'GET') {
            return verifyWebhook(queryStringParameters);
        }

        if (httpMethod === 'POST') {
            if (typeof body === 'string') {
                return receiveMessage(JSON.parse(body));
            }
        }

        return {
            statusCode: 404,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ error: 'Rota não encontrada.' }),
        };
    } catch (error) {
        console.error('Erro no handler()', error);

        return {
            statusCode: 500,
            body: JSON.stringify({ error: 'Erro' }),
        };
    }
};

const verifyWebhook = (queryParams) => {
    const mode = queryParams?.['hub.mode'];
    const token = queryParams?.['hub.verify_token'];
    const challenge = queryParams?.['hub.challenge'];

    if (!mode || token !== VERIFY_TOKEN) {
        return {
            statusCode: 403,
            body: JSON.stringify({ error: 'Falha na verificação' }),
        };
    }

    return {
        headers: {
            'Content-Type': 'text/plain',
        },
        statusCode: 200,
        body: challenge,
    };
};

const receiveMessage = async (body) => {
    console.log('receiveMessage()', JSON.stringify(body));
    if (body.entry) {
        for (const entry of body.entry) {
            for (const change of entry.changes) {
                if (change.value.messages) {
                    const message = change.value.messages[0];
                    const chmens = await chamaAnaliseMens(message);
                }
            }
        }

        return { statusCode: 200, body: 'EVENT_RECEIVED' };
    }

    return {
        statusCode: 404,
        body: JSON.stringify({ error: 'Nenhuma mensagem recebida' }),
    };
};

const chamaAnaliseMens = async (message) => {
    try {
        console.log('chamaAnaliseMens()', message);
        const params = {
            FunctionName: 'capAnalisaMens',
            Payload: Buffer.from(JSON.stringify(message)),
        };

        const lambdaClient = new LambdaClient({ region: 'us-east-2' });
        const command = new InvokeCommand(params);
        const response = await lambdaClient.send(command);

        const responsePayload = JSON.parse(Buffer.from(response.Payload));
        console.log('Response.responsePayload: ', responsePayload);

        return {
            statusCode: 200,
            body: JSON.stringify({
                message: 'Lambda A invocada com sucesso!',
                response: responsePayload,
            }),
        };
    } catch (error) {
        console.error('Erro ao invocar a capAnalisaMens:', error);
        return {
            statusCode: 500,
            body: JSON.stringify({ error: 'Falha na invocação da capAnalisaMens' }),
        };
    }
};
