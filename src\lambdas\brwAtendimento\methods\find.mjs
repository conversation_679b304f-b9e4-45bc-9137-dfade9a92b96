import { checkData, baseResponse, dbQuery } from 'capfunctions';

const REQUIRED_FIELDS = [
    'clCliente',
    'ocNrContrato',
    'isInstSaude',
    'laNome',
    'esEspecialidade',
    'opNrPlantao',
];

const PLANTAO_QUERY = /*sql*/ `
    SELECT
        cisp.*,
        cisp.clCliente, cisp.ocNrContrato, cisp.isInstSaude, cisp.laNome, cisp.esEspecialidade, cisp.opNrPlantao, usCPFUsuarioAprovador, 
        opDataDivulgacao, opAtivo, opDataFechamento, opPeriodoIni, opPeriodoFim, opQtdHorasRequisitada, 
        psCPF, opQtdHorasRealizadas, opChaveAcesso, opSituacao, opValorFixo, opValorHora, opValorUnit, opDiaFechamento,
        opTipoFechamento, cisp.ceTipoPagamento, cu.usCPFUsuario as psCPF, cu.usNome as psNome, cis.isNome,
        MAX(cisa.agDataFim) OVER (
            PARTITION BY 
              cisa.isInstSaude,
              cisa.ceTipoPagamento,
              cisa.clCliente,
              cisa.opNrPlantao,
              cisa.ocNrContrato,
              cisa.laNome
       ) AS agDataFim
    FROM capInstSaudePlantao cisp
    INNER JOIN capInstSaude cis ON cis.isInstSaude = cisp.isInstSaude
    INNER JOIN capInstSaudePlantaoAgenda cisa ON (
        cisa.isInstSaude = cisp.isInstSaude
        AND cisa.ceTipoPagamento = cisp.ceTipoPagamento
        AND cisa.clCliente = cisp.clCliente
        AND cisa.opNrPlantao = cisp.opNrPlantao
        AND cisa.ocNrContrato = cisp.ocNrContrato
        AND cisa.laNome = cisp.laNome 
    )
    LEFT join capUsuario cu on cu.usCPFUsuario = cisp.psCPF
    WHERE cisp.clCliente = ?
    AND cisp.ocNrContrato = ?
    AND cisp.isInstSaude = ?
    AND cisp.laNome = ?
    AND cisp.esEspecialidade = ?
    AND cisp.opNrPlantao = ?
`;

const FECHAMENTO_QUERY = /*sql*/ `
    SELECT MAX(afFim) AS afFimMaisRecente
    FROM capAtendimentoFechamento
    WHERE 
    clCliente = ?
    AND ocNrContrato = ?
    AND isInstSaude = ?
    AND laNome = ?
    AND esEspecialidade = ?
    AND opNrPlantao = ?
`;

const ESPECIALIDADE_QUERY = /*sql*/ `
    SELECT * 
    FROM capInstSaudeContratoEspec
    WHERE 
    clCliente = ?
    AND ocNrContrato = ?
    AND isInstSaude = ?
    AND laNome = ?
    AND esEspecialidade = ?
`;

const HABILITADO_PARA_FECHAMENTO_QUERY = /*sql*/ `
    SELECT * FROM capAtendimentoFechamento f, capOperPlantaoCheck c
    WHERE f.clCliente = ?
    AND f.ocNrContrato = ?
    AND f.isInstSaude = ?
    AND f.laNome = ?
    AND f.esEspecialidade = ?
    AND f.opNrPlantao = ?
    AND f.adSituacao = 'aguardandoAprovacao'
    AND f.clCliente = c.clCliente
    AND f.ocNrContrato = c.ocNrContrato
    AND f.isInstSaude = c.isInstSaude
    AND f.laNome = c.laNome
    AND f.esEspecialidade = c.esEspecialidade
    AND f.opNrPlantao = c.opNrPlantao
    AND c.codFechamento IS NULL
    AND f.afFim < NOW()
    ORDER BY c.ocCheckOut DESC
    LIMIT 1
`;

export async function _find(event) {
    try {
        if (checkData(event, { obrigatorios: REQUIRED_FIELDS })) {
            return baseResponse.error('Dados insuficentes');
        }

        const params = [
            event.clCliente,
            event.ocNrContrato,
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.opNrPlantao,
        ];

        const [plantao] = await dbQuery(PLANTAO_QUERY, params);

        if (!plantao) {
            return baseResponse.notFound('Atendimento não encontrado');
        }

        const agendas = await dbQuery(
            /*sql*/ `
                SELECT * FROM capInstSaudePlantaoAgenda
                WHERE isInstSaude = ? AND laNome = ? AND esEspecialidade = ? AND ocNrContrato = ? AND clCliente = ? AND opNrPlantao = ?;
            `,
            [
                event.isInstSaude,
                event.laNome,
                event.esEspecialidade,
                event.ocNrContrato,
                event.clCliente,
                event.opNrPlantao,
            ]
        );
         

        const [fechamento] = await dbQuery(FECHAMENTO_QUERY, params);
        const [especialidade] = await dbQuery(ESPECIALIDADE_QUERY, params.slice(0, 5));
        const [habilitadoParaFechamento] = await dbQuery(HABILITADO_PARA_FECHAMENTO_QUERY, params);

        const responseBody = {
            ...plantao,
            ...(fechamento && { ultimoFechamento: fechamento.afFimMaisRecente }),
            ...(especialidade && {
                ceValorHoraProf: especialidade.ceValorHoraProf,
                ceQtdDiasMinRecebim: especialidade.ceQtdDiasMinRecebim,
                ceValorFixo: especialidade.ceValorFixo,
                habilitadoParaFechamento:
                    plantao.opTipoFechamento === 'Manual' && !!habilitadoParaFechamento,
            }),
            agendas: agendas.reduce((acc, ag) => {
                return { ...acc, [ag.agData]: ag };
            }, {}),
        };

        return baseResponse.ok('ok', responseBody);
    } catch (error) {
        console.log('ERROR _find', error);

        return baseResponse.error('Erro ao processar requisição para buscar atendimento');
    }
}
