import {
  createResponse,
  checkData,
  deleteEntity,
  execQuery,
  insert,
  update,
  find,
  baseResponse
} from 'capfunctions';

const mainTable = 'brcPerfilMenuRotas';

async function _list(event) {
  try {
    const query = `SELECT
                    b.pmNome as perfil,
                    b.pmNomeTela as perfilTela,
                    c.gmNome as grupo,
                    c.gmDescricao as grupoDescricao,
                    GROUP_CONCAT(CONCAT(d.mrNome,'==',d.mrRota)) as rotas
                  FROM ${mainTable} a
                  LEFT JOIN brcPerfilMenu b on a.pmNome = b.pmNome
                  LEFT JOIN brcGrupoMenu c on a.gmNome = c.gmNome
                  LEFT JOIN brcMenuRotas d on a.mrNome = d.mrNome
                  WHERE a.pmNome like ? and a.gmNome like ?
                  GROUP BY a.pmNome, a.gmNome
                `;

    const params = [
      '%' + (event['nome'] || '') + '%',
      '%' + (event['nome'] || '') + '%',
    ];
    const response = await execQuery(query, params);

    if (response.success) {
      response['results'].map((v) => ({ ...v, rotas: v.rotas.split(',') }));
      return baseResponse.created('Listado com sucesso', response['results'].map((v) => ({
          ...v,
          rotas: v.rotas
            ? v.rotas.split(',').map((v) => ({
                nome: v.split('==')[0],
                rota: v.split('==')[1],
              }))
            : [],
        })));
    }

    return createResponse(500, '', false, null);
  } catch (error) {
    console.log('catch no _list()', error);
    return createResponse(500, 'Erro no servidor', false, null);
  }
}

async function _find(event) {
  if (checkData(event, { obrigatorios: ['perfil'] }))
    return createResponse(500, 'Dados insuficentes.', null, null);

  const query = `SELECT
                    a.pmNome as perfil,
                    a.gmNome as grupo,
                    GROUP_CONCAT(a.mrNome) as rotas
                  FROM ${mainTable} a
                  WHERE a.pmNome = ? and gmNome = ?
                  GROUP BY a.pmNome
                `;

  const params = [event['perfil'], event['grupo']];
  const response = await execQuery(query, params);

  if (response && response.results && response.results.length > 0)
    return createResponse(200, '', true, {
      ...(response.results && response.results[0]),
      rotas: response.results && response.results[0].rotas.split(','),
    });

  return createResponse(404, 'Não encontrado', false, null);
}

async function _insert(event) {
  try {
    if (checkData(event, { obrigatorios: ['perfil', 'rotas'] }))
      return createResponse(500, 'Dados insuficentes.', '', null);

    const existRelationship = await execQuery(
      /*sql*/
      `SELECT * FROM brcGrupoMenuPerfilMenu
        WHERE gmNome = ? AND pmNome = ?`,
      [event['grupo'], event['perfil']]
    );
    if (!existRelationship || !existRelationship.length) {
      await insert('brcGrupoMenuPerfilMenu', {gmNome: event.grupo, pmNome: event.perfil}, event)
    }

    const existEntity = await execQuery(
      `SELECT count(*) cnt FROM brcPerfilMenuRotas a WHERE pmNome = ? and gmNome = ? `,
      [event['perfil'], event['grupo']]
    );
    if (existEntity['results'][0].cnt > 0)
      return createResponse(
        500,
        "Perfil '" +
          event['perfil'] +
          "' com o grupo '" +
          event['grupo'] +
          "' ja existe",
        '',
        null
      );

    let response = { success: true };
    for (const i in event.rotas) {
      const entity = {
        pmNome: event.perfil,
        gmNome: event.grupo,
        mrNome: event.rotas[i],
      };
      const resp = await insert(mainTable, entity, event);
      if (!resp.success) {
        response['success'] = false;
      }
    }

    if (response.success) {
      return createResponse(201, 'Criado com successo', true, response.results);
    }

    return createResponse(500, '', false, null);
  } catch (error) {
    console.log('catch no _insert()', error);
    return createResponse(500, 'Erro no servidor', false, null);
  }
}

async function _update(event) {
  try {
    if (checkData(event, { obrigatorios: ['perfil', 'rotas'] }))
      return createResponse(500, 'Dados insuficentes.', '', null);

    await execQuery(
      `DELETE FROM ${mainTable} WHERE pmNome = ? and gmNome = ? `,
      [event['perfil'], event['grupo']]
    );

    let response = { success: true };
    for (const i in event.rotas) {
      const entity = {
        pmNome: event.perfil,
        gmNome: event.grupo,
        mrNome: event.rotas[i],
      };
      const resp = await insert(mainTable, entity, event);
      if (!resp.success) {
        response['success'] = false;
      }
    }

    if (response.success) {
      return createResponse(201, 'Atualizado.', true, response.results);
    }

    return createResponse(500, '', false, null);
  } catch (error) {
    console.log('catch no _update()', error);
    return createResponse(500, 'Erro no servidor', false, null);
  }
}

async function _delete(event) {
  try {
    if (checkData(event, { obrigatorios: ['nome'] }))
      return createResponse(500, 'Dados insuficentes.', '', null);

    const response = await deleteEntity(mainTable, {
      pmNome: event.perfil,
      gmNome: event.grupo,
    }, event);

    if (response.success)
      return createResponse(201, 'Apagado.', true, response.results);

    return createResponse(500, '', false, null);
  } catch (error) {
    console.log('catch no _delete()', error);
    return createResponse(500, 'Erro no servidor', false, null);
  }
}

export { _insert, _update, _list, _find, _delete };