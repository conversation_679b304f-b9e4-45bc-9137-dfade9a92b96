import {
  checkData,
  deleteEntity,
  execQuery,
  insert,
  update,
  find,
  baseResponse,
} from "capfunctions";

const mainTable = 'brcGrupoMenu';

async function _list(event) {
  try {
    const query = /*sql*/ `
      SELECT
          a.gmNome as nome,
          a.gmDescricao as descricao,
          a.gmTipo as tipo
        FROM brcGrupoMenu a
        LEFT JOIN brcGrupoMenuPerfilMenu b on a.gmNome = b.gmNome
        WHERE a.gmNome like ? ${event['perfilMenu'] ? ' AND b.pmNome = ?' : ''}
    `;

    const params = ['%' + (event['nome'] || '') + '%'];
    if (event['perfilMenu']) {
      params.push(event['perfilMenu']);
    }

    const response = await execQuery(query, params);

    if (response.success) {
      return baseResponse.ok('Listado com sucesso', response.results)
    }

    return baseResponse.error("Erro ao realizar listagem");
  } catch (error) {
    console.log("ERROR _list",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _find(event) {
  try {
    const findParam = {
      select: { nome: 'gmNome', descricao: 'gmDescricao', tipo: 'gmTipo' },
      where: { nome: 'gmNome' },
    };
  
    if (
      checkData(event, {
        obrigatorios: Object.keys(findParam.where),
      })
      ) {
      return baseResponse.error("Dados insuficentes");
    }
  
    const query = /*sql*/ `
      SELECT
        a.gmNome as nome,
        a.gmDescricao as descricao,
        a.gmTipo as tipo
      FROM brcGrupoMenu a
      LEFT JOIN brcGrupoMenuPerfilMenu b on a.gmNome = b.gmNome
      WHERE a.gmNome = ?
    `;
  
    const params = [event['nome'] || ''];
    const response = await execQuery(query, params);
  
    if (response && response.results && response.results.length > 0)
      return baseResponse.ok('', response.results && response.results[0])
    
    return baseResponse.error("Erro ao encontrar grupo menu");
  } catch (error) {
    console.log("ERROR _find",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _insert(event) {
  try {
    if (checkData(event, { obrigatorios: ['nome', 'descricao', 'tipo'] }))
      return baseResponse.error("Dados insuficentes");
    
    const entity = {
      gmNome: event.nome,
      gmDescricao: event.descricao,
      gmTipo: event.tipo,
    };

    const response = await insert(mainTable, entity, event);
    if (response.success)
      return baseResponse.created('Criado com sucesso', response.results)
    
    return baseResponse.error("Erro ao criar");
  } catch (error) {
    console.log("ERROR _insert",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _update(event) {
  try {
    if (checkData(event, { obrigatorios: ['nome'] }))
      return baseResponse.error("Dados insuficentes");
    
    const body = { descricao: 'gmDescricao', tipo: 'gmTipo' };

    const entity = {};
    Object.keys(body).forEach((v) => (entity[body[v]] = event[v]));
    const response = await update(mainTable, entity, { gmNome: event.nome }, event);
    if (response.success)
      return baseResponse.ok('Atualizado com sucesso', response.results)
    
    return baseResponse.error("Erro ao atualizar");
  } catch (error) {
    console.log("ERROR _update",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

async function _delete(event) {
  try {
    if (checkData(event, { obrigatorios: ['nome'] }))
      return baseResponse.error("Dados insuficentes");
    
    const response = await deleteEntity(mainTable, { gmNome: event.nome }, event);

    if (response.success)
      return baseResponse.ok('Removido com sucesso', response.results)
    
    return baseResponse.error("Erro ao remover");
  } catch (error) {
    console.log("ERROR _delete",error);
    return baseResponse.error("Erro ao processar requisição");
  }
}

export { _insert, _update, _list, _find, _delete };
