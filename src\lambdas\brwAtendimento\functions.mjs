import {
    createResponse,
    checkData,
    execQuery,
    baseResponse,
    dbQuery,
    insert,
    update,
    timeToMinutes,
    getNextDay,
} from 'capfunctions';

import { _find } from './methods/find.mjs';
import { _list } from './methods/list.mjs';
import { _concluirAtendimento } from './methods/concluirAtendimento.mjs';
import { _listarAgendaPlantao } from './methods/listarAgendaPlantao.mjs';

async function _insert(event) {
    /*  
        set @numeroPlantao = 51;
        set @numeroAntecipacao = (select anNrAntecipacao from capAditivoProfSaudeAntecipacao WHERE opNrPlantao = @numeroPlantao);

        DELETE FROM capInstSaudePlSolicitAprovado WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capInstSaudePlantaoSolicitacao WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capAtendimentoFechamento WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capAditivoProfSaude WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capOperPlantaoCheck WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capInstSaudePlantaoAgenda WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capInstSaudePlantao WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capAditivoProfSaudeAntecipacao WHERE opNrPlantao = @numeroPlantao;
        DELETE FROM capAntecipacao WHERE anNrAntecipacao = @numeroAntecipacao;
    */

    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'ocNrContrato',
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'opPeriodoIni',
                    'opPeriodoFim',
                    'opQtdHorasRequisitada',
                    'ceTipoPagamento',
                    'opTipoFechamento',
                    'calendario',
                ],
            })
        ) {
            return baseResponse.badRequest('Dados insuficentes.');
        }

        const insertNrPlantao = await insert('capNrPlantao', '', event);
        const selNr = await execQuery(
            'SELECT opNrPlantao FROM capNrPlantao WHERE id = ?',
            [insertNrPlantao.results.insertId]
        );
        const opNrPlantao = selNr.results[0].opNrPlantao;

        const opSituacao = event.psCPF ? 'AguardExecucao' : 'Aberto';
        const calendario = event.calendario;

        const entity = {
            clCliente: event.clCliente,
            ocNrContrato: event.ocNrContrato,
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            usCPFUsuarioAprovador: event.usCPFUsuarioAprovador,
            opDataDivulgacao: event?.opDataDivulgacao,
            opAtivo: 1,
            opDataFechamento: event.opDataFechamento,
            opPeriodoIni: event.opPeriodoIni,
            opPeriodoFim: event.opPeriodoFim,
            opQtdHorasRequisitada: calendario.horasPrevistas.replace(':', '.'),
            opQtdHorasRealizadas: event.opQtdHorasRealizadas,
            opChaveAcesso: event.opChaveAcesso,
            opNrPlantao,
            opSituacao,
            opValorHora: event.opValorHora,
            opValorFixo: event.opValorFixo,
            opValorUnit: event.opValorUnit,
            ceTipoPagamento: event.ceTipoPagamento,
            opTipoFechamento: event.opTipoFechamento,
            opDiaFechamento: event.opDiaFechamento,
            opValorExtra1: event?.valorPlantaoExtra1 || null,
            opValorExtra2: event?.valorPlantaoExtra2 || null,
        };

        if (event?.psCPF) {
            entity.psCPF = event.psCPF;
        }

        const response = await insert('capInstSaudePlantao', entity, event);

        const listaDeInserts = [];
        for (const dataCorrente in calendario.dias) {
            const dia = calendario.dias[dataCorrente];

            const startMinutes = timeToMinutes(dia.inicio);
            const endMinutes = timeToMinutes(dia.fim);
            const isNextDay = endMinutes <= startMinutes;

            listaDeInserts.push(
                insert('capInstSaudePlantaoAgenda', {
                    isInstSaude: event.isInstSaude,
                    laNome: event.laNome,
                    esEspecialidade: event.esEspecialidade,
                    ocNrContrato: event.ocNrContrato,
                    clCliente: event.clCliente,
                    ceTipoPagamento: event.ceTipoPagamento,
                    opNrPlantao,
                    agData: dataCorrente,
                    agDiaSem: dia.diaSemana,
                    agHoraIni: dia.inicio,
                    agHoraFim: dia.fim,
                    agIntervalo: dia.intervalo,
                    agAtivo: dia.ativo ? 1 : 0,

                    agDataIni: `${dataCorrente} ${dia.inicio}`,
                    agDataFim: `${isNextDay ? getNextDay(dataCorrente) : dataCorrente} ${dia.fim}`,

                    agTipoValor: dia.tipoValor,
                })
            );
        }

        await Promise.all(listaDeInserts);

        if (response.success) {
            if (event?.psCPF) {
                const dtAditivo = {
                    isInstSaude: event.isInstSaude,
                    laNome: event.laNome,
                    esEspecialidade: event.esEspecialidade,
                    ocNrContrato: event.ocNrContrato,
                    clCliente: event.clCliente,
                    opNrPlantao: opNrPlantao,
                    psCPF: event.psCPF,
                    usCPFUsuarioAprovador: event.body.user,
                    adValorFixo: event.opValorHora,
                    adValor: event.opValorHora,
                    ceTipoPagamento: event.ceTipoPagamento,
                    adDataSolicitacao: 'current_timestamp',
                };

                const insAditivo = await insert(
                    'capAditivoProfSaude',
                    dtAditivo,
                    event
                );

                if (insAditivo.success) {
                    const dtSol = {
                        isInstSaude: event.isInstSaude,
                        laNome: event.laNome,
                        esEspecialidade: event.esEspecialidade,
                        ocNrContrato: event.ocNrContrato,
                        clCliente: event.clCliente,
                        opNrPlantao,
                        psCPF: event.psCPF,
                        psSituacao: 'Aprovado',
                        ceTipoPagamento: event.ceTipoPagamento,
                    };

                    const insertSol = await insert(
                        'capInstSaudePlantaoSolicitacao',
                        dtSol,
                        event
                    );

                    if (insertSol.success) {
                        const dtAprov = {
                            isInstSaude: event.isInstSaude,
                            laNome: event.laNome,
                            esEspecialidade: event.esEspecialidade,
                            ocNrContrato: event.ocNrContrato,
                            clCliente: event.clCliente,
                            opNrPlantao,
                            psCPF: event.psCPF,
                            psSequenciaSolicitacao: 1,
                            opSituacao: 'Aprovado',
                            ceTipoPagamento: event.ceTipoPagamento,
                            saDataEvento: 'current_timestamp',
                        };
                        const insertAprov = await insert(
                            'capInstSaudePlSolicitAprovado',
                            dtAprov,
                            event
                        );
                        if (!insertAprov) {
                            return baseResponse.badRequest('Nada encontrado');
                        }
                    }
                }
            }
            return baseResponse.created('Criado com successo');
        }

        return baseResponse.notFound('Erro ao cadastrar atendimento');
    } catch (error) {
        console.log('ERROR _insert', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _update(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'clCliente',
                    'ocNrContrato',
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'opDataDivulgacao',
                    'opPeriodoIni',
                    'opPeriodoFim',
                    'opQtdHorasRequisitada',
                    'ceTipoPagamento',
                    'opTipoFechamento',
                    'opDiaFechamento',
                ],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        const entity = {
            clCliente: event.clCliente,
            ocNrContrato: event.ocNrContrato,
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: event.esEspecialidade,
            usCPFUsuarioAprovador: event.usCPFUsuarioAprovador,
            opDataDivulgacao: event.opDataDivulgacao,
            opAtivo: 1,
            opDataFechamento: event.opDataFechamento,
            opPeriodoIni: event.opPeriodoIni,
            opPeriodoFim: event.opPeriodoFim,
            opQtdHorasRequisitada: event.opQtdHorasRequisitada,
            psCPF: event.psCPF,
            opQtdHorasRealizadas: event.opQtdHorasRealizadas,
            opChaveAcesso: event.opChaveAcesso,
            opValorHora: event.opValorHora,
            opValorFixo: event.opValorFixo,
            opValorUnit: event.opValorUnit,
            ceTipoPagamento: event.ceTipoPagamento,
            opTipoFechamento: event.opTipoFechamento,
            opDiaFechamento: event.opDiaFechamento,
        };

        const response = await update(
            'capInstSaudePlantao',
            entity,
            {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
            },
            event
        );

        if (response.success) {
            return createResponse(200, 'Atualizado.', true, response.results);
        }
        console.info(response.success);

        return baseResponse.error('Erro ao atualizar atendimento');
    } catch (error) {
        console.log('ERROR _update', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _aprovaExecucao(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: [
                    'isInstSaude',
                    'laNome',
                    'esEspecialidade',
                    'ocNrContrato',
                    'clCliente',
                    'opNrPlantao',
                    'psCPF',
                ],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        const query = `
          SELECT *
            FROM capOperPlantaoCheck 
            WHERE isInstSaude = ?
            AND laNome = ?
            AND esEspecialidade = ?
            AND ocNrContrato = ?
            AND clCliente = ?
            AND opNrPlantao = ?
            AND psCPF = ?;
          `;

        const paramsQuery = [
            event.isInstSaude,
            event.laNome,
            event.esEspecialidade,
            event.ocNrContrato,
            event.clCliente,
            event.opNrPlantao,
            event.psCPF,
        ];

        const queryHoras = await execQuery(query, paramsQuery);

        let horasTotal = 0;
        let minutosTotal = 0;

        if (queryHoras.success) {
            for (const plantao of queryHoras.results) {
                horasTotal += parseFloat(plantao.ocQtAprovadas.split(':')[0]);
                minutosTotal += parseFloat(plantao.ocQtAprovadas.split(':')[1]);
            }

            horasTotal += Math.floor(minutosTotal / 60);
            minutosTotal = minutosTotal % 60;
        }

        const entity = {
            opQtdHorasRealizadas: `${horasTotal}:${minutosTotal}:00`,
            opSituacao: 'AprovExecucao',
        };

        const response = await update(
            'capInstSaudePlantao',
            entity,
            {
                clCliente: event.clCliente,
                ocNrContrato: event.ocNrContrato,
                isInstSaude: event.isInstSaude,
                laNome: event.laNome,
                esEspecialidade: event.esEspecialidade,
                psCPF: event.psCPF,
                opNrPlantao: event.opNrPlantao,
            },
            event
        );

        if (response.success) {
            return baseResponse.created(
                'Aprovação executada com sucesso',
                response.results
            );
        }
        console.log(response);

        return baseResponse.error('Erro ao aprovar execução atendimento');
    } catch (error) {
        console.log('ERROR _aprovaExecucao', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}

async function _agenda(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: ['inicio', 'fim'],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        let query = '';
        const params = [event.inicio, event.fim];

        let conditions = `AND cisp.opSituacao NOT IN ('Solicitado', 'AguardExecucao')`;
        
        if (event.psCPFs?.length > 0) {
            const psCpfs = event.psCPFs.map(() => '?').join(',');
            conditions = `${conditions} AND cu.usCPFUsuario in (${psCpfs})`;
            params.push(...event.psCPFs);
        }

        if (event.clClientes?.length > 0) {
            const clClientes = event.clClientes.map(() => '?').join(',');
            conditions = `${conditions} AND cispa.clCliente in (${clClientes})`;
            params.push(...event.clClientes);
        }

        query = /*sql*/ `
            SELECT 
                cispa.agDiaSem,
                cispa.agData,
                cispa.isInstSaude,
                cispa.laNome,
                cispa.esEspecialidade,
                cispa.ocNrContrato,
                cispa.clCliente,
                cispa.opNrPlantao,
                cisp.opSituacao,
                cispa.agDataIni,
                cispa.agDataFim,
                cispa.agDataIni as dtperiodoIni,
                cispa.agDataFim as dtPeriodoFim,
                cispa.agHoraIni,
                cispa.agHoraFim,
                cisp.psCPF,
                cu.usNome,
                cispa.agAtivo,
                cispa.ceTipoPagamento,
                cispa.agIntervalo,
                cispa.dtInclusao,
                cispa.dtModificacao,
                cc.clNomeCliente,
                (
                    SELECT 
                        IFNULL(DATE_FORMAT(
                            SEC_TO_TIME(SUM(TIME_TO_SEC(poc.ocQtRealizadas))), 
                            '%H:%i'
                        ), '00:00')
                    FROM capOperPlantaoCheck poc
                    WHERE poc.isInstSaude = cispa.isInstSaude
                    AND poc.laNome = cispa.laNome
                    AND poc.esEspecialidade = cispa.esEspecialidade
                    AND poc.ocNrContrato = cispa.ocNrContrato
                    AND poc.clCliente = cispa.clCliente
                    AND poc.opNrPlantao = cispa.opNrPlantao
                    AND DATE(poc.agData) = DATE(cispa.agData)
                ) as totalHoras
            FROM
                capInstSaudePlantaoAgenda as cispa
            LEFT JOIN
                capInstSaudePlantao as cisp ON cisp.opNrPlantao = cispa.opNrPlantao
            INNER JOIN
                capUsuario as cu
            ON
                cu.usCPFUsuario = cisp.psCPF
            INNER JOIN
                capCliente as cc
            ON
                cc.clCliente = cispa.clCliente
            WHERE
                cispa.agData BETWEEN ? AND ?
            AND
                cispa.opNrPlantao IS NOT NULL
            AND
                cispa.agAtivo = 1
            ${conditions}
            ORDER BY
                cispa.agData , cispa.agHoraIni;
        `;

        let agendas = await dbQuery(query, params);

        agendas = await Promise.all(
            agendas.map(async (agenda) => {
                agenda.dtNomedia = [
                    'Dom',
                    'Seg',
                    'Ter',
                    'Qua',
                    'Qui',
                    'Sex',
                    'Sab',
                ][agenda.agDiaSem];

                agenda.checks = await dbQuery(
                    /*sql*/ `
                        SELECT p.*,
                            ocQtAprovadas as hrsTrabalhadas
                        FROM 
                            capOperPlantaoCheck  p,
                            capInstSaudePlantao sp
                        WHERE sp.psCPF = ?
                        AND p.laNome = sp.laNome
                        AND p.esEspecialidade = sp.esEspecialidade
                        AND p.ocNrContrato = sp.ocNrContrato
                        AND p.clCliente = sp.clCliente
                        AND p.opNrPlantao = sp.opNrPlantao
            
                        AND p.isInstSaude = ?
                        AND p.laNome = ?
                        AND p.esEspecialidade = ?
                        AND p.ocNrContrato = ?
                        AND p.clCliente = ?
                        AND p.opNrPlantao = ?
                        AND DATE(p.agData) = DATE(?)  
                    `,
                    [
                        agenda.psCPF,
                        agenda.isInstSaude,
                        agenda.laNome,
                        agenda.esEspecialidade,
                        agenda.ocNrContrato,
                        agenda.clCliente,
                        agenda.opNrPlantao,
                        agenda.agData,
                    ]
                );

                return agenda;
            })
        );

        const objetoTransformado = {};

        agendas?.forEach((item) => {
            const { agData } = item;

            if (!objetoTransformado[agData]) {
                objetoTransformado[agData] = [];
            }

            objetoTransformado[agData].push(item);
        });

        return baseResponse.ok('listado com sucesso.', objetoTransformado);
    } catch (error) {
        console.log('ERROR _agenda', error);

        return baseResponse.error('Erro ao processar requisição');
    }
}

function somarHoras(hora1, hora2) {
    const [h1, m1] = hora1.split(':').map(Number);
    const [h2, m2] = hora2.split(':').map(Number);

    let totalMinutos = m1 + m2;
    let totalHoras = h1 + h2;

    if (totalMinutos >= 60) {
        totalHoras += Math.floor(totalMinutos / 60);
        totalMinutos = totalMinutos % 60;
    }

    const resultado = `${totalHoras.toString().padStart(2, '0')}:${totalMinutos
        .toString()
        .padStart(2, '0')}`;

    return resultado;
}

function subtrairHoras(hora1, hora2) {
    const [h1, m1] = hora1.split(':').map(Number);
    const [h2, m2] = hora2.split(':').map(Number);

    const totalMinutos1 = h1 * 60 + m1;
    const totalMinutos2 = h2 * 60 + m2;

    let diferencaMinutos = totalMinutos1 - totalMinutos2;

    if (diferencaMinutos < 0) {
        diferencaMinutos += 24 * 60;
    }

    const resultadoHoras = Math.floor(diferencaMinutos / 60);
    const resultadoMinutos = diferencaMinutos % 60;

    const resultado = `${resultadoHoras
        .toString()
        .padStart(2, '0')}:${resultadoMinutos.toString().padStart(2, '0')}`;

    return resultado;
}

/**
 * @param horas {Array<{inicio: string, fim: string, intervalo: string}>} Array de objetos com propriedades inicio e fim
 */
function somarTotalHoras(horas) {
    let horasPrevistas = '';

    horasPrevistas = horas.reduce((acc, hora) => {
        if (hora.inicio && hora.fim && hora.intervalo) {
            if (!acc) {
                return subtrairHoras(
                    subtrairHoras(hora.fim, hora.inicio),
                    hora.intervalo
                );
            }

            return somarHoras(
                acc,
                subtrairHoras(subtrairHoras(hora.fim, hora.inicio), hora.intervalo)
            );
        }

        return acc;
    }, '');

    return horasPrevistas;
}

/**
 * @param hora {Object<{inicio: string, fim: string, intervalo: string}>} Objeto com propriedades inicio e fim
 * @returns {string} Horas totais
 */
function somarTotalHora(hora) {
    return subtrairHoras(subtrairHoras(hora.fim, hora.inicio), hora.intervalo);
}

const _concluir = _concluirAtendimento;

export {
    _insert,
    _update,
    _list,
    _find,
    _aprovaExecucao,
    _concluir,
    _agenda,
    _listarAgendaPlantao,
    somarTotalHora,
    somarTotalHoras,
};
