import { sendTemplateMessage } from 'capcorpconf';
import { dbQuery, insert } from 'capfunctions';

export const handler = async () => {
  const gapEmMinutos = 5;
  try {
    const agendas = await dbQuery(/*sql*/ `
      SELECT
	      *
      FROM
        capInstSaudePlantao p,
        capInstSaudePlantaoAgenda a,
        capProfissionalSaude ps,
        capUsuario u,
        capInstSaudeLocalAtendimento la,
        (
          SELECT
          c.clCliente,
          c.meCodigo,
          c.cmIncidencia,
          m.meModeloMeta,
          m.meFrequencia
          FROM
          capCliMensagem c,
          capMensageria m
          WHERE
          c.meCodigo = m.meCodigo
          AND m.meFrequencia = 'D'
          AND m.meModeloMeta = 'att001'
        ) e
      WHERE
        p.clCliente = e.clCliente
        AND ps.psCPF = p.psCPF
        AND ps.psCPF = u.usCPFUsuario
        AND p.isInstSaude = a.isInstSaude
        AND p.laNome = a.laNome
        AND p.esEspecialidade = a.esEspecialidade
        AND p.ocNrContrato = a.ocNrContrato
        AND p.clCliente = a.clCliente
        AND p.ceTipoPagamento = a.ceTipoPagamento
        AND p.opNrPlantao = a.opNrPlantao
        AND la.isInstSaude = p.isInstSaude
        AND la.laNome = p.laNome
        AND
        

          NOW() BETWEEN
        	TIMESTAMP(concat(a.agData, ' ', a.agHoraIni)) - INTERVAL (e.cmIncidencia * 24) HOUR${
            gapEmMinutos > 0 ? ` - INTERVAL ${gapEmMinutos} MINUTE` : ''
          }
			  AND TIMESTAMP(concat(a.agData, ' ', a.agHoraIni)) - INTERVAL (e.cmIncidencia * 24) HOUR${
          gapEmMinutos > 0 ? ` + INTERVAL ${gapEmMinutos} MINUTE` : ''
        }
        AND NOT EXISTS (
          SELECT 1 FROM capMensPlantaoAgenda cmpa
          WHERE
            cmpa.isInstSaude = p.isInstSaude
            AND cmpa.laNome = p.laNome
            AND cmpa.esEspecialidade = p.esEspecialidade
            AND cmpa.ocNrContrato = p.ocNrContrato
            AND cmpa.clCliente = p.clCliente
            AND cmpa.ceTipoPagamento = p.ceTipoPagamento
            AND cmpa.opNrPlantao = p.opNrPlantao
            AND cmpa.meCodigo = e.meCodigo
            AND cmpa.cmIncidencia = e.cmIncidencia
            AND cmpa.agData = a.agData
            AND cmpa.agHoraIni = a.agHoraIni
            AND cmpa.agHoraFim = a.agHoraFim
        )
    `);

    const data = (date) => {
      const [year, month, day] = date.split('-');

      return `${day}/${month}/${year}`;
    };

    for (const agenda of agendas) {
      const parambody = {
        dr: agenda.usGenero === 'F' ? 'Dra.' : 'Dr.',
        nome: agenda.usNome,
        especialidade: agenda.esEspecialidade,
        data: data(agenda.agData),
        instsaude: agenda.laNome,
        endereco: `${agenda.laEndereco}, ${agenda.laNrEnd} - ${agenda.laBairro}, ${agenda.laCidade} - ${agenda.laUF}`,
      };

      await sendTemplateMessage({
        body: {
          phoneNumber: agenda.usTelefone,
          parambody,
          buttons: [],
          language: 'pt_BR',
          templateName: 'att001',
        },
      });

      await insert('capMensPlantaoAgenda', {
        isInstSaude: agenda.isInstSaude,
        laNome: agenda.laNome,
        esEspecialidade: agenda.esEspecialidade,
        ocNrContrato: agenda.ocNrContrato,
        clCliente: agenda.clCliente,
        ceTipoPagamento: agenda.ceTipoPagamento,
        opNrPlantao: agenda.opNrPlantao,
        meCodigo: agenda.meCodigo,
        cmIncidencia: agenda.cmIncidencia,
        agData: agenda.agData,
        agHoraIni: agenda.agHoraIni,
        agHoraFim: agenda.agHoraFim,
      });
    }
  } catch (error) {
    console.error(error);
  }
};
