import { checkData, baseResponse, dbQuery } from 'capfunctions';

export async function _listFechamentosProfissional(event) {
    try {
        if (
            checkData(event, {
                obrigatorios: ['clCliente'],
            })
        ) {
            return baseResponse.error('Dados insuficentes');
        }

        let params = [event.clCliente];

        let filter = '';

        if (event.opNrPlantao) {
            filter += /*sql*/ `
                AND caf.opNrPlantao = ?
            `;

            params.push(event.opNrPlantao);
        }

        if (event.search) {
            filter += /*sql*/ `
                AND (
                    caf.afNome LIKE ?
                    OR caf.ocNrContrato LIKE ?
                    OR caf.opNrPlantao LIKE ?
                    OR caf.codFechamento LIKE ?
                )
            `;

            params.push(`%${event.search}%`);
            params.push(`%${event.search}%`);
            params.push(`%${event.search}%`);
            params.push(`%${event.search}%`);
        }

        // Pagination parameters
        const page = Number(event.page) || 1;
        const pageSize = Number(event.pageSize) || 25;
        const offset = (page - 1) * pageSize;

        // Prepare multi query combining count and data queries
        const multiQuery = /*sql*/ `
            SELECT COUNT(DISTINCT caf.codFechamento) as total
            FROM capAtendimentoFechamento caf
            INNER JOIN capOperPlantaoCheck copc ON (
                copc.codFechamento = caf.codFechamento
                AND caf.isInstSaude = copc.isInstSaude
                AND caf.laNome = copc.laNome
                AND caf.esEspecialidade = copc.esEspecialidade
                AND caf.ocNrContrato = copc.ocNrContrato
                AND caf.opNrPlantao = copc.opNrPlantao
            )
            WHERE caf.clCliente = ?
            ${filter};

            SELECT
                CONCAT(
                    FLOOR(SUM(TIME_TO_SEC(ocQtAprovadas)) / 3600), 
                    ':', 
                    LPAD(MOD(FLOOR(SUM(TIME_TO_SEC(ocQtAprovadas)) / 60), 60), 2, '0')
                ) AS totalHoras,
                caf.*
            FROM capAtendimentoFechamento caf
            INNER JOIN capOperPlantaoCheck copc ON (
                copc.codFechamento = caf.codFechamento
                AND caf.isInstSaude = copc.isInstSaude
                AND caf.laNome = copc.laNome
                AND caf.esEspecialidade = copc.esEspecialidade
                AND caf.ocNrContrato = copc.ocNrContrato
                AND caf.opNrPlantao = copc.opNrPlantao
            )
            WHERE caf.clCliente = ?
            ${filter}
            GROUP BY caf.codFechamento
            ORDER BY caf.codFechamento ASC
            LIMIT ?
            OFFSET ?;
        `;

        // Duplicate base parameters for both queries and add pagination for the second
        const multiParams = [...params, ...params, pageSize, offset];

        // Execute multi query; result[0] is count, result[1] is data
        const result = await dbQuery(multiQuery, multiParams);
        const total = result[0][0].total;
        const totalPages = Math.ceil(total / pageSize);
        const nextPage = page < totalPages ? page + 1 : null;
        const response = result[1];

        if (response?.length > 0) {
            return baseResponse.paginated('Listado com sucesso!', {
                totalPages,
                currentPage: page,
                nextPage,
                data: response,
            });
        }

        return baseResponse.ok('Nenhum registro encontrado', []);
    } catch (error) {
        console.log('ERROR _listFechamentos', error);
        return baseResponse.error('Erro ao processar requisição');
    }
}
