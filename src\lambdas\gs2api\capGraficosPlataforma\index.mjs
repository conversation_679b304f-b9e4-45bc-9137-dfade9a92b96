import { getUserFromToken, baseResponse, UserGroupTypes } from 'capfunctions';

import {
  _opDadosCompletos,
  _clDadosCompletos,
  _gcDadosCompletos,
  _gcDadosMensais,
} from './functions.mjs';

const actions = { _opDadosCompletos, _clDadosCompletos, _gcDadosCompletos, _gcDadosMensais };

export const handler = async (event) => {
  try {
    console.log('event ', event);

    const userFromToken = await getUserFromToken(event.headers?.Authorization);

    const groupsAllowed = [
      UserGroupTypes.GESTOR_CAPITALE,
      UserGroupTypes.GESTOR_CLIENTE,
      UserGroupTypes.ESCALISTA,
    ];
    const isAllowed = groupsAllowed.some((group) => userFromToken?.body.groups.includes(group));

    if (!isAllowed) {
      return baseResponse.unauthorized('Usuário sem as permissões necessárias.');
    }

    if (!!userFromToken) {
      if (!(event.body?.method in actions || event.method in actions)) {
        return baseResponse.notFound('Método não encontrado');
      }

      return {
        newToken: userFromToken?.body?.newToken,
        ...(await actions[event.body?.method || event.method]({
          ...(event.body || event),
          ...userFromToken,
        })),
      };
    }

    return baseResponse.unauthorized('Token de sessão expirado, por favor, faça login novamente');
  } catch (error) {
    console.log('Erro ao processar a requisição', error);

    return baseResponse.error(
      'Erro ao processar a requisição. CATCH HANDLER capGraficosPlataforma'
    );
  }
};
