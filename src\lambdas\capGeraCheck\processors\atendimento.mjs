import { update } from 'capfunctions';

import { AgendaRepository } from '../repositories/agenda.mjs';
import { AtendimentoRepository } from '../repositories/atendimento.mjs';
import { CheckRepository } from '../repositories/checkin.mjs';
import { CheckProcessor } from './checkin.mjs';

export class AtendimentoProcessor {
  /**
   * @param {AgendaRepository} agendaRepository
   * @param {CheckProcessor} checkProcessor
   * @param {CheckRepository} checkRepository
   * @param {AtendimentoRepository} atendimentoRepository
   */
  constructor(agendaRepository, checkProcessor, checkRepository, atendimentoRepository) {
    this.agendaRepository = agendaRepository;
    this.checkProcessor = checkProcessor;
    this.checkRepository = checkRepository;
    this.atendimentoRepository = atendimentoRepository;
  }

  async process(atendimento) {
    const event = {
      body: {
        newToken: 'system',
        userName: 'system',
      },
    };

    const agendas = await this.agendaRepository.getAgendasForAtendimento(atendimento);
    if (!agendas || agendas.length === 0) {
      return;
    }

    // Atualiza o status se estiver aguardando execução
    if (atendimento.opSituacao === 'AguardExecucao') {
      await update(
        'capInstSaudePlantao',
        { opSituacao: 'EmExecucao' },
        {
          opNrPlantao: atendimento.opNrPlantao,
          isInstSaude: atendimento.isInstSaude,
          laNome: atendimento.laNome,
          esEspecialidade: atendimento.esEspecialidade,
          ocNrContrato: atendimento.ocNrContrato,
          clCliente: atendimento.clCliente,
          ceTipoPagamento: atendimento.ceTipoPagamento,
        },
        event
      );
    }

    const checkKey = {
      isInstSaude: atendimento.isInstSaude,
      laNome: atendimento.laNome,
      esEspecialidade: atendimento.esEspecialidade,
      ocNrContrato: atendimento.ocNrContrato,
      clCliente: atendimento.clCliente,
      ceTipoPagamento: atendimento.ceTipoPagamento,
      opNrPlantao: atendimento.opNrPlantao,
    };

    // Para cada agenda do atendimento, processa o registro de check (único por agenda)
    for (const agenda of agendas) {
      await this.checkProcessor.processAgenda(atendimento, agenda, checkKey, event);
    }

    // Atualiza os checks aprovados (em massa) e encaminha para fechamento
    await this.checkRepository.updateApprovedChecks(atendimento);
  }
}
