{"id": null, "tenantId": "473680", "name": "GS2 - Adesão aos Termos - Médicos", "created": null, "flowNodes": [{"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p><strong>@NomeCompleto</strong>, por favor, confirme se está de acordo com os <strong>Termos de Condições Gerais</strong> compartilhados.</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "PKQvVFw", "buttonText": "✅ Sim, de acordo", "nodeResultId": "main_message-yghZN"}, {"id": "lpfBXlC", "buttonText": "❌ Não, obrigado", "nodeResultId": "main_buttons-IFeUG"}, {"id": "aOcXgss", "buttonText": "ℹ️ Quero saber mais", "nodeResultId": "main_invokeFlow-BrXcU"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-LcPIt", "id": "main_buttons-GLxWf", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "1002", "posY": "280"}, "isStartNode": true}, {"flowNodeConditions": [{"id": "SPuZYHU", "flowConditionType": "NotEqual", "variable": "RespostaLivre", "value": "<PERSON><PERSON>"}], "conditionResult": {"yResultNodeId": "main_buttons-IFeUG", "nResultNodeId": "main_invokeFlow-exiAY"}, "conditionOperator": "None", "id": "main_condition-LcPIt", "flowNodeType": "Condition", "flowNodePosition": {"posX": "1643", "posY": "904"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-BrXcU", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "2520", "posY": "471"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-kEBlR", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "2333", "posY": "-143"}, "isStartNode": false}, {"interactiveButtonsHeader": {"type": "Text", "text": "", "media": null}, "interactiveButtonsBody": "<p>O que gostaria de fazer agora?</p>", "interactiveButtonsFooter": "", "interactiveButtonsItems": [{"id": "rXOHutm", "buttonText": "↩️ Aceitar os termos", "nodeResultId": "main_buttons-GLxWf"}, {"id": "XcMhNKr", "buttonText": "ℹ️ Quero saber mais", "nodeResultId": "main_invokeFlow-BrXcU"}, {"id": "jBljWNL", "buttonText": "❌ Fechar a conversa", "nodeResultId": "main_invokeFlow-exiAY"}], "interactiveButtonsUserInputVariable": "RespostaLivre", "interactiveButtonsDefaultNodeResultId": "main_condition-LcPIt", "id": "main_buttons-IFeUG", "flowNodeType": "InteractiveButtons", "flowNodePosition": {"posX": "2040", "posY": "126"}, "isStartNode": false}, {"newFlowId": "", "id": "main_invokeFlow-exiAY", "flowNodeType": "InvokeFlow", "flowNodePosition": {"posX": "2600", "posY": "922"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p><strong>🎉 </strong>Obrigado! Em breve entraremos em contato com mais informações e próximos passos.</p>", "caption": "", "mimeType": ""}], "id": "main_message-yghZN", "flowNodeType": "Message", "flowNodePosition": {"posX": "1447", "posY": "-230"}, "isStartNode": false}, {"flowReplies": [{"flowReplyType": "Text", "data": "<p>Até logo!</p>", "caption": "", "mimeType": ""}], "id": "main_message-VVnWF", "flowNodeType": "Message", "flowNodePosition": {"posX": "1868", "posY": "-212"}, "isStartNode": false}], "flowEdges": [{"id": "reactflow__edge-main_buttons-GLxWfaOcXgss-main_invokeFlow-BrXcU", "sourceNodeId": "main_buttons-GLxWf__aOcXgss", "targetNodeId": "main_invokeFlow-BrXcU"}, {"id": "reactflow__edge-main_buttons-GLxWfmain_buttons-GLxWf-default-main_condition-LcPIt", "sourceNodeId": "main_buttons-GLxWf__main_buttons-GLxWf-default", "targetNodeId": "main_condition-LcPIt"}, {"id": "reactflow__edge-main_buttons-GLxWflpfBXlC-main_buttons-IFeUG", "sourceNodeId": "main_buttons-GLxWf__lpfBXlC", "targetNodeId": "main_buttons-IFeUG"}, {"id": "reactflow__edge-main_buttons-IFeUGmain_buttons-IFeUG-default-main_condition-LcPIt", "sourceNodeId": "main_buttons-IFeUG__main_buttons-IFeUG-default", "targetNodeId": "main_condition-LcPIt"}, {"id": "reactflow__edge-main_buttons-IFeUGXcMhNKr-main_invokeFlow-BrXcU", "sourceNodeId": "main_buttons-IFeUG__XcMhNKr", "targetNodeId": "main_invokeFlow-BrXcU"}, {"id": "reactflow__edge-main_buttons-IFeUGrXOHutm-main_buttons-GLxWf", "sourceNodeId": "main_buttons-IFeUG__rXOHutm", "targetNodeId": "main_buttons-GLxWf"}, {"id": "reactflow__edge-main_condition-LcPIttrue-main_buttons-IFeUG", "sourceNodeId": "main_condition-LcPIt__true", "targetNodeId": "main_buttons-IFeUG"}, {"id": "reactflow__edge-main_buttons-IFeUGjBljWNL-main_invokeFlow-exiAY", "sourceNodeId": "main_buttons-IFeUG__jBljWNL", "targetNodeId": "main_invokeFlow-exiAY"}, {"id": "reactflow__edge-main_condition-LcPItfalse-main_invokeFlow-exiAY", "sourceNodeId": "main_condition-LcPIt__false", "targetNodeId": "main_invokeFlow-exiAY"}, {"id": "reactflow__edge-main_buttons-GLxWfPKQvVFw-main_message-yghZN", "sourceNodeId": "main_buttons-GLxWf__PKQvVFw", "targetNodeId": "main_message-yghZN"}, {"id": "reactflow__edge-main_message-yghZN-main_message-VVnWF", "sourceNodeId": "main_message-yghZN", "targetNodeId": "main_message-VVnWF"}, {"id": "reactflow__edge-main_message-VVnWF-main_invokeFlow-kEBlR", "sourceNodeId": "main_message-VVnWF", "targetNodeId": "main_invokeFlow-kEBlR"}], "lastUpdated": "2025-07-28T02:44:26.372Z", "isDeleted": false, "transform": {"posX": "-189.55592525087297", "posY": "192.2174988886124", "zoom": "0.5"}, "isPro": true, "channelTypes": ["WA"]}