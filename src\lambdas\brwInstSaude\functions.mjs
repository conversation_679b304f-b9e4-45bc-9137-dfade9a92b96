import {
  createResponse,
  checkData,
  execQuery,
  baseResponse,
  dbQuery,
  insert,
  update,
  deleteEntity,
  onlyNumber,
  isEmailValid,
  lambdaInvoke,
} from 'capfunctions';

async function _list(event) {
  console.log(event);
  try {
    const isMaster = event.body?.groups?.find((group) => group === 'gestorcliente');

    let complement = /*sql*/`
      FROM
        capInstSaude a
      WHERE a.isNome LIKE ?
    `;
    const params = ['%' + (event['searchName'] || '') + '%', event.clCliente, event.body.user];

    if (!isMaster) {
      complement = /*sql*/ `
          FROM
            capInstSaude a
            , capInstSaudeContrato b
            , capClienteUsuario c
          WHERE a.isNome LIKE ?
          AND a.isInstSaude = b.isInstSaude
          AND b.clCliente = ?
          AND b.clCliente = c.clCliente
          AND c.usCPFUsuario = ?
      `;
      params.push(event.clCliente);
      params.push(event.body.user);
    }

    const query = /*sql*/ `
      SELECT
        a.isInstSaude
        , a.isNome
        , a.isEmail
        , a.isCEP
        , a.isEndereco
        , a.isNrEnd
        , a.isComplEnd
        , a.isBairro
        , a.isCidade
        , a.isUF
        , a.isCodANS
        , a.isAtivo
        , a.dtInclusao
        , a.dtModificacao
      ${complement}
      GROUP BY a.isInstSaude;
    `;
    
    const response = await dbQuery(query, params);

    return baseResponse.ok('', response);
  } catch (error) {
    console.log('ERROR _list', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _find(event) {
  try {
    if (checkData(event, ['clCliente'])) {
      return baseResponse.error('Dados insuficentes');
    }

    const query = `SELECT
                  isInstSaude, isNome, isEmail, isCEP, isEndereco, isNrEnd, isComplEnd, isBairro, isCidade, isUF, isCodANS, isAtivo, dtInclusao, dtModificacao
                  FROM capInstSaude a
                  WHERE a.isInstSaude = ?
                  `;

    const params = [event['isInstSaude'] || ''];
    const response = await execQuery(query, params);

    if (response && response.results && response.results.length > 0) {
      return baseResponse.created('Listado com sucesso', response.results && response.results[0]);
    }

    return baseResponse.error('Não foi possível encontrar a instituição de saúde');
  } catch (error) {
    console.log('ERROR _find', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _insert(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'isNome',
          'isEmail',
          'isCEP',
          'isEndereco',
          'isNrEnd',
          'isBairro',
          'isCidade',
          'isUF',
          'isCodANS',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    if (!isEmailValid(event.isEmail)) {
      //return createResponse(400, 'Formato de email inválido.', '', null);
    }

    const entity = {
      isInstSaude: event.isInstSaude,
      isNome: event.isNome,
      isEmail: event.isEmail,
      isCEP: event.isCEP,
      isEndereco: event.isEndereco,
      isNrEnd: event.isNrEnd,
      isComplEnd: event.isComplEnd || '',
      isBairro: event.isBairro,
      isCidade: event.isCidade,
      isUF: event.isUF,
      isCodANS: event.isCodANS,
    };

    const response = await insert('capInstSaude', entity, event);

    if (response.success) {
      return baseResponse.created('Criado com successo');
    }

    return baseResponse.notFound('Nada encontrado');
  } catch (error) {
    console.log('ERROR _insert', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _update(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'isNome',
          'isEmail',
          'isCEP',
          'isEndereco',
          'isNrEnd',
          'isBairro',
          'isCidade',
          'isUF',
          'isCodANS',
          'isAtivo',
        ],
      })
    ) {
      return baseResponse.error('Dados insuficentes');
    }

    const entity = {
      isNome: event.isNome,
      isEmail: event.isEmail,
      isCEP: event.isCEP,
      isEndereco: event.isEndereco,
      isNrEnd: event.isNrEnd,
      isComplEnd: event.isComplEnd || '',
      isBairro: event.isBairro,
      isCidade: event.isCidade,
      isUF: event.isUF,
      isCodANS: event.isCodANS,
      isAtivo: event.isAtivo,
    };
    //      dtModificacao: 'current_timestamp' truncando

    const response = await update(
      'capInstSaude',
      entity,
      {
        isInstSaude: event.isInstSaude,
      },
      event
    );

    if (response.success) {
      return baseResponse.created('Atualizado', response.results);
    }
    console.info(response.success);

    return baseResponse.error('Erro ao atualizar instituição de saúde');
  } catch (error) {
    console.log('ERROR _update', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _listlocais(event) {
  console.log(event);
  try {
    const query = `SELECT a.isInstSaude, a.laNome, a.laDescricao, a.laCEP, a.laEndereco, a.laNrEnd, a.laComplEnd, a.laBairro, a.laCidade, a.laUF, a.laLat, a.laLong
                   FROM capInstSaudeLocalAtendimento a
                   WHERE a.isInstSaude = ?
                  `;
    const params = [event['isInstSaude'] || ''];
    const response = await execQuery(query, params);
    if (response.success) {
      if (response.results[0].especialidades)
        response.results[0].especialidades = JSON.parse(response.results[0].especialidades);

      return baseResponse.created('Listado com sucesso', response.results);
    }
    return baseResponse.error('Erro ao listar locais');
  } catch (error) {
    console.log('ERROR _', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _findlocaisesp(event) {
  console.log(event);
  try {
    const query = `SELECT a.isInstSaude, a.laNome, a.laDescricao, a.laCEP, a.laEndereco, a.laNrEnd, a.laComplEnd, a.laBairro, a.laCidade, a.laUF, b.esEspecialidade,
                    json_arrayagg(b.esEspecialidade) as especialidades,
                    a.laLat, a.laLong
                    FROM capInstSaudeLocalAtendimento a, capInstSaudeEspecialidade b
                    WHERE a.isInstSaude = ?
                    and   a.isInstSaude = b.isInstSaude
                    and   a.laNome = ?
                    and   a.laNome = b.laNome
                  `;
    const params = [event.isInstSaude, event.laNome];
    const response = await execQuery(query, params);
    if (response.success) {
      if (response.results[0].especialidades)
        response.results[0].especialidades = JSON.parse(response.results[0].especialidades);

      return baseResponse.created('Listado com sucesso', response.results);
    }
    return baseResponse.error('Erro ao buscar locais');
  } catch (error) {
    console.log('ERROR _findlocaisesp', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _insertlocal(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'laDescricao',
          'laCEP',
          'laEndereco',
          'laNrEnd',
          'laBairro',
          'laCidade',
          'laUF',
          'laLat',
          'laLong',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const entity = {
      isInstSaude: event.isInstSaude,
      laNome: event.laNome,
      laEndereco: event.laEndereco,
      laNrEnd: event.laNrEnd,
      laComplEnd: event.laComplEnd,
      laBairro: event.laBairro,
      laCidade: event.laCidade,
      laUF: event.laUF,
      laDescricao: event.laDescricao,
      laCEP: event.laCEP,
      laLat: event.laLat,
      laLong: event.laLong,
    };

    const response = await insert('capInstSaudeLocalAtendimento', entity, event);

    if (response.success) {
      if (event.especialidades) {
        const especs = event.especialidades;
        let respesp, entesp;
        for (const espec of especs) {
          entesp = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: espec,
          };
          respesp = await insert('capInstSaudeEspecialidade', entesp, event);
        }
      }
      return baseResponse.created('Local criado com sucesso');
    }

    return baseResponse.notFound('Não foi possível criar o local');
  } catch (error) {
    console.log('ERROR _insertlocal', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

async function _updatelocal(event) {
  try {
    if (
      checkData(event, {
        obrigatorios: [
          'isInstSaude',
          'laNome',
          'laDescricao',
          'laCEP',
          'laEndereco',
          'laNrEnd',
          'laBairro',
          'laCidade',
          'laUF',
          'laLat',
          'laLong',
        ],
      })
    ) {
      return baseResponse.badRequest('Dados insuficentes.');
    }

    const entity = {
      isInstSaude: event.isInstSaude,
      laEndereco: event.laEndereco,
      laNrEnd: event.laNrEnd,
      laComplEnd: event.laComplEnd,
      laBairro: event.laBairro,
      laCidade: event.laCidade,
      laUF: event.laUF,
      laDescricao: event.laDescricao,
      laCEP: event.laCEP,
      laLat: event.laLat,
      laLong: event.laLong,
    };

    const response = await update(
      'capInstSaudeLocalAtendimento',
      entity,
      {
        isInstSaude: event.isInstSaude,
        laNome: event.laNome,
      },
      event
    );

    if (response.success) {
      if (event.especialidades) {
        const especs = event.especialidades;
        var respesp, entesp;

        const especialidadesQuery = `SELECT * from capInstSaudeEspecialidade WHERE isInstSaude = "${event.isInstSaude}" AND laNome = "${event.laNome}"`;
        const responseEspecialidadesQuery = await execQuery(especialidadesQuery);

        if (
          responseEspecialidadesQuery.success &&
          responseEspecialidadesQuery?.results?.length > 0
        ) {
          const deletePromises = [];
          for (const especialidade of responseEspecialidadesQuery?.results) {
            const deleteQuery = `
            DELETE from capInstSaudeEspecialidade 
            WHERE 
              isInstSaude = "${especialidade.isInstSaude}" 
              AND laNome = "${especialidade.laNome}" 
              AND esEspecialidade = "${especialidade.esEspecialidade}"`;

            deletePromises.push(execQuery(deleteQuery));
          }

          await Promise.all(deletePromises);
        }

        const updates = [];

        for (const espec of especs) {
          entesp = {
            isInstSaude: event.isInstSaude,
            laNome: event.laNome,
            esEspecialidade: espec,
          };
          updates.push(insert('capInstSaudeEspecialidade', entesp, event));
        }

        await Promise.all(updates);
      }
      return baseResponse.created('Local atualizado com sucesso', response.results);
    }
    console.info(response.success);

    return baseResponse.error('Erro ao atualizar local');
  } catch (error) {
    console.log('ERROR _updatelocal', error);
    return baseResponse.error('Erro ao processar requisição');
  }
}

export { _insert, _update, _list, _find, _listlocais, _insertlocal, _updatelocal, _findlocaisesp };
