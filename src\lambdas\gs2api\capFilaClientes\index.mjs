import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';

import { dbQuery } from 'capfunctions';

const sqsClient = new SQSClient({ region: 'us-east-2' });

const enviaFila = async (clCliente, tipoAtendimento) => {
  const queueUrl =
    'https://sqs.us-east-2.amazonaws.com/730335345607/capFilaClienteAtendimento.fifo';

  const timestamp = new Date().getTime();
  const deduplicationId = `${clCliente}-${tipoAtendimento}-${timestamp}`;

  const params = {
    QueueUrl: queueUrl,
    MessageBody: JSON.stringify({
      clCliente,
      tipoAtendimento,
    }),
    MessageGroupId: 'capFilaClientes',
    MessageDeduplicationId: deduplicationId,
  };

  const command = new SendMessageCommand(params);
  const response = await sqsClient.send(command);

  console.log({ clCliente, tipoAtendimento, MessageId: response.MessageId });
};

export const handler = async (event) => {
  try {
    console.log(event);

    const clientes = [{ clCliente: 26324097000181 }];
    // const clientes = await dbQuery(/*sql*/ `
    //   SELECT clCliente FROM capCliente
    // `);

    for (const cliente of clientes) {
      const { clCliente } = cliente;

      await enviaFila(clCliente, 'Diario');
      await enviaFila(clCliente, 'Semanal');
      await enviaFila(clCliente, 'Mensal');
    }
  } catch (error) {
    console.error('Error:', error);
  }
};
