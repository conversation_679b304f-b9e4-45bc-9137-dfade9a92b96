import axios from 'axios';

const WHATSAPP_TOKEN = process.env.WHATSAPP_TOKEN;
const PHONE_NUMBER_ID = process.env.PHONE_NUMBER_ID;

export const handler = async (event) => {
  try {
    console.log('evento: ', event);
    const { phone, saudacao, dr, nome, agenda, local, periodo, endereco } = event.body;
    const response = await axios.post(
      `https://graph.facebook.com/v22.0/${PHONE_NUMBER_ID}/messages`,
      {
        messaging_product: 'whatsapp',
        to: phone,
        type: 'template',
        template: {
          name: 'conf_agenda',
          language: { code: 'pt_BR' },
          components: [
            {
              type: 'body',
              parameters: [
                { type: 'text', text: saudacao },
                { type: 'text', text: dr },
                { type: 'text', text: nome },
                { type: 'text', text: agenda },
                { type: 'text', text: local },
                { type: 'text', text: periodo },
                { type: 'text', text: endereco },
              ],
            },
          ],
        },
      },
      {
        headers: {
          Authorization: `Bearer ${WHATSAPP_TOKEN}`,
          'Content-Type': 'application/json',
        },
      }
    );
    console.log('response', response);
    return {
      statusCode: 200,
      body: JSON.stringify({ success: true, message: 'Mensagem enviada!', data: response.data }),
    };
  } catch (error) {
    console.error('Erro ao enviar mensagem', error.response?.data || error.message);
    return {
      statusCode: 500,
      body: JSON.stringify({ success: false, error: error.response?.data || error.message }),
    };
  }
};
