import mysql from "mysql";

const connOptions = {
  host: process.env.host,
  user: process.env.user,
  password: process.env.senha,
  database: process.env.database,
  multipleStatements: true,
};

const connection = mysql.createConnection(connOptions);

export async function execQuery(query, params = []) {
  console.log("execQuery => ", { query, params });
  return new Promise(function (resolve, reject) {
    connection.query(query, params, function (err, response) {
      if (err) {
        console.log(`Erro na query ${query}`, `erro: ${err}`);
        reject(err);
      } else {
//      console.log(`Resultado da query `, `results: ${response}`);
        resolve(response);
      }
    });
  });
}

