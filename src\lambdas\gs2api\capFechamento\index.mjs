import { baseResponse, getUser<PERSON>romToken, UserGroupTypes } from 'capfunctions';
import {
    _fechamentoParcial,
    _listFechamentos,
    _aprovaFechamentos,
    _antecipaAtendimento,
    _preVisualizacaoAntecipacao,
    _visualizarValores,
} from './functions.mjs';
import { _bucarRelatorio, _bucarFiltroRelatorio } from './relatorios.mjs';
import { _listFechamentosProfissional } from './methods/listFechamentosProfissional.mjs';
import { _valoresFechamentoProfissional } from './methods/valoresFechamentoProfissional.mjs';

const allowedMethods = [
    '_listFechamentosProfissional',
    '_valoresFechamentoProfissional',
    '_antecipaAtendimento',
];

const actions = {
    _fechamentoParcial,
    _listFechamentos,
    _aprovaFechamentos,
    _antecipaAtendimento,
    _preVisualizacaoAntecipacao,
    _visualizarValo<PERSON>,
    _bucarRelatorio,
    _listFechamentosProfissional,
    _valoresFechamentoProfissional,
    _bucarFiltroRelatorio
};

export const handler = async (event) => {
    try {
        console.log(event);

        if (isSqsEvent(event)) {
            return await processaMensagensDaFila(event);
        } else if (isHttpEvent(event)) {
            return await processaRequisiçãoHttp(event);
        } else {
            return baseResponse.error('Tipo de evento não suportado');
        }
    } catch (err) {
        console.log(err);
        return baseResponse.error('Erro ao processar a requisição. CATCH HANDLER capFechamento');
    }
};

const isSqsEvent = (event) => {
    return event?.Records && event.Records[0].eventSource === 'aws:sqs';
};

const isHttpEvent = (event) => {
    return event?.httpMethod || event?.headers;
};

const processaMensagensDaFila = async (event) => {
    try {
        const processamento = [];

        for (const record of event?.Records) {
            const recordData = JSON.parse(record.body);

            for (const body of recordData) {
                console.log(body);
                processamento.push(_fechamentoParcial(body));
            }
        }

        await Promise.all(processamento);

        return { success: true };
    } catch (err) {
        console.log(err);
        return { success: false };
    }
};

const processaRequisiçãoHttp = async (event) => {
    try {
        const userFromToken = await getUserFromToken(event.headers?.Authorization);

        if (!userFromToken) {
            return baseResponse.unauthorized(
                'Token de sessão expirado, por favor, faça login novamente'
            );
        }

        const method = event.body?.method || event.method;

        const groupsAllowed = [UserGroupTypes.GESTOR_CAPITALE, UserGroupTypes.GESTOR_CLIENTE];
        const isAllowed = groupsAllowed.some((group) => userFromToken?.body.groups.includes(group));

        if (!isAllowed && !allowedMethods.includes(method)) {
            return baseResponse.unauthorized('Usuário sem as permissões necessárias.');
        }

        if (!(event.body?.method in actions || event.method in actions)) {
            return baseResponse.notFound('Método não encontrado');
        }

        return {
            newToken: userFromToken?.body?.newToken,
            ...(await actions[event.body?.method || event.method]({
                ...(event.body || event),
                ...userFromToken,
            })),
        };
    } catch (error) {
        console.log('Erro ao processar a requisição', error);

        return baseResponse.error('Erro ao processar a requisição. CATCH HANDLER capFechamento');
    }
};
