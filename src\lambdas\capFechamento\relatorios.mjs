import { baseResponse, checkData, dbQuery } from 'capfunctions';

export const fechamentoSituacao = {
  processandoAntecipacao: 'Antecipação em Processamento',
  aguardandoAprovacao: 'Aguardadando Aprovação',
  aprovado: 'Aprovado',
  antecipacaoSolicitada: 'Antecipação Solicitada',
  antecipado: 'Antecipado',
};

export async function _bucarRelatorio(event) {
  if (
    checkData(event, {
      obrigatorios: ['dataInicio', 'dataFim', 'profissional', 'situacoes', 'clCliente'],
    })
  ) {
    return baseResponse.badRequest('Dados insuficentes.');
  }

  const response = await dbQuery(
    /*sql*/ `
        SELECT
            a.*,
            b.usNome
        FROM
            capAtendimentoFechamento a,
            capUsuario b
        WHERE a.clCliente = ? 
        AND a.psCPF = b.usCPFUsuario
        AND DATE(a.adDataSolicitacao) BETWEEN ? AND ?
        AND a.psCPF IN (?)
        AND a.adSituacao IN (?);
    `,
    [event.clCliente, event.dataInicio, event.dataFim, event.profissional, event.situacoes]
  );

  return baseResponse.ok('', response);
}

export async function _bucarFiltroRelatorio(event) {
  const response = await dbQuery(
    /*sql*/ `
        SELECT
            adSituacao
        FROM
            capAtendimentoFechamento a
        WHERE a.clCliente = ?
        GROUP BY adSituacao
        ORDER BY adSituacao;
    `,
    [event.clCliente, event.dataInicio, event.dataFim, event.profissional, event.situacoes]
  );

  return baseResponse.ok(
    '',
    response.map((item) => ({
      label: fechamentoSituacao[item.adSituacao] || item.adSituacao,
      value: item.adSituacao,
    }))
  );
}
