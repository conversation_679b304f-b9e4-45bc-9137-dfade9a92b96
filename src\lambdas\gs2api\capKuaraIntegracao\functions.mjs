import { dbQ<PERSON>y } from 'capfunctions';

class CapitalService {
    #baseURL = '';
    #email = '';
    #password = '';
    #token = '';

    constructor() {
        this.#baseURL = process.env.CAPITAL_API_BASE_URL;
        this.#email = process.env.CAPITAL_API_EMAIL;
        this.#password = process.env.CAPITAL_API_PASSWORD;
    }

    getToken = () => this.#getToken();

    #getToken = async () => {
        if (this.#token) {
            return this.#token;
        }

        const result = await fetch(`${this.#baseURL}/user/auth`, {
            method: 'POST',
            body: JSON.stringify({
                email: this.#email,
                password: this.#password,
            }),
            headers: {
                'Content-Type': 'application/json',
                Accept: 'application/json',
            },
        });

        const json = await result.json();

        if (json.token) {
            this.#token = json.token;

            return this.#token;
        }

        return null;
    };

    #request = async (endpoint, data, method) => {
        try {
            const url = `${this.#baseURL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;

            const Authorization = `Bearer ${await this.#getToken()}`;
            const init = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    Accept: 'application/json',
                    Authorization,
                },
            };

            if (['POST', 'PUT'].includes(method) && data) {
                console.log(`Método ${method} para: ` + url, JSON.stringify(data));
                init.body = JSON.stringify(data);
            }

            const result = await fetch(url, init);

            const json = await result.json();

            console.log(
                `[Método ${method}] - JSON retornado do envio de dados para: ` + url,
                JSON.stringify(json)
            );

            return json;
        } catch (error) {
            console.log(`[CapitalService.${method}] :: `, { endpoint, data, method }, error);

            return null;
        }
    };

    get = (endpoint) => this.#request(endpoint, null, 'GET');
    post = (endpoint, data) => this.#request(endpoint, data, 'POST');

    cadastrarPessoaFisica = async (dados) => {
        const bankAccounts = [];

        if (
            dados.codigo_banco &&
            dados.conta_bancaria &&
            dados.agencia &&
            dados.agencia_digito &&
            dados.conta_digito
        ) {
            bankAccounts.push({
                bankCode: dados.codigo_banco,
                account: dados.conta_bancaria,
                accountDigit: dados.conta_digito,
                agency: dados.agencia,
                agencyDigit: dados.agencia_digito,
                operationTypeValue: 'Transfer',
                type: 'NaturalCheckingAccount',
            });
        }

        const pix = {
            CPF: 'NaturalRegistrationNumber',
            CNPJ: 'LegalRegistrationNumber',
            Email: 'Email',
            Celular: 'Phone',
            Aleatório: 'Automatic',
        };

        if (dados.chave_pix && dados.tipo_chave_pix) {
            const pixKeyTypeValue = pix[dados.tipo_chave_pix];

            bankAccounts.push({
                keyPix: dados.chave_pix,
                pixKeyTypeValue,
                operationTypeValue: 'Pix',
            });
        }

        const payload = {
            cpf: dados.cpf,
            phone: dados.telefone,
            email: dados.email,
            name: dados.nome,
            nationality: dados.nacionalidade,
            address: {
                addressName: dados.logradouro,
                zipCode: dados.cep,
                city: dados.cidade,
                uf: dados.estado,
                district: dados.bairro,
                number: dados.numero_endereco,
                complement: dados.complemento,
            },
            birthDate: dados.data_nascimento,
            civilStatus: dados?.estado_civil ?? null,
            gender: dados?.genero ?? null,

            bankAccounts,
        };

        if (!dados.estado_civil) {
            delete payload.civilStatus;
        }

        if (!dados.genero) {
            delete payload.gender;
        } else {
            payload.gender = dados.genero === 'F' ? 'Female' : 'Male';
        }

        const result = await this.post('person/natural', [payload]);

        return result;
    };

    cadastrarPessoaJuridica = async (dados) => {
        const result = await this.post('person/legal', [
            {
                cnpj: dados.cnpj,
                companyName: dados.razao_social,
                name: dados.nome_fantasia,
                email: dados.email,
                phone: dados.telefone,
                address: {
                    addressName: dados.endereco,
                    zipCode: dados.cep,
                    city: dados.cidade,
                    uf: dados.uf,
                    district: dados.bairro,
                    number: dados.numero,
                    complement: dados?.complemento ?? null,
                },

                bankAccounts: [],
            },
        ]);

        return result;
    };

    solicitaAntecipacao = async (dados) => {
        /*
            {
                "personCPF": "***********",
                "companyCNPJ": "**************",
                "requestedAmount": 550,
                "startDate": "2022-12-31",
                "firstPaymentDate": "2025-02-18", //Deve ser 31 dias após startDate (dia de hoje)
                "numberOfInterestPayments": 0,  // Juros
                "numberOfPayments": 1,
                "apr": 0.06, // 6% lembrar de taca o zero
                "paymentDay": 10,
                "operationTypeValue": "PIX"
            }
        */

        if (dados.diaPrimeiroPagamento <= 31) {
            dados.diaPrimeiroPagamento = 31;
        }

        const [{ paymentDay, startDate, firstPaymentDate }] = await dbQuery(
            /*sql*/ `
            SELECT DATE_FORMAT(NOW(), '%d') AS paymentDay,
            DATE_FORMAT(NOW(), '%Y-%m-%d') AS startDate,
            DATE_FORMAT(NOW() + INTERVAL ? DAY, '%Y-%m-%d') AS firstPaymentDate;
        `,
            [dados.diaPrimeiroPagamento]
        );

        const payload = {
            startDate,
            numberOfPayments: 1,
            companyCNPJ: dados.cnpj, // CNPJ da empresa
            personCPF: dados.cpf, // CPF do solicitante
            apr: dados.taxa, // Taxa de juros
            requestedAmount: parseFloat(dados.valor), // Valor solicitado cheio / deve ser igual ou maio que 5000.01
            paymentDay: Number(paymentDay),
            firstPaymentDate,
            numberOfInterestPayments: 0, // Número de pagamentos
            operationTypeValue: 'Transfer', // Tipo de operação (Transfer | Pix)
            paymentPeriodicity: 'Monthly', // Periodicidade do pagamento
        };

        /*
            [
                {
                    "id": "2c289a16-97b8-46a2-9c3c-8b2cf18ca1e7",
                    "naturalPersonId": "63d51355-6dcd-4147-b916-30125958fd21",
                    "legalPersonId": "bb9dba25-98dd-44d2-a176-5d9a9a3460a8",
                    "requestedAmount": 12600.0,
                    "status": "RASCUNHO",
                    "operationTypeValue": "Transfer",
                    "firstPaymentDate": "2025-03-22",
                    "numberOfInterestPayments": 0,
                    "numberOfPayments": 1,
                    "apr": 0.06,
                    "paymentDay": 19,
                    "startDate": "2025-02-19",
                    "paymentPeriodicity": null,
                    "created_at": "2025-02-27T13:50:17",
                    "updated_at": "2025-02-27T13:50:17",
                    "is_active": true
                }
            ]
        */
        const result = await this.post('credit/', [payload]);

        if (result?.error) {
            console.log('Erro ao solicitar antecipação', result);

            return null;
        }

        const body = result[0];

        await dbQuery(
            /*sql*/ `
            UPDATE
                capAntecipacao
            SET
                anKuaraDataPagamento = ?,
                anKuaraId = ?,
                anKuaraStatus = ?
            WHERE
                anNrAntecipacao = ?;
            `,
            [firstPaymentDate, body.id, body.status, dados.anNrAntecipacao]
        );

        return result;
    };
}

const capitalService = new CapitalService();

export { capitalService };
